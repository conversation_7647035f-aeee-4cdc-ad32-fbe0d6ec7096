import { useState, useEffect, useRef, useCallback } from 'react';

function getWebSocketUrl() {
  const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
  return `${protocol}//${window.location.host}/ws`;
}

// Enhanced fallback polling with comprehensive data fetching for production
async function pollServerStatus() {
  try {
    // Fetch all critical data endpoints
    const [statsResponse, botResponse, usersResponse] = await Promise.all([
      fetch('/api/discord/server-stats'),
      fetch('/api/discord/bot-status'),
      fetch('/api/users')
    ]);
    
    const statsData = await statsResponse.json();
    const botData = await botResponse.json();
    const usersData = await usersResponse.json();
    
    return {
      serverStats: statsData,
      botStatus: botData,
      users: usersData,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('❌ Production polling failed:', error);
    return null;
  }
}

interface WebSocketData {
  type: string;
  data: any;
  timestamp?: string;
}

export function useWebSocket() {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebSocketData | null>(null);
  const ws = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const mountedRef = useRef(true);
  const connectingRef = useRef(false);
  const maxReconnectAttempts = 3;
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const [usingFallback, setUsingFallback] = useState(false);
  const heartbeatRef = useRef<NodeJS.Timeout | null>(null);
  const pingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const connect = useCallback(() => {
    if (!mountedRef.current || connectingRef.current) return;
    
    connectingRef.current = true;
    
    try {
      // 清理现有连接
      if (ws.current) {
        ws.current.onopen = null;
        ws.current.onmessage = null;
        ws.current.onclose = null;
        ws.current.onerror = null;
        if (ws.current.readyState === WebSocket.OPEN || ws.current.readyState === WebSocket.CONNECTING) {
          ws.current.close();
        }
        ws.current = null;
      }

      const wsUrl = getWebSocketUrl();
      console.log(`🔌 WebSocket attempt ${reconnectAttempts.current + 1}/${maxReconnectAttempts + 1}`);
      
      ws.current = new WebSocket(wsUrl);
      
      // Set connection timeout
      const connectionTimeout = setTimeout(() => {
        if (ws.current && ws.current.readyState === WebSocket.CONNECTING) {
          console.log('🕐 WebSocket connection timeout');
          ws.current.close();
        }
      }, 10000); // 10 second timeout

      ws.current.onopen = () => {
        if (!mountedRef.current) return;
        console.log('✅ WebSocket connected successfully');
        clearTimeout(connectionTimeout);
        setIsConnected(true);
        setUsingFallback(false);
        reconnectAttempts.current = 0;
        connectingRef.current = false;
        
        // 清除重连定时器和轮询
        if (reconnectTimeoutRef.current) {
          clearTimeout(reconnectTimeoutRef.current);
          reconnectTimeoutRef.current = null;
        }
        if (pollingRef.current) {
          clearInterval(pollingRef.current);
          pollingRef.current = null;
        }
        
        // 启动心跳
        startHeartbeat();
      };

      ws.current.onmessage = (event) => {
        if (!mountedRef.current) return;
        try {
          const data = JSON.parse(event.data);
          
          // 处理心跳响应
          if (data.type === 'pong') {
            console.log('💓 WebSocket heartbeat received');
            // 清除ping超时
            if (pingTimeoutRef.current) {
              clearTimeout(pingTimeoutRef.current);
              pingTimeoutRef.current = null;
            }
            return;
          }
          
          console.log('📥 Processing WebSocket message:', data.type, data.data);
          setLastMessage(data);
        } catch (error) {
          console.error('❌ Failed to parse message:', error);
        }
      };

      ws.current.onclose = (event) => {
        if (!mountedRef.current) return;
        console.log('📡 WebSocket disconnected, code:', event.code);
        setIsConnected(false);
        connectingRef.current = false;
        
        // 停止心跳
        stopHeartbeat();
        
        // 更积极的重连策略，但避免过度重连
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          const delay = Math.min(1000 * reconnectAttempts.current, 5000); // 递增延迟，最多5秒
          console.log(`⏳ Reconnecting in ${delay}ms...`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              connect();
            }
          }, delay);
        } else if (reconnectAttempts.current >= maxReconnectAttempts) {
          console.log('🏭 Production mode - starting with polling fallback');
          setUsingFallback(true);
          startPolling();
        }
      };

      ws.current.onerror = (error) => {
        if (!mountedRef.current) return;
        console.error('❌ WebSocket error:', error);
        connectingRef.current = false;
      };

    } catch (error) {
      console.error('❌ WebSocket connection failed:', error);
      connectingRef.current = false;
      setIsConnected(false);
    }
  }, []);

  const startHeartbeat = useCallback(() => {
    if (heartbeatRef.current) return;
    
    heartbeatRef.current = setInterval(() => {
      if (ws.current && ws.current.readyState === WebSocket.OPEN) {
        // 发送心跳
        ws.current.send(JSON.stringify({ type: 'ping' }));
        
        // 设置超时检测
        pingTimeoutRef.current = setTimeout(() => {
          console.log('💀 WebSocket heartbeat timeout, reconnecting...');
          if (ws.current) {
            ws.current.close();
          }
        }, 5000); // 5秒超时
      }
    }, 30000); // 每30秒发送一次心跳
  }, []);

  const stopHeartbeat = useCallback(() => {
    if (heartbeatRef.current) {
      clearInterval(heartbeatRef.current);
      heartbeatRef.current = null;
    }
    if (pingTimeoutRef.current) {
      clearTimeout(pingTimeoutRef.current);
      pingTimeoutRef.current = null;
    }
  }, []);

  const startPolling = useCallback(() => {
    console.log('🏭 Starting production polling mode...');
    setIsConnected(true); // Consider polling as "connected"
    stopHeartbeat(); // 停止心跳
    
    const poll = async () => {
      if (!mountedRef.current) return;
      
      try {
        const data = await pollServerStatus();
        if (data) {
          // 模拟WebSocket消息格式
          setLastMessage({
            type: 'botStatus',
            data: data.botStatus?.data || { connected: false }
          });
        }
      } catch (error) {
        console.error('Polling error:', error);
      }
    };
    
    // Initial poll
    poll();
    
    // Set up interval polling every 3 seconds
    pollingRef.current = setInterval(poll, 3000);
  }, [stopHeartbeat]);

  const sendMessage = useCallback((message: any) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      try {
        ws.current.send(JSON.stringify(message));
      } catch (error) {
        console.error('❌ Failed to send message:', error);
      }
    } else if (usingFallback) {
      console.log('📡 Using HTTP fallback, message ignored:', message);
    } else {
      console.warn('⚠️ WebSocket not connected');
    }
  }, [usingFallback]);

  useEffect(() => {
    mountedRef.current = true;
    
    // 延迟连接，确保页面加载完成
    const initTimeout = setTimeout(() => {
      if (mountedRef.current) {
        connect();
      }
    }, 1000);
    
    return () => {
      mountedRef.current = false;
      clearTimeout(initTimeout);
      
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
      }
      
      stopHeartbeat();
      
      if (ws.current) {
        ws.current.onopen = null;
        ws.current.onmessage = null;
        ws.current.onclose = null;
        ws.current.onerror = null;
        ws.current.close();
        ws.current = null;
      }
    };
  }, [connect, startPolling, stopHeartbeat]);

  return {
    isConnected,
    lastMessage,
    sendMessage
  };
}