{"name": "discord-bot-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "vite", "dev:backend": "nodemon server/index.js", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "express": "^4.18.2", "discord.js": "^14.14.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "sqlite3": "^5.1.6", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "node-cron": "^3.0.3", "ws": "^8.14.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "nodemon": "^3.0.2", "concurrently": "^8.2.2"}, "knip": {"entry": ["src/main.tsx", "src/pages/*.tsx", "src/components/*.tsx", "src/contexts/*.tsx", "server/index.js"], "project": ["src/**/*.{ts,tsx,js,jsx}", "server/**/*.js"]}}