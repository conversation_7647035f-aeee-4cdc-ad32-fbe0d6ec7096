import React, { useState, useEffect } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';
import { useData } from '../contexts/DataContext';
import { getApiBaseUrl } from '../utils/api.js';
import BotStabilityMonitor from "@/components/BotStabilityMonitor";

const Settings: React.FC = () => {
  const [isTokenModalOpen, setIsTokenModalOpen] = useState(false);
  const [isBotTestModalOpen, setIsBotTestModalOpen] = useState(false);
  const [isSyncModalOpen, setIsSyncModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [botToken, setBotToken] = useState('');
  const [testResult, setTestResult] = useState<any>(null);
  const [settings, setSettings] = useState<any>({});
  const [syncResult, setSyncResult] = useState<any>(null);
  const { showNotification } = useNotification();
  const { botStatus, serverData, isConnected, refreshData, healthCheck } = useData();

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/settings`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Server returned non-JSON response');
      }
      
      const data = await response.json();
      if (data.success) {
        setSettings(data.data);
        setBotToken(data.data.bot_token || '');
      } else {
        throw new Error(data.message || 'Failed to load settings');
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      showNotification('加载设置失败，请检查服务器连接', 'error');
    }
  };

  const handleTestToken = async () => {
    if (!botToken.trim()) {
      showNotification('请输入机器人Token', 'error');
      return;
    }

    setIsLoading(true);
    setTestResult(null);

    try {
      const response = await fetch(`${getApiBaseUrl()}/api/discord/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: botToken }),
      });

      const data = await response.json();
      setTestResult(data);

      if (data.success) {
        showNotification('Token测试成功！', 'success');
      } else {
        showNotification(`Token测试失败: ${data.message}`, 'error');
      }
    } catch (error) {
      console.error('Token test error:', error);
      showNotification('Token测试失败，请检查网络连接', 'error');
      setTestResult({
        success: false,
        message: '网络连接错误'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveToken = async () => {
    if (!botToken.trim()) {
      showNotification('请输入机器人Token', 'error');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`${getApiBaseUrl()}/api/settings/bot_token`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: botToken }),
      });

      const data = await response.json();

      if (data.success) {
        showNotification('Token保存成功！正在连接Discord并同步数据...', 'success');
        setIsTokenModalOpen(false);

        // Refresh data after successful save
        setTimeout(() => {
          refreshData();
        }, 3000);
      } else {
        showNotification(`Token保存失败: ${data.message}`, 'error');
      }
    } catch (error) {
      console.error('Token save error:', error);
      showNotification('Token保存失败，请检查网络连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestartBot = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/discord/restart`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        showNotification('机器人重启成功！', 'success');
        // Refresh data after restart
        setTimeout(() => {
          refreshData();
        }, 2000);
      } else {
        showNotification(`机器人重启失败: ${data.message}`, 'error');
      }
    } catch (error) {
      console.error('Bot restart error:', error);
      showNotification('机器人重启失败，请检查网络连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncAllData = async () => {
    setIsSyncing(true);
    setSyncResult(null);

    try {
      // Sync Discord users
      const usersResponse = await fetch(`${getApiBaseUrl()}/api/users/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const usersData = await usersResponse.json();
      
      // Refresh server data
      await refreshData();

      setSyncResult({
        success: true,
        message: '数据同步成功',
        details: usersData
      });

      showNotification('所有数据同步成功！', 'success');
    } catch (error) {
      console.error('Data sync error:', error);
      setSyncResult({
        success: false,
        message: '数据同步失败，请检查Discord连接'
      });
      showNotification('数据同步失败，请检查Discord连接', 'error');
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
      <div className="max-w-5xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-8">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-2xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
              <icons.Settings className="w-8 h-8 text-black font-bold" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">设置中心</h1>
              <p className="text-gray-500 dark:text-gray-400">管理机器人配置和系统设置</p>
            </div>
          </div>
        </div>

        {/* Bot Status Card */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                <icons.Bot className="w-6 h-6 text-white font-bold" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">Discord 机器人状态</h2>
                <div className="flex items-center space-x-2 mt-1">
                  <div className={`w-3 h-3 rounded-full ${botStatus?.connected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className={`text-sm font-medium ${botStatus?.connected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {botStatus?.connected ? '在线' : '离线'}
                  </span>
                  {botStatus?.connected && (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      • 延迟: {botStatus?.latency || '未知'}ms
                    </span>
                  )}
                </div>
              </div>
            </div>
            <button
              onClick={handleRestartBot}
              disabled={isLoading}
              className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-6 py-3 rounded-xl font-medium transition-all shadow-lg shadow-yellow-500/25 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <icons.RotateCcw className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
              <span>重启机器人</span>
            </button>
          </div>

          {botStatus?.connected && (
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <icons.Users className="h-8 w-8 text-blue-500" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{serverData?.memberCount || 0}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">服务器成员</p>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <icons.Hash className="h-8 w-8 text-green-500" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{serverData?.channels?.length || 0}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">可用频道</p>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <icons.Shield className="h-8 w-8 text-purple-500" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">{serverData?.roles?.length || 0}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">身份组</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Discord Configuration */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-400 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg shadow-indigo-500/25">
                <icons.Zap className="w-6 h-6 text-white font-bold" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">Discord 配置</h2>
                <p className="text-gray-500 dark:text-gray-400">管理机器人Token和连接设置</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">机器人Token</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {settings.bot_token ? '已配置 • ••••••••••' : '未配置'}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setIsBotTestModalOpen(true)}
                      className="bg-blue-100 dark:bg-blue-900/20 hover:bg-blue-200 dark:hover:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-colors border border-blue-200 dark:border-blue-800"
                    >
                      测试连接
                    </button>
                    <button
                      onClick={() => setIsTokenModalOpen(true)}
                      className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                    >
                      <icons.Edit className="h-4 w-4 inline mr-1" />
                      编辑
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Data Synchronization */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                <icons.RefreshCw className="w-6 h-6 text-white font-bold" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">服务器数据同步</h2>
                <p className="text-gray-500 dark:text-gray-400">同步Discord用户和服务器数据</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">全量数据同步</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      同步用户、频道、身份组等所有数据
                    </p>
                  </div>
                  <button
                    onClick={() => setIsSyncModalOpen(true)}
                    disabled={!botStatus?.connected}
                    className="bg-gradient-to-r from-green-400 to-green-500 hover:from-green-500 hover:to-green-600 text-white px-6 py-2 rounded-lg font-medium transition-all shadow-lg shadow-green-500/25 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                  >
                    <icons.Download className="h-4 w-4" />
                    <span>立即同步</span>
                  </button>
                </div>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">自动同步状态</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {botStatus?.connected ? '已启用 - 实时同步中' : '已禁用 - 请先连接机器人'}
                    </p>
                  </div>
                  <div className={`w-3 h-3 rounded-full ${botStatus?.connected ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bot Stability Monitor */}
        <div className="mt-8">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                <icons.Activity className="w-6 h-6 text-white font-bold" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">系统稳定性监控</h2>
                <p className="text-gray-500 dark:text-gray-400">实时监控机器人系统健康状况</p>
              </div>
            </div>
            
            <BotStabilityMonitor data={healthCheck} />
          </div>
        </div>

        {/* Token Configuration Modal */}
        {isTokenModalOpen && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">配置Discord Token</h3>
                <button
                  onClick={() => setIsTokenModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <icons.X className="h-5 w-5" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Discord Bot Token
                  </label>
                  <input
                    type="password"
                    value={botToken}
                    onChange={(e) => setBotToken(e.target.value)}
                    placeholder="输入您的Discord机器人Token"
                    className="w-full bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl px-4 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-colors"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    请在Discord开发者门户获取您的机器人Token
                  </p>
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <button
                    onClick={() => setIsTokenModalOpen(false)}
                    className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl font-medium transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleSaveToken}
                    disabled={isLoading || !botToken.trim()}
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-6 py-3 rounded-xl font-medium transition-all shadow-lg shadow-yellow-500/25 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? '保存中...' : '保存Token'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bot Test Modal */}
        {isBotTestModalOpen && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">测试Discord连接</h3>
                <button
                  onClick={() => setIsBotTestModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <icons.X className="h-5 w-5" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Discord Bot Token
                  </label>
                  <input
                    type="password"
                    value={botToken}
                    onChange={(e) => setBotToken(e.target.value)}
                    placeholder="输入要测试的Token"
                    className="w-full bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl px-4 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  />
                </div>

                {testResult && (
                  <div className={`p-4 rounded-xl border ${
                    testResult.success 
                      ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
                      : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
                  }`}>
                    <div className="flex items-center space-x-2">
                      {testResult.success ? (
                        <icons.CheckCircle className="w-5 h-5" />
                      ) : (
                        <icons.AlertCircle className="w-5 h-5" />
                      )}
                      <span className="font-medium">{testResult.message}</span>
                    </div>
                    {testResult.success && testResult.data && (
                      <div className="mt-2 text-sm">
                        <p>机器人名称: {testResult.data.username}</p>
                        <p>服务器数量: {testResult.data.guilds}</p>
                      </div>
                    )}
                  </div>
                )}

                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <button
                    onClick={() => setIsBotTestModalOpen(false)}
                    className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl font-medium transition-colors"
                  >
                    关闭
                  </button>
                  <button
                    onClick={handleTestToken}
                    disabled={isLoading || !botToken.trim()}
                    className="bg-gradient-to-r from-blue-400 to-blue-500 hover:from-blue-500 hover:to-blue-600 text-white px-6 py-3 rounded-xl font-medium transition-all shadow-lg shadow-blue-500/25 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? '测试中...' : '开始测试'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Data Sync Modal */}
        {isSyncModalOpen && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-md">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">数据同步</h3>
                <button
                  onClick={() => setIsSyncModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <icons.X className="h-5 w-5" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
                  <div className="flex items-center space-x-2">
                    <icons.Info className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-blue-800 dark:text-blue-400">
                      将同步以下数据：
                    </span>
                  </div>
                  <ul className="mt-2 text-sm text-blue-700 dark:text-blue-300 list-disc list-inside space-y-1">
                    <li>Discord用户信息</li>
                    <li>服务器频道列表</li>
                    <li>身份组配置</li>
                    <li>成员权限设置</li>
                  </ul>
                </div>

                {syncResult && (
                  <div className={`p-4 rounded-xl border ${
                    syncResult.success 
                      ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
                      : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
                  }`}>
                    <div className="flex items-center space-x-2">
                      {syncResult.success ? (
                        <icons.CheckCircle className="w-5 h-5" />
                      ) : (
                        <icons.AlertCircle className="w-5 h-5" />
                      )}
                      <span className="font-medium">{syncResult.message}</span>
                    </div>
                    {syncResult.success && syncResult.details && (
                      <div className="mt-2 text-sm">
                        <p>同步完成，数据已更新</p>
                      </div>
                    )}
                  </div>
                )}

                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <button
                    onClick={() => setIsSyncModalOpen(false)}
                    className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl font-medium transition-colors"
                  >
                    关闭
                  </button>
                  <button
                    onClick={handleSyncAllData}
                    disabled={isSyncing || !botStatus?.connected}
                    className="bg-gradient-to-r from-green-400 to-green-500 hover:from-green-500 hover:to-green-600 text-white px-6 py-3 rounded-xl font-medium transition-all shadow-lg shadow-green-500/25 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSyncing ? '同步中...' : '开始同步'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Settings;