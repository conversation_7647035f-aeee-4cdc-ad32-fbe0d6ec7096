import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Database, 
  MessageSquare, 
  Users,
  Zap,
  TrendingUp
} from 'lucide-react';

interface BotStabilityData {
  timestamp: string;
  discordReady: boolean;
  databaseConnected: boolean;
  checkinConfigActive: boolean;
  consecutiveFailures: number;
  reconnectionAttempts?: number;
  details: {
    discordLatency: number;
    guilds: number;
    users: number;
    activeWebSockets: number;
    memoryUsage: NodeJS.MemoryUsage;
    uptime: number;
  };
}

interface BotStabilityMonitorProps {
  data: BotStabilityData | null;
}

const BotStabilityMonitor: React.FC<BotStabilityMonitorProps> = ({ data }) => {
  const [stabilityScore, setStabilityScore] = useState(100);
  const [stabilityHistory, setStabilityHistory] = useState<number[]>([]);

  useEffect(() => {
    if (!data) return;

    // Calculate stability score based on system health
    let score = 100;
    
    if (!data.discordReady) score -= 40;
    if (!data.databaseConnected) score -= 30;
    if (!data.checkinConfigActive) score -= 10;
    if (data.consecutiveFailures > 0) score -= (data.consecutiveFailures * 5);
    if (data.details.discordLatency > 1000) score -= 10;
    if (data.details.memoryUsage.heapUsed > 500 * 1024 * 1024) score -= 5; // 500MB
    
    score = Math.max(0, score);
    setStabilityScore(score);
    
    // Update history (keep last 20 readings)
    setStabilityHistory(prev => [...prev.slice(-19), score]);
  }, [data]);

  if (!data) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Bot Stability Monitor
          </CardTitle>
          <CardDescription>Real-time system health monitoring</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusColor = (healthy: boolean) => healthy ? 'text-green-600' : 'text-red-600';
  const getStatusIcon = (healthy: boolean) => healthy ? CheckCircle : AlertTriangle;
  const getStabilityColor = () => {
    if (stabilityScore >= 90) return 'text-green-600';
    if (stabilityScore >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatMemory = (bytes: number) => {
    return `${Math.round(bytes / 1024 / 1024)}MB`;
  };

  return (
    <div className="space-y-4">
      {/* Overall Stability Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Overall Stability Score
            <Badge variant={stabilityScore >= 90 ? "default" : stabilityScore >= 70 ? "secondary" : "destructive"}>
              {stabilityScore}%
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Progress value={stabilityScore} className="w-full" />
          <p className="text-sm text-gray-600 mt-2">
            Last updated: {new Date(data.timestamp).toLocaleTimeString()}
          </p>
        </CardContent>
      </Card>

      {/* System Status Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Discord Status */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-sm">
              <MessageSquare className="h-4 w-4" />
              Discord Connection
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {React.createElement(getStatusIcon(data.discordReady), {
                className: `h-4 w-4 ${getStatusColor(data.discordReady)}`
              })}
              <span className={`text-sm font-medium ${getStatusColor(data.discordReady)}`}>
                {data.discordReady ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            <div className="mt-2 text-xs text-gray-600">
              <p>Latency: {data.details.discordLatency}ms</p>
              <p>Guilds: {data.details.guilds}</p>
              <p>Users: {data.details.users}</p>
            </div>
          </CardContent>
        </Card>

        {/* Database Status */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Database className="h-4 w-4" />
              Database
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {React.createElement(getStatusIcon(data.databaseConnected), {
                className: `h-4 w-4 ${getStatusColor(data.databaseConnected)}`
              })}
              <span className={`text-sm font-medium ${getStatusColor(data.databaseConnected)}`}>
                {data.databaseConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            <div className="mt-2 text-xs text-gray-600">
              <p>Config: {data.checkinConfigActive ? 'Active' : 'Inactive'}</p>
              <p>WebSockets: {data.details.activeWebSockets}</p>
            </div>
          </CardContent>
        </Card>

        {/* System Resources */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2 text-sm">
              <Zap className="h-4 w-4" />
              System Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1 text-xs text-gray-600">
              <p>Uptime: {formatUptime(data.details.uptime)}</p>
              <p>Memory: {formatMemory(data.details.memoryUsage.heapUsed)}</p>
              <p>Heap Total: {formatMemory(data.details.memoryUsage.heapTotal)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {data.consecutiveFailures > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            System has experienced {data.consecutiveFailures} consecutive failures. 
            {data.reconnectionAttempts && ` Reconnection attempts: ${data.reconnectionAttempts}`}
          </AlertDescription>
        </Alert>
      )}

      {!data.discordReady && (
        <Alert variant="destructive">
          <MessageSquare className="h-4 w-4" />
          <AlertDescription>
            Discord bot is not connected. Check token and network connectivity.
          </AlertDescription>
        </Alert>
      )}

      {data.details.discordLatency > 1000 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            High Discord latency detected ({data.details.discordLatency}ms). May affect response times.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default BotStabilityMonitor;