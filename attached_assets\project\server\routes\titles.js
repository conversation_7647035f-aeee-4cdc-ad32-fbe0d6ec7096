import express from 'express';
import { db } from '../database/init.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Get all titles
router.get('/', async (req, res) => {
  try {
    const titles = await db.allAsync(`
      SELECT * FROM titles 
      ORDER BY min_points ASC
    `);

    res.json({
      success: true,
      data: titles
    });
  } catch (error) {
    logger.error('Error getting titles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get titles'
    });
  }
});

// Create new title
router.post('/', async (req, res) => {
  try {
    const { name, description, min_points, max_points, color, icon } = req.body;

    if (!name || min_points === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Name and min_points are required'
      });
    }

    await db.runAsync(`
      INSERT INTO titles (name, description, min_points, max_points, color, icon)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [name, description, min_points, max_points, color, icon]);

    res.json({
      success: true,
      message: 'Title created successfully'
    });
  } catch (error) {
    logger.error('Error creating title:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create title'
    });
  }
});

// Update title
router.put('/:id', async (req, res) => {
  try {
    const { name, description, min_points, max_points, color, icon, is_active } = req.body;

    await db.runAsync(`
      UPDATE titles 
      SET name = ?, description = ?, min_points = ?, max_points = ?, color = ?, icon = ?, is_active = ?
      WHERE id = ?
    `, [name, description, min_points, max_points, color, icon, is_active, req.params.id]);

    res.json({
      success: true,
      message: 'Title updated successfully'
    });
  } catch (error) {
    logger.error('Error updating title:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update title'
    });
  }
});

// Delete title
router.delete('/:id', async (req, res) => {
  try {
    await db.runAsync('DELETE FROM titles WHERE id = ?', [req.params.id]);

    res.json({
      success: true,
      message: 'Title deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting title:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete title'
    });
  }
});

export default router;