import React, { useState } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';

const LotterySystem: React.FC = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDrawModalOpen, setIsDrawModalOpen] = useState(false);
  const [selectedLottery, setSelectedLottery] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const { showNotification } = useNotification();

  const [lotteries, setLotteries] = useState([
    {
      id: '1',
      title: '新年福利抽奖',
      description: '庆祝新年，丰厚奖品等你来拿！',
      prize: 'Discord Nitro × 3',
      cost: 100,
      maxParticipants: 50,
      currentParticipants: 23,
      startDate: '2024-03-01',
      endDate: '2024-03-15',
      status: 'active',
      winner: null
    },
    {
      id: '2',
      title: '活跃用户专享',
      description: '感谢活跃用户的支持与参与',
      prize: '游戏激活码 × 5',
      cost: 50,
      maxParticipants: 30,
      currentParticipants: 30,
      startDate: '2024-02-15',
      endDate: '2024-02-28',
      status: 'ended',
      winner: '张三'
    },
    {
      id: '3',
      title: '周末惊喜抽奖',
      description: '周末特别活动，人人有机会',
      prize: '积分奖励 × 1000',
      cost: 0,
      maxParticipants: 100,
      currentParticipants: 67,
      startDate: '2024-03-02',
      endDate: '2024-03-03',
      status: 'active',
      winner: null
    }
  ]);

  const handleCreateLottery = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setOperationResult({ type: 'success', message: '抽奖活动创建成功！' });
      setIsCreateModalOpen(false);
      setTimeout(() => setOperationResult(null), 3000);
    }, 1500);
  };

  const handleEditLottery = (lottery: any) => {
    setSelectedLottery(lottery);
    setIsEditModalOpen(true);
  };

  const handleDrawLottery = (lottery: any) => {
    setSelectedLottery(lottery);
    setIsDrawModalOpen(true);
  };

  const handleFormSubmit = (type: string) => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setOperationResult({
        type: 'success',
        message: type === 'edit' ? '抽奖活动更新成功！' : 
                type === 'draw' ? '抽奖完成！中奖者已通知' : '操作完成！'
      });
      
      setTimeout(() => {
        setOperationResult(null);
        if (type === 'edit') setIsEditModalOpen(false);
        if (type === 'draw') setIsDrawModalOpen(false);
      }, 2000);
    }, 1500);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400';
      case 'ended': return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
      case 'cancelled': return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400';
      default: return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '进行中';
      case 'ended': return '已结束';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">抽奖系统</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">创建和管理抽奖活动</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-yellow-500/25 font-medium"
          >
            <icons.Plus className="w-4 h-4" />
            <span>创建抽奖</span>
          </button>
        </div>
      </div>

      {/* 操作结果提示 */}
      {operationResult && (
        <div className={`p-4 rounded-xl border ${
          operationResult.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {operationResult.type === 'success' ? (
              <icons.CheckCircle className="w-5 h-5" />
            ) : (
              <icons.AlertCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{operationResult.message}</span>
          </div>
        </div>
      )}

      {/* 抽奖列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {lotteries.map((lottery) => (
          <div key={lottery.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all duration-200">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-pink-400 to-pink-500 rounded-xl flex items-center justify-center shadow-lg shadow-pink-500/25">
                  <icons.Gift className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">{lottery.title}</h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(lottery.status)}`}>
                    {getStatusText(lottery.status)}
                  </span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {lottery.status === 'active' && (
                  <button 
                    onClick={() => handleDrawLottery(lottery)}
                    className="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 transition-colors p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20" 
                    title="开始抽奖"
                  >
                    <icons.Shuffle className="w-4 h-4" />
                  </button>
                )}
                <button 
                  onClick={() => handleEditLottery(lottery)}
                  className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 transition-colors p-2 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20" 
                  title="编辑抽奖"
                >
                  <icons.Edit className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{lottery.description}</p>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">奖品</span>
                <span className="font-medium text-gray-900 dark:text-white">{lottery.prize}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">参与费用</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {lottery.cost === 0 ? '免费' : `${lottery.cost} 积分`}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">参与人数</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {lottery.currentParticipants} / {lottery.maxParticipants}
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-pink-400 to-pink-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${(lottery.currentParticipants / lottery.maxParticipants) * 100}%` }}
                />
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">活动时间</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {lottery.startDate} ~ {lottery.endDate}
                </span>
              </div>
              {lottery.winner && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">中奖者</span>
                  <span className="font-medium text-yellow-600 dark:text-yellow-400">🎉 {lottery.winner}</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* 创建抽奖模态框 */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">创建抽奖活动</h3>
              <button 
                onClick={() => setIsCreateModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={(e) => { e.preventDefault(); handleCreateLottery(); }} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">活动标题</label>
                <input
                  type="text"
                  placeholder="请输入抽奖活动标题"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">活动描述</label>
                <textarea
                  placeholder="请描述抽奖活动..."
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">奖品</label>
                <input
                  type="text"
                  placeholder="Discord Nitro × 1"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">参与费用</label>
                  <input
                    type="number"
                    placeholder="0"
                    min="0"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最大人数</label>
                  <input
                    type="number"
                    placeholder="50"
                    min="1"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">开始时间</label>
                  <input
                    type="date"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">结束时间</label>
                  <input
                    type="date"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                >
                  {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                  <span>{isLoading ? '创建中...' : '创建抽奖'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default LotterySystem;