import React, { useState } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';

const SystemLogs: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedModule, setSelectedModule] = useState('');
  const [selectedUser, setSelectedUser] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isArchiveModalOpen, setIsArchiveModalOpen] = useState(false);
  const [isClearModalOpen, setIsClearModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const [exportProgress, setExportProgress] = useState(0);
  const { showNotification } = useNotification();

  const logs = [
    {
      id: '1',
      time: '2024-03-01 14:32:15',
      user: '管理员',
      module: '积分系统',
      action: '为用户 @张三 发放了 50 积分',
      level: 'info',
      ip: '*************',
      userAgent: 'Chrome/120.0.0.0',
      details: '通过管理后台手动发放积分，原因：活动奖励',
      duration: '0.12s'
    },
    {
      id: '2',
      time: '2024-03-01 14:28:43',
      user: '系统',
      module: '签到系统',
      action: '用户 @李四 完成每日签到',
      level: 'info',
      ip: '*************',
      userAgent: 'Discord Bot',
      details: '自动签到，获得基础积分 10 分',
      duration: '0.05s'
    },
    {
      id: '3',
      time: '2024-03-01 14:25:12',
      user: '管理员',
      module: '头衔系统',
      action: '创建新头衔：超级会员',
      level: 'info',
      ip: '*************',
      userAgent: 'Chrome/120.0.0.0',
      details: '积分要求：5000-9999，颜色：#D6F36F',
      duration: '0.23s'
    },
    {
      id: '4',
      time: '2024-03-01 14:20:58',
      user: '系统',
      module: '自动化规则',
      action: '规则 "新用户欢迎" 执行成功',
      level: 'success',
      ip: 'system',
      userAgent: 'Discord Bot',
      details: '触发条件：用户加入，执行动作：发送欢迎消息',
      duration: '0.08s'
    },
    {
      id: '5',
      time: '2024-03-01 14:18:36',
      user: '管理员',
      module: '用户管理',
      action: '同步用户数据',
      level: 'info',
      ip: '*************',
      userAgent: 'Chrome/120.0.0.0',
      details: '从 Discord 服务器同步了 1,234 个用户信息',
      duration: '2.45s'
    },
    {
      id: '6',
      time: '2024-03-01 14:15:22',
      user: '系统',
      module: 'Bot连接',
      action: 'Discord Bot 连接失败，正在重试...',
      level: 'warning',
      ip: 'system',
      userAgent: 'Discord Bot',
      details: '连接超时，错误代码：ECONNRESET，重试次数：3/5',
      duration: '5.00s'
    },
    {
      id: '7',
      time: '2024-03-01 14:12:45',
      user: '系统',
      module: '数据库',
      action: '数据库连接超时',
      level: 'error',
      ip: 'system',
      userAgent: 'System',
      details: '连接池耗尽，当前连接数：50/50，等待队列：15',
      duration: '10.00s'
    },
    {
      id: '8',
      time: '2024-03-01 14:10:11',
      user: '管理员',
      module: '系统设置',
      action: '修改了签到奖励积分为 15 分',
      level: 'info',
      ip: '*************',
      userAgent: 'Chrome/120.0.0.0',
      details: '原值：10 分，新值：15 分，生效时间：立即',
      duration: '0.18s'
    }
  ];

  const modules = ['积分系统', '签到系统', '头衔系统', '用户管理', '自动化规则', 'Bot连接', '数据库', '系统设置', '抽奖系统'];
  const users = ['管理员', '系统', '张三', '李四', '王五'];
  const levels = ['info', 'success', 'warning', 'error'];

  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.module.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.details.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesModule = !selectedModule || log.module === selectedModule;
    const matchesUser = !selectedUser || log.user === selectedUser;
    const matchesLevel = !selectedLevel || log.level === selectedLevel;
    
    return matchesSearch && matchesModule && matchesUser && matchesLevel;
  });

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400';
      case 'warning': return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400';
      case 'success': return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400';
      default: return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400';
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error': return icons.AlertCircle;
      case 'warning': return icons.AlertTriangle;
      case 'success': return icons.CheckCircle;
      default: return icons.Info;
    }
  };

  const getLevelText = (level: string) => {
    switch (level) {
      case 'error': return '错误';
      case 'warning': return '警告';
      case 'success': return '成功';
      default: return '信息';
    }
  };

  const handleExport = () => {
    setIsExportModalOpen(true);
    setExportProgress(0);
    setIsLoading(true);
    
    const interval = setInterval(() => {
      setExportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          return 100;
        }
        return prev + 20;
      });
    }, 300);
  };

  const handleArchive = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('日志归档成功！');
      setIsArchiveModalOpen(false);
    }, 2000);
  };

  const handleClearLogs = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('日志清空成功！');
      setIsClearModalOpen(false);
    }, 2000);
  };

  const handleSaveSettings = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('日志设置保存成功！');
      setIsSettingsModalOpen(false);
    }, 1500);
  };

  const handleAdvancedFilter = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('高级筛选应用成功！');
      setIsFilterModalOpen(false);
    }, 1500);
  };

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white">系统日志</h2>
        <p className="text-gray-600 dark:text-gray-400 mt-1">查看系统操作记录和错误日志</p>
      </div>

      {/* 操作结果提示 */}
      {operationResult && (
        <div className={`p-4 rounded-xl border ${
          operationResult.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {operationResult.type === 'success' ? (
              <icons.CheckCircle className="w-5 h-5" />
            ) : (
              <icons.AlertCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{operationResult.message}</span>
          </div>
        </div>
      )}

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧统计区域 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 日志统计卡片 */}
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.FileText className="w-6 h-6 text-black font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日日志</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{logs.length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" style={{ width: '85%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">总计 2,847 条</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-red-500 rounded-xl flex items-center justify-center shadow-lg shadow-red-500/25">
                  <icons.AlertCircle className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">错误日志</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{logs.filter(log => log.level === 'error').length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-red-400 to-red-500 h-2 rounded-full" style={{ width: '15%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">需要关注</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                  <icons.AlertTriangle className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">警告日志</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{logs.filter(log => log.level === 'warning').length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-orange-400 to-orange-500 h-2 rounded-full" style={{ width: '12%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">建议处理</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                  <icons.Activity className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">系统运行</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">正常</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" style={{ width: '95%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">运行时间 7天</p>
            </div>
          </div>

          {/* 日志级别分布 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">日志级别分布</h3>
            <div className="space-y-4">
              {[
                { level: 'info', label: '信息', count: logs.filter(log => log.level === 'info').length, color: 'bg-blue-500' },
                { level: 'success', label: '成功', count: logs.filter(log => log.level === 'success').length, color: 'bg-green-500' },
                { level: 'warning', label: '警告', count: logs.filter(log => log.level === 'warning').length, color: 'bg-yellow-400' },
                { level: 'error', label: '错误', count: logs.filter(log => log.level === 'error').length, color: 'bg-red-500' }
              ].map(item => (
                <div key={item.level} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 ${item.color} rounded-full`}></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">{item.label}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-900 dark:text-white">{item.count}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {logs.length > 0 ? ((item.count / logs.length) * 100).toFixed(1) : 0}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 中间主要内容区域 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          {/* 搜索和筛选 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="md:col-span-2">
                <div className="relative">
                  <icons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="搜索日志内容、用户或模块..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div>
                <select 
                  value={selectedModule}
                  onChange={(e) => setSelectedModule(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">所有模块</option>
                  {modules.map(module => (
                    <option key={module} value={module}>{module}</option>
                  ))}
                </select>
              </div>
              <div>
                <select 
                  value={selectedUser}
                  onChange={(e) => setSelectedUser(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">所有用户</option>
                  {users.map(user => (
                    <option key={user} value={user}>{user}</option>
                  ))}
                </select>
              </div>
              <div>
                <select 
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="">所有级别</option>
                  {levels.map(level => (
                    <option key={level} value={level}>{getLevelText(level)}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                显示 {filteredLogs.length} 条日志记录
              </div>
              <div className="flex space-x-2">
                <button 
                  onClick={handleExport}
                  className="px-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center space-x-2 transition-colors"
                >
                  <icons.Download className="w-4 h-4" />
                  <span>导出日志</span>
                </button>
                <button 
                  onClick={() => {
                    showNotification('日志已刷新！');
                  }}
                  className="px-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center space-x-2 transition-colors"
                >
                  <icons.RefreshCw className="w-4 h-4" />
                  <span>刷新</span>
                </button>
                <button 
                  onClick={() => setIsClearModalOpen(true)}
                  className="px-4 py-2 text-sm border border-red-300 dark:border-red-600 text-red-600 dark:text-red-400 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2 transition-colors"
                >
                  <icons.Trash2 className="w-4 h-4" />
                  <span>清空日志</span>
                </button>
              </div>
            </div>
          </div>

          {/* 日志列表 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">操作日志</h3>
              <div className="space-y-4">
                {filteredLogs.map((log) => {
                  const LevelIcon = getLevelIcon(log.level);
                  return (
                    <div key={log.id} className="border border-gray-200 dark:border-gray-600 rounded-xl p-6 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                            log.level === 'error' ? 'bg-red-100 dark:bg-red-900/20' :
                            log.level === 'warning' ? 'bg-yellow-100 dark:bg-yellow-900/20' :
                            log.level === 'success' ? 'bg-green-100 dark:bg-green-900/20' : 'bg-blue-100 dark:bg-blue-900/20'
                          }`}>
                            <LevelIcon className={`w-5 h-5 ${
                              log.level === 'error' ? 'text-red-600 dark:text-red-400' :
                              log.level === 'warning' ? 'text-yellow-600 dark:text-yellow-400' :
                              log.level === 'success' ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'
                            }`} />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-3">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getLevelColor(log.level)}`}>
                                {getLevelText(log.level)}
                              </span>
                              <span className="text-sm font-bold text-gray-900 dark:text-white">{log.module}</span>
                              <span className="text-sm text-gray-500 dark:text-gray-400">•</span>
                              <span className="text-sm text-gray-500 dark:text-gray-400">{log.user}</span>
                            </div>
                            <span className="text-sm text-gray-500 dark:text-gray-400">{log.time}</span>
                          </div>
                          <p className="text-sm text-gray-900 dark:text-white mb-2 font-medium">{log.action}</p>
                          <p className="text-xs text-gray-600 dark:text-gray-400 mb-3">{log.details}</p>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-xs text-gray-500 dark:text-gray-400">
                            <div className="flex items-center space-x-2">
                              <icons.Globe className="w-3 h-3" />
                              <span>IP: {log.ip}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <icons.Monitor className="w-3 h-3" />
                              <span>客户端: {log.userAgent}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <icons.Clock className="w-3 h-3" />
                              <span>耗时: {log.duration}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧信息面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 模块活动统计 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.BarChart3 className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">模块统计</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">活动频率</p>
              </div>
            </div>
            
            <div className="space-y-4">
              {modules.slice(0, 6).map(module => {
                const count = logs.filter(log => log.module === module).length;
                const percentage = count > 0 ? (count / logs.length) * 100 : 0;
                return (
                  <div key={module} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">{module}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400">{count} 次</span>
                        <span className="text-xs text-gray-400 dark:text-gray-500">({percentage.toFixed(1)}%)</span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 24小时趋势 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">24小时趋势</h3>
            <div className="flex items-end space-x-1 h-24">
              {Array.from({ length: 24 }, (_, hour) => {
                const height = Math.floor(Math.random() * 80) + 20;
                return (
                  <div key={hour} className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-t">
                    <div 
                      className="bg-yellow-400 rounded-t transition-all duration-300"
                      style={{ height: `${height}%` }}
                      title={`${hour}:00`}
                    />
                  </div>
                );
              })}
            </div>
            <div className="mt-4 text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">1,247</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">今日总日志数</div>
            </div>
          </div>

          {/* 系统健康状态 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">系统健康</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">CPU 使用率</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '23%' }}></div>
                  </div>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">23%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">内存使用</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-yellow-400 h-2 rounded-full" style={{ width: '67%' }}></div>
                  </div>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">67%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">磁盘空间</span>
                <div className="flex items-center space-x-2">
                  <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45%' }}></div>
                  </div>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">45%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">网络延迟</span>
                <span className="text-sm font-bold text-green-600 dark:text-green-400">45ms</span>
              </div>
            </div>
          </div>

          {/* 快速操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快速操作</h3>
            <div className="space-y-3">
              <button 
                onClick={() => setIsFilterModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Filter className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">高级筛选</span>
              </button>
              <button 
                onClick={() => setIsArchiveModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Archive className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">归档日志</span>
              </button>
              <button 
                onClick={() => setIsSettingsModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Settings className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">日志设置</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 导出日志模态框 */}
      {isExportModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">导出日志</h3>
              {!isLoading && (
                <button 
                  onClick={() => setIsExportModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <icons.X className="w-5 h-5" />
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  {isLoading ? (
                    <icons.Download className="w-8 h-8 text-black animate-pulse" />
                  ) : (
                    <icons.CheckCircle className="w-8 h-8 text-black" />
                  )}
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {isLoading ? '正在导出日志...' : '导出完成！'}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {isLoading ? '请稍候，正在生成日志文件' : operationResult?.message || '日志已成功导出'}
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">导出进度</span>
                  <span className="font-medium text-gray-900 dark:text-white">{exportProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${exportProgress}%` }}
                  />
                </div>
              </div>
              
              {!isLoading && (
                <button
                  onClick={() => {
                    setIsExportModalOpen(false);
                    showNotification('日志导出成功！');
                  }}
                  className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-medium"
                >
                  完成
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 高级筛选模态框 */}
      {isFilterModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">高级日志筛选</h3>
              <button 
                onClick={() => setIsFilterModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={(e) => { e.preventDefault(); handleAdvancedFilter(); }} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">日志级别</label>
                <div className="space-y-2">
                  {levels.map(level => (
                    <label key={level} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500"
                        defaultChecked
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">{getLevelText(level)}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">时间范围</label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">开始时间</label>
                    <input
                      type="datetime-local"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">结束时间</label>
                    <input
                      type="datetime-local"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IP 地址</label>
                <input
                  type="text"
                  placeholder="例如：*************"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">关键词</label>
                <input
                  type="text"
                  placeholder="搜索日志内容中的关键词"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsFilterModalOpen(false)}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                >
                  {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                  <span>{isLoading ? '应用中...' : '应用筛选'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 归档日志模态框 */}
      {isArchiveModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">归档日志</h3>
              <button 
                onClick={() => setIsArchiveModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={(e) => { e.preventDefault(); handleArchive(); }} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">归档范围</label>
                <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有日志</option>
                  <option>7天前的日志</option>
                  <option>30天前的日志</option>
                  <option>90天前的日志</option>
                  <option>自定义范围</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">归档格式</label>
                <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>JSON 格式</option>
                  <option>CSV 格式</option>
                  <option>文本格式</option>
                  <option>压缩归档</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">归档后操作</label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-2">
                    <input type="radio" name="action" className="text-yellow-600 focus:ring-yellow-500" defaultChecked />
                    <span className="text-sm text-gray-700 dark:text-gray-300">保留日志</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input type="radio" name="action" className="text-yellow-600 focus:ring-yellow-500" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">归档后删除</span>
                  </label>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsArchiveModalOpen(false)}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                >
                  {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                  <span>{isLoading ? '归档中...' : '开始归档'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 清空日志确认模态框 */}
      {isClearModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">清空日志确认</h3>
              <button 
                onClick={() => setIsClearModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.AlertTriangle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">确认清空所有日志</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  此操作将永久清空所有系统日志记录，此操作不可撤销。建议在清空前先导出或归档日志。
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">清空范围</label>
                <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有日志</option>
                  <option>仅信息日志</option>
                  <option>仅错误和警告日志</option>
                  <option>7天前的日志</option>
                </select>
              </div>
              
              <div>
                <label className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-red-600 focus:ring-red-500" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">我已了解此操作的风险，并确认要清空日志</span>
                </label>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsClearModalOpen(false)}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleClearLogs}
                  disabled={isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg shadow-red-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                >
                  {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                  <span>{isLoading ? '清空中...' : '确认清空'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 日志设置模态框 */}
      {isSettingsModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">日志设置</h3>
              <button 
                onClick={() => setIsSettingsModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={(e) => { e.preventDefault(); handleSaveSettings(); }} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">日志保留期限</label>
                <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>7天</option>
                  <option>30天</option>
                  <option>90天</option>
                  <option>180天</option>
                  <option>永久保留</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">日志级别</label>
                <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>全部记录</option>
                  <option>仅记录警告和错误</option>
                  <option>仅记录错误</option>
                  <option>自定义级别</option>
                </select>
              </div>
              
              <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">自动归档</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">定期归档旧日志</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">错误通知</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">发生错误时通知管理员</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                </label>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsSettingsModalOpen(false)}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                >
                  {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                  <span>{isLoading ? '保存中...' : '保存设置'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemLogs;