import React from 'react';
import * as icons from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { useData } from '../contexts/DataContext';

interface Tab {
  id: string;
  name: string;
  icon: string;
  component: React.ComponentType;
}

interface LayoutProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ tabs, activeTab, onTabChange, children }) => {
  const { isDark, toggleTheme } = useTheme();
  const { botStatus, serverData, isConnected } = useData();

  const handleLogoClick = () => {
    onTabChange('dashboard');
  };

  const getBotStatusColor = () => {
    if (!isConnected) return 'bg-gray-400';
    if (botStatus.error) return 'bg-red-400';
    if (botStatus.ready) return 'bg-green-400';
    if (botStatus.connected) return 'bg-yellow-400';
    return 'bg-gray-400';
  };

  const getBotStatusText = () => {
    if (!isConnected) return 'WebSocket 断开';
    if (botStatus.error) return 'Bot 错误';
    if (botStatus.ready) return 'Bot 在线';
    if (botStatus.connected) return 'Bot 连接中';
    return 'Bot 离线';
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      {/* 顶部导航栏 */}
      <nav className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50 backdrop-blur-sm bg-white/95 dark:bg-gray-800/95">
        <div className="px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Logo 和标题 */}
            <div 
              className="flex items-center space-x-4 cursor-pointer hover:opacity-80 transition-opacity"
              onClick={handleLogoClick}
            >
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-2xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Bot className="w-7 h-7 text-black font-bold" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Discord Bot</h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">管理控制台</p>
              </div>
            </div>

            {/* 导航菜单 - 简化版本 */}
            <div className="flex items-center space-x-2">
              {tabs.map((tab) => {
                const Icon = icons[tab.icon as keyof typeof icons] as React.ComponentType<any>;
                return (
                  <button
                    key={tab.id}
                    onClick={() => onTabChange(tab.id)}
                    className={`group relative flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-200 ${
                      activeTab === tab.id
                        ? 'bg-yellow-400 text-black shadow-lg shadow-yellow-400/25'
                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                    title={tab.name}
                  >
                    <Icon className="w-5 h-5" />
                    
                    {/* 悬停时显示的标签 */}
                    <div className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2 px-3 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none z-50">
                      {tab.name}
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900 dark:border-b-gray-700"></div>
                    </div>
                  </button>
                );
              })}
            </div>

            {/* 右侧操作区 */}
            <div className="flex items-center space-x-4">
              {/* Bot 状态指示器 */}
              <div className="flex items-center space-x-3 px-4 py-2 bg-gray-50 dark:bg-gray-700 rounded-xl">
                <div className={`w-3 h-3 ${getBotStatusColor()} rounded-full ${botStatus.ready ? 'animate-pulse' : ''} shadow-lg`}></div>
                <div className="hidden md:block">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300">{getBotStatusText()}</div>
                  {botStatus.ready && (
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {serverData.memberCount} 成员 • {botStatus.latency}ms
                    </div>
                  )}
                </div>
              </div>

              {/* WebSocket 连接状态 */}
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`} title={isConnected ? 'WebSocket 已连接' : 'WebSocket 断开'}></div>

              {/* 主题切换 */}
              <button
                onClick={toggleTheme}
                className="w-12 h-12 rounded-xl flex items-center justify-center text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200"
                title={isDark ? '切换到亮色模式' : '切换到暗色模式'}
              >
                {isDark ? <icons.Sun className="w-5 h-5" /> : <icons.Moon className="w-5 h-5" />}
              </button>

              {/* 用户头像 */}
              <div className="w-12 h-12 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl flex items-center justify-center shadow-lg cursor-pointer hover:shadow-xl transition-all duration-200">
                <span className="text-white font-bold text-sm">管</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* 面包屑导航 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-100 dark:border-gray-700">
        <div className="px-6 lg:px-8 py-4">
          <div className="flex items-center space-x-2 text-sm">
            <icons.Home className="w-4 h-4 text-gray-400" />
            <span className="text-gray-400">/</span>
            <span className="font-medium text-yellow-600 dark:text-yellow-400">
              {tabs.find(tab => tab.id === activeTab)?.name || '控制台'}
            </span>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <main className="p-6 lg:p-8">
        {children}
      </main>
    </div>
  );
};

export default Layout;