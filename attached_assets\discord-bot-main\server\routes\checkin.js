import express from 'express';
import { db } from '../database/init.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Get checkin statistics
router.get('/stats', async (req, res) => {
  try {
    const today = new Date().toISOString().split('T')[0];
    
    // Today's checkins
    const todayCheckins = await db.getAsync(`
      SELECT COUNT(*) as count FROM checkin_records 
      WHERE checkin_date = ?
    `, [today]);

    // Total checkins
    const totalCheckins = await db.getAsync(`
      SELECT COUNT(*) as count FROM checkin_records
    `);

    // Average streak
    const avgStreak = await db.getAsync(`
      SELECT AVG(checkin_streak) as avg FROM users 
      WHERE checkin_streak > 0
    `);

    // Top streaks
    const topStreaks = await db.allAsync(`
      SELECT username, checkin_streak 
      FROM users 
      WHERE checkin_streak > 0 
      ORDER BY checkin_streak DESC 
      LIMIT 10
    `);

    res.json({
      success: true,
      data: {
        todayCheckins: todayCheckins.count,
        totalCheckins: totalCheckins.count,
        averageStreak: Math.round(avgStreak.avg || 0),
        topStreaks
      }
    });
  } catch (error) {
    logger.error('Error getting checkin stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get checkin statistics'
    });
  }
});

// Get checkin history
router.get('/history', async (req, res) => {
  try {
    const { page = 1, limit = 50, userId = '' } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT cr.*, u.username 
      FROM checkin_records cr
      LEFT JOIN users u ON cr.user_id = u.id
      WHERE 1=1
    `;
    let params = [];

    if (userId) {
      query += ' AND cr.user_id = ?';
      params.push(userId);
    }

    query += ' ORDER BY cr.created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const history = await db.allAsync(query, params);

    res.json({
      success: true,
      data: history
    });
  } catch (error) {
    logger.error('Error getting checkin history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get checkin history'
    });
  }
});

export default router;