import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import { logger } from '../utils/logger.js';
import { mkdirSync } from 'fs';

// Ensure data directory exists
try {
  mkdirSync('./data', { recursive: true });
} catch (error) {
  // Directory already exists
}

const db = new sqlite3.Database('./data/bot.db');

// Promisify database methods
db.runAsync = promisify(db.run.bind(db));
db.getAsync = promisify(db.get.bind(db));
db.allAsync = promisify(db.all.bind(db));

export async function initDatabase() {
  try {
    // Create tables
    await createTables();
    logger.info('Database tables created successfully');
  } catch (error) {
    logger.error('Database initialization failed:', error);
    throw error;
  }
}

async function createTables() {
  // Users table
  await db.runAsync(`
    CREATE TABLE IF NOT EXISTS users (
      id TEXT PRIMARY KEY,
      username TEXT NOT NULL,
      discriminator TEXT,
      avatar TEXT,
      points INTEGER DEFAULT 0,
      title TEXT DEFAULT '新手',
      join_date DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_active DATETIME DEFAULT CURRENT_TIMESTAMP,
      message_count INTEGER DEFAULT 0,
      voice_time INTEGER DEFAULT 0,
      checkin_streak INTEGER DEFAULT 0,
      last_checkin DATE,
      status TEXT DEFAULT 'offline',
      roles TEXT DEFAULT '[]',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Points history table
  await db.runAsync(`
    CREATE TABLE IF NOT EXISTS points_history (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL,
      amount INTEGER NOT NULL,
      reason TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('earned', 'deducted', 'admin')),
      admin_id TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    )
  `);

  // Checkin records table
  await db.runAsync(`
    CREATE TABLE IF NOT EXISTS checkin_records (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id TEXT NOT NULL,
      checkin_date DATE NOT NULL,
      points_earned INTEGER NOT NULL,
      streak_bonus INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id),
      UNIQUE(user_id, checkin_date)
    )
  `);

  // Titles table
  await db.runAsync(`
    CREATE TABLE IF NOT EXISTS titles (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      description TEXT,
      min_points INTEGER NOT NULL,
      max_points INTEGER,
      color TEXT DEFAULT '#6B7280',
      icon TEXT,
      is_active BOOLEAN DEFAULT 1,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Settings table
  await db.runAsync(`
    CREATE TABLE IF NOT EXISTS settings (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      type TEXT DEFAULT 'string',
      description TEXT,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // System logs table
  await db.runAsync(`
    CREATE TABLE IF NOT EXISTS system_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      level TEXT NOT NULL CHECK (level IN ('info', 'warning', 'error', 'success')),
      module TEXT NOT NULL,
      action TEXT NOT NULL,
      details TEXT,
      user_id TEXT,
      ip_address TEXT,
      user_agent TEXT,
      duration INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Insert default titles
  await insertDefaultTitles();
  
  // Insert default settings
  await insertDefaultSettings();
}

async function insertDefaultTitles() {
  const defaultTitles = [
    { name: '新手', description: '刚加入的新成员', min_points: 0, max_points: 99, color: '#6B7280' },
    { name: '初级成员', description: '开始活跃的成员', min_points: 100, max_points: 499, color: '#10B981' },
    { name: '活跃成员', description: '经常参与讨论的成员', min_points: 500, max_points: 1499, color: '#3B82F6' },
    { name: '高级成员', description: '社区的重要成员', min_points: 1500, max_points: 4999, color: '#8B5CF6' },
    { name: '核心成员', description: '社区的核心力量', min_points: 5000, max_points: null, color: '#F59E0B' }
  ];

  for (const title of defaultTitles) {
    try {
      await db.runAsync(`
        INSERT OR IGNORE INTO titles (name, description, min_points, max_points, color)
        VALUES (?, ?, ?, ?, ?)
      `, [title.name, title.description, title.min_points, title.max_points, title.color]);
    } catch (error) {
      // Ignore duplicate entries
    }
  }
}

async function insertDefaultSettings() {
  const defaultSettings = [
    { key: 'bot_token', value: '', type: 'string', description: 'Discord Bot Token' },
    { key: 'bot_prefix', value: '!', type: 'string', description: 'Bot Command Prefix' },
    { key: 'checkin_points', value: '10', type: 'number', description: 'Daily Checkin Points' },
    { key: 'message_points', value: '1', type: 'number', description: 'Points per Message' },
    { key: 'invite_points', value: '50', type: 'number', description: 'Points for Inviting Users' },
    { key: 'auto_backup', value: 'true', type: 'boolean', description: 'Enable Auto Backup' },
    { key: 'error_notifications', value: 'true', type: 'boolean', description: 'Enable Error Notifications' },
    { key: 'debug_mode', value: 'false', type: 'boolean', description: 'Enable Debug Mode' }
  ];

  for (const setting of defaultSettings) {
    try {
      await db.runAsync(`
        INSERT OR IGNORE INTO settings (key, value, type, description)
        VALUES (?, ?, ?, ?)
      `, [setting.key, setting.value, setting.type, setting.description]);
    } catch (error) {
      // Ignore duplicate entries
    }
  }
}

export { db };