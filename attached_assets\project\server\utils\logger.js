import winston from 'winston';
import { db } from '../database/init.js';
import { mkdirSync } from 'fs';

// Ensure logs directory exists
try {
  mkdirSync('./logs', { recursive: true });
} catch (error) {
  // Directory already exists
}

// Create logger
export const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'discord-bot' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Custom log function that also saves to database
export async function logToDatabase(level, module, action, details, userId = null, ipAddress = null, userAgent = null, duration = null) {
  try {
    await db.runAsync(`
      INSERT INTO system_logs (level, module, action, details, user_id, ip_address, user_agent, duration)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [level, module, action, details, userId, ipAddress, userAgent, duration]);
  } catch (error) {
    logger.error('Failed to log to database:', error);
  }
}