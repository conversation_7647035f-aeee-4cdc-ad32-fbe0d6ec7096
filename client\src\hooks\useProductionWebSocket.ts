import { useState, useEffect, useRef, useCallback } from 'react';

interface WebSocketData {
  type: string;
  data: any;
  timestamp?: string;
}

// Production-ready WebSocket with enhanced fallback
export function useProductionWebSocket() {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<WebSocketData | null>(null);
  const ws = useRef<WebSocket | null>(null);
  const pollingInterval = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 10;
  const heartbeatInterval = useRef<NodeJS.Timeout | null>(null);
  
  // Detect production environment
  const isProduction = window.location.hostname.includes('.replit.dev') || 
                      window.location.hostname.includes('.replit.app') || 
                      window.location.protocol === 'https:';

  // Enhanced production polling with robust error handling
  const startPolling = useCallback(() => {
    if (pollingInterval.current) return;
    
    console.log('🏭 Starting production polling mode...');
    
    const poll = async () => {
      if (!mountedRef.current) return;
      
      try {
        // Use Promise.allSettled for better error resilience
        const results = await Promise.allSettled([
          fetch('/api/discord/server-stats', { 
            signal: AbortSignal.timeout(8000),
            cache: 'no-cache'
          }),
          fetch('/api/discord/bot-status', { 
            signal: AbortSignal.timeout(8000),
            cache: 'no-cache'
          }),
          fetch('/api/stats', { 
            signal: AbortSignal.timeout(8000),
            cache: 'no-cache'
          })
        ]);
        
        // Process successful responses
        for (let i = 0; i < results.length; i++) {
          const result = results[i];
          if (result.status === 'fulfilled' && result.value.ok) {
            try {
              const data = await result.value.json();
              
              if (i === 0 && data.success) { // server-stats
                setLastMessage({
                  type: 'serverData',
                  data: data.data,
                  timestamp: new Date().toISOString()
                });
              } else if (i === 1 && data.success) { // bot-status
                setLastMessage({
                  type: 'botStatus',
                  data: data.data,
                  timestamp: new Date().toISOString()
                });
              } else if (i === 2 && data.userCount !== undefined) { // stats
                setLastMessage({
                  type: 'stats',
                  data: data,
                  timestamp: new Date().toISOString()
                });
              }
            } catch (parseError) {
              console.warn(`Failed to parse response ${i}:`, parseError);
            }
          }
        }
        
        setIsConnected(true);
      } catch (error) {
        // Only log meaningful errors to reduce console noise
        if (error instanceof Error && error.message) {
          console.error('Production polling error:', error.message);
        }
        setIsConnected(false);
      }
    };
    
    // Initial poll
    poll();
    
    // Set up interval polling
    pollingInterval.current = setInterval(poll, 3000);
  }, []);

  const stopPolling = useCallback(() => {
    if (pollingInterval.current) {
      clearInterval(pollingInterval.current);
      pollingInterval.current = null;
      console.log('🛑 Stopped production polling');
    }
  }, []);

  // Enhanced WebSocket with heartbeat and exponential backoff
  const connectWebSocket = useCallback(() => {
    if (ws.current?.readyState === WebSocket.OPEN) return;
    
    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    
    console.log(`🔌 WebSocket attempt ${reconnectAttempts.current + 1}/${maxReconnectAttempts}`);
    
    ws.current = new WebSocket(wsUrl);
    
    ws.current.onopen = () => {
      console.log('✅ WebSocket connected successfully');
      setIsConnected(true);
      reconnectAttempts.current = 0; // Reset on successful connection
      stopPolling(); // Stop polling when WebSocket works
      
      // Start heartbeat
      heartbeatInterval.current = setInterval(() => {
        if (ws.current?.readyState === WebSocket.OPEN) {
          ws.current.send(JSON.stringify({ type: 'ping' }));
        }
      }, 30000); // Heartbeat every 30 seconds
    };
    
    ws.current.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        // Handle heartbeat pong
        if (data.type === 'pong') {
          console.log('💓 WebSocket heartbeat received');
          return;
        }
        
        console.log('📨 WebSocket message:', data.type);
        setLastMessage(data);
      } catch (error) {
        console.error('WebSocket message parse error:', error);
      }
    };
    
    ws.current.onclose = (event) => {
      console.log('📡 WebSocket disconnected, code:', event.code);
      setIsConnected(false);
      
      // Clear heartbeat
      if (heartbeatInterval.current) {
        clearInterval(heartbeatInterval.current);
        heartbeatInterval.current = null;
      }
      
      // Attempt reconnection with exponential backoff
      if (reconnectAttempts.current < maxReconnectAttempts && mountedRef.current) {
        const backoffDelay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
        console.log(`⏳ Reconnecting in ${backoffDelay}ms...`);
        
        setTimeout(() => {
          if (mountedRef.current) {
            reconnectAttempts.current++;
            connectWebSocket();
          }
        }, backoffDelay);
      } else {
        console.log('🔄 Max reconnection attempts reached, falling back to polling');
        startPolling();
      }
    };
    
    ws.current.onerror = (error) => {
      console.error('WebSocket error:', error);
      setIsConnected(false);
    };
  }, [startPolling, stopPolling]);

  // Initialize connection
  useEffect(() => {
    mountedRef.current = true;
    
    if (isProduction) {
      console.log('🏭 Production mode - starting with polling fallback');
      startPolling();
      // Also try WebSocket, but don't depend on it
      setTimeout(connectWebSocket, 1000);
    } else {
      console.log('🛠️ Development mode - WebSocket primary');
      connectWebSocket();
    }
    
    return () => {
      mountedRef.current = false;
      stopPolling();
      
      // Clean up heartbeat
      if (heartbeatInterval.current) {
        clearInterval(heartbeatInterval.current);
        heartbeatInterval.current = null;
      }
      
      // Clean up WebSocket
      if (ws.current) {
        ws.current.close();
        ws.current = null;
      }
      
      console.log('🧹 WebSocket hook cleanup completed');
    };
  }, [isProduction, startPolling, connectWebSocket]);

  return {
    isConnected,
    lastMessage,
    isProduction
  };
}