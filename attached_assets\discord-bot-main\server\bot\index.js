import { Client, GatewayIntentBits, Collection } from 'discord.js';
import { logger } from '../utils/logger.js';
import { getDb } from '../database/init.js';
import { getSetting } from '../utils/settings.js';
import { broadcastEvent } from '../websocket/index.js';

// Import event handlers
import { handleReady } from './events/ready.js';
import { handleGuildMemberAdd } from './events/guildMemberAdd.js';
import { handleMessageCreate } from './events/messageCreate.js';
import { handleInteractionCreate } from './events/interactionCreate.js';

// Import commands
import { registerCommands } from './commands/index.js';

let client = null;
let botStatus = {
  connected: false,
  ready: false,
  latency: 0,
  uptime: 0,
  guilds: 0,
  users: 0,
  error: null
};

export async function initBot() {
  try {
    const token = await getSetting('bot_token');
    
    if (!token || token.trim() === '') {
      logger.warn('No bot token found, bot will not start');
      botStatus.error = 'No bot token configured';
      return;
    }

    await startBot(token);
  } catch (error) {
    logger.error('Failed to initialize bot:', error);
    botStatus.error = error.message;
    try {
      broadcastEvent('bot_error', { error: error.message });
    } catch (broadcastError) {
      logger.error('Failed to broadcast bot error:', broadcastError);
    }
  }
}

export async function startBot(token) {
  try {
    if (!token || token.trim() === '') {
      throw new Error('Bot token is required');
    }

    if (client) {
      await stopBot();
    }

    client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildPresences
      ]
    });

    // Initialize commands collection
    client.commands = new Collection();

    // Register event handlers
    client.once('ready', () => {
      handleReady(client, botStatus);
      try {
        broadcastEvent('bot_ready', { 
          botTag: client.user.tag,
          guilds: client.guilds.cache.size,
          users: client.users.cache.size
        });
      } catch (broadcastError) {
        logger.error('Failed to broadcast bot ready:', broadcastError);
      }
      
      // Start syncing users from Discord after a delay
      setTimeout(() => {
        syncDiscordUsers().catch(error => {
          logger.error('Error in initial user sync:', error);
        });
      }, 5000);
    });
    
    client.on('guildMemberAdd', (member) => {
      handleGuildMemberAdd(member);
      try {
        broadcastEvent('member_joined', {
          username: member.user.username,
          id: member.id
        });
      } catch (broadcastError) {
        logger.error('Failed to broadcast member joined:', broadcastError);
      }
    });
    
    client.on('messageCreate', (message) => {
      handleMessageCreate(message);
      if (!message.author.bot) {
        try {
          broadcastEvent('message_sent', {
            username: message.author.username,
            content: message.content.substring(0, 100),
            channel: message.channel.name
          });
        } catch (broadcastError) {
          logger.error('Failed to broadcast message sent:', broadcastError);
        }
      }
    });
    
    client.on('interactionCreate', (interaction) => handleInteractionCreate(interaction));

    // Error handling
    client.on('error', (error) => {
      logger.error('Discord client error:', error);
      botStatus.error = error.message;
      botStatus.connected = false;
      botStatus.ready = false;
      try {
        broadcastEvent('bot_error', { error: error.message });
      } catch (broadcastError) {
        logger.error('Failed to broadcast bot error:', broadcastError);
      }
    });

    client.on('disconnect', () => {
      logger.warn('Discord client disconnected');
      botStatus.connected = false;
      botStatus.ready = false;
      try {
        broadcastEvent('bot_disconnected', {});
      } catch (broadcastError) {
        logger.error('Failed to broadcast bot disconnected:', broadcastError);
      }
    });

    // Register slash commands
    await registerCommands(client);

    // Login to Discord
    await client.login(token);
    
    botStatus.connected = true;
    botStatus.error = null;
    
    logger.info('Bot login successful');
    
    return client;
  } catch (error) {
    logger.error('Failed to start bot:', error);
    botStatus.error = error.message;
    botStatus.connected = false;
    botStatus.ready = false;
    try {
      broadcastEvent('bot_error', { error: error.message });
    } catch (broadcastError) {
      logger.error('Failed to broadcast bot error:', broadcastError);
    }
    throw error;
  }
}

export async function stopBot() {
  try {
    if (client) {
      await client.destroy();
      client = null;
      botStatus.connected = false;
      botStatus.ready = false;
      botStatus.error = null;
      logger.info('Bot stopped successfully');
      try {
        broadcastEvent('bot_stopped', {});
      } catch (broadcastError) {
        logger.error('Failed to broadcast bot stopped:', broadcastError);
      }
    }
  } catch (error) {
    logger.error('Failed to stop bot:', error);
    throw error;
  }
}

export async function restartBot() {
  try {
    const token = await getSetting('bot_token');
    if (!token || token.trim() === '') {
      throw new Error('No bot token configured');
    }

    await stopBot();
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    await startBot(token);
    
    logger.info('Bot restarted successfully');
    try {
      broadcastEvent('bot_restarted', {});
    } catch (broadcastError) {
      logger.error('Failed to broadcast bot restarted:', broadcastError);
    }
  } catch (error) {
    logger.error('Failed to restart bot:', error);
    throw error;
  }
}

export function getBotStatus() {
  if (client && client.isReady()) {
    botStatus.latency = client.ws.ping;
    botStatus.uptime = client.uptime;
    botStatus.guilds = client.guilds.cache.size;
    botStatus.users = client.users.cache.size;
  }
  
  return botStatus;
}

export function getClient() {
  return client;
}

// Sync Discord users to database
export async function syncDiscordUsers() {
  if (!client || !client.isReady()) {
    throw new Error('Bot is not ready or connected');
  }
  
  try {
    const guild = client.guilds.cache.first();
    if (!guild) {
      throw new Error('Bot is not in any guilds');
    }

    await guild.members.fetch();
    const db = getDb();
    let syncedCount = 0;
    
    for (const [memberId, member] of guild.members.cache) {
      if (member.user.bot) continue;

      try {
        await db.run(`
          INSERT OR REPLACE INTO users (
            id, username, discriminator, avatar, join_date, last_active, status, roles, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          member.id,
          member.user.username,
          member.user.discriminator || '0',
          member.user.avatar,
          member.joinedAt?.toISOString() || new Date().toISOString(),
          new Date().toISOString(),
          member.presence?.status || 'offline',
          JSON.stringify(member.roles.cache.map(role => role.name)),
          new Date().toISOString()
        ]);
        syncedCount++;
      } catch (dbError) {
        logger.error(`Error syncing user ${member.user.username}:`, dbError);
      }
    }

    logger.info(`Synced ${syncedCount} users from Discord`);
    try {
      broadcastEvent('users_synced', { count: syncedCount });
    } catch (broadcastError) {
      logger.error('Failed to broadcast users synced:', broadcastError);
    }
    
    return syncedCount;
  } catch (error) {
    logger.error('Error syncing Discord users:', error);
    throw error;
  }
}

// Auto-sync users every 10 minutes
setInterval(() => {
  if (client && client.isReady()) {
    syncDiscordUsers().catch(error => {
      logger.error('Error in auto-sync:', error);
    });
  }
}, 10 * 60 * 1000);