import React, { useState } from 'react';
import * as icons from 'lucide-react';
import { useData } from '../contexts/DataContext';

const FunctionTesting: React.FC = () => {
  const [isTestModalOpen, setIsTestModalOpen] = useState(false);
  const [selectedTest, setSelectedTest] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<{[key: string]: any}>({});
  const { botStatus } = useData();

  const testCategories = [
    {
      id: 'automation',
      name: '自动化规则测试',
      icon: 'Workflow',
      description: '测试自动化规则的触发和执行',
      color: 'from-purple-500 to-purple-600',
      tests: [
        {
          id: 'user_join_test',
          name: '用户加入测试',
          description: '模拟新用户加入服务器，测试欢迎消息和角色分配',
          status: 'ready',
          lastRun: null,
          duration: '2.3s'
        },
        {
          id: 'message_points_test',
          name: '消息积分测试',
          description: '模拟用户发送消息，测试积分奖励系统',
          status: 'passed',
          lastRun: '2024-03-01 14:30',
          duration: '0.8s'
        },
        {
          id: 'violation_detection_test',
          name: '违规检测测试',
          description: '模拟违规内容，测试自动删除和警告功能',
          status: 'failed',
          lastRun: '2024-03-01 12:15',
          duration: '1.2s'
        }
      ]
    },
    {
      id: 'checkin',
      name: '签到系统测试',
      icon: 'Calendar',
      description: '测试签到功能和奖励机制',
      color: 'from-green-500 to-green-600',
      tests: [
        {
          id: 'daily_checkin_test',
          name: '每日签到测试',
          description: '模拟用户签到，验证积分发放和连续天数计算',
          status: 'passed',
          lastRun: '2024-03-01 09:15',
          duration: '1.5s'
        },
        {
          id: 'consecutive_bonus_test',
          name: '连续签到奖励测试',
          description: '测试连续签到的额外奖励机制',
          status: 'ready',
          lastRun: null,
          duration: '2.1s'
        },
        {
          id: 'duplicate_checkin_test',
          name: '重复签到测试',
          description: '验证重复签到的防护机制',
          status: 'passed',
          lastRun: '2024-03-01 08:45',
          duration: '0.5s'
        }
      ]
    },
    {
      id: 'points',
      name: '积分系统测试',
      icon: 'Star',
      description: '测试积分发放、扣除和计算',
      color: 'from-yellow-400 to-yellow-500',
      tests: [
        {
          id: 'points_award_test',
          name: '积分发放测试',
          description: '测试手动和自动积分发放功能',
          status: 'passed',
          lastRun: '2024-03-01 11:20',
          duration: '1.0s'
        },
        {
          id: 'points_deduction_test',
          name: '积分扣除测试',
          description: '测试违规行为的积分扣除机制',
          status: 'ready',
          lastRun: null,
          duration: '0.8s'
        },
        {
          id: 'points_calculation_test',
          name: '积分计算测试',
          description: '验证积分计算的准确性和一致性',
          status: 'passed',
          lastRun: '2024-03-01 10:30',
          duration: '0.3s'
        }
      ]
    },
    {
      id: 'titles',
      name: '头衔系统测试',
      icon: 'Crown',
      description: '测试头衔晋升和权限分配',
      color: 'from-orange-500 to-orange-600',
      tests: [
        {
          id: 'title_upgrade_test',
          name: '头衔晋升测试',
          description: '模拟积分达到阈值，测试自动头衔晋升',
          status: 'ready',
          lastRun: null,
          duration: '1.8s'
        },
        {
          id: 'title_notification_test',
          name: '晋升通知测试',
          description: '测试头衔晋升时的通知消息发送',
          status: 'passed',
          lastRun: '2024-03-01 13:45',
          duration: '0.9s'
        }
      ]
    },
    {
      id: 'lottery',
      name: '抽奖系统测试',
      icon: 'Gift',
      description: '测试抽奖功能和奖品发放',
      color: 'from-pink-500 to-pink-600',
      tests: [
        {
          id: 'lottery_draw_test',
          name: '抽奖执行测试',
          description: '模拟抽奖过程，验证随机性和公平性',
          status: 'ready',
          lastRun: null,
          duration: '2.5s'
        },
        {
          id: 'prize_distribution_test',
          name: '奖品发放测试',
          description: '测试中奖后的奖品发放机制',
          status: 'failed',
          lastRun: '2024-02-29 16:20',
          duration: '3.2s'
        }
      ]
    },
    {
      id: 'commands',
      name: 'Slash 指令测试',
      icon: 'Terminal',
      description: '测试 Slash 指令的响应和执行',
      color: 'from-blue-500 to-blue-600',
      tests: [
        {
          id: 'command_response_test',
          name: '指令响应测试',
          description: '测试各种 Slash 指令的响应速度和准确性',
          status: 'passed',
          lastRun: '2024-03-01 15:10',
          duration: '0.4s'
        },
        {
          id: 'command_permission_test',
          name: '指令权限测试',
          description: '验证指令权限控制和频道限制',
          status: 'ready',
          lastRun: null,
          duration: '0.6s'
        },
        {
          id: 'help_command_test',
          name: '帮助指令测试',
          description: '测试 /帮助 指令的显示和格式',
          status: 'ready',
          lastRun: null,
          duration: '0.3s'
        }
      ]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400';
      case 'failed': return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400';
      case 'running': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400';
      case 'ready': return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
      default: return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'passed': return '✅ 通过';
      case 'failed': return '❌ 失败';
      case 'running': return '🔄 运行中';
      case 'ready': return '⚡ 就绪';
      default: return '⚪ 未知';
    }
  };

  const runTest = async (test: any) => {
    if (!botStatus.ready) {
      alert('Bot 未连接，无法执行测试');
      return;
    }

    setIsLoading(true);
    setSelectedTest(test);
    
    try {
      // 模拟测试执行
      const response = await fetch('/api/test/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          testId: test.id,
          category: test.category
        })
      });
      
      const result = await response.json();
      
      setTestResults(prev => ({
        ...prev,
        [test.id]: result
      }));
      
      // 更新测试状态
      test.status = result.success ? 'passed' : 'failed';
      test.lastRun = new Date().toLocaleString('zh-CN');
      test.duration = result.duration || '1.0s';
      
    } catch (error) {
      console.error('Test execution failed:', error);
      test.status = 'failed';
      test.lastRun = new Date().toLocaleString('zh-CN');
    } finally {
      setIsLoading(false);
    }
  };

  const runAllTests = async (category: any) => {
    if (!botStatus.ready) {
      alert('Bot 未连接，无法执行测试');
      return;
    }

    setIsLoading(true);
    
    for (const test of category.tests) {
      await runTest({ ...test, category: category.id });
      // 添加短暂延迟避免过快执行
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                <icons.TestTube className="w-8 h-8 text-white font-bold" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">功能测试</h1>
                <p className="text-gray-500 dark:text-gray-400">测试 Discord Bot 的各项功能和系统组件</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className={`px-4 py-2 rounded-xl ${botStatus.ready ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400' : 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400'}`}>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${botStatus.ready ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className="text-sm font-medium">
                    {botStatus.ready ? 'Bot 在线' : 'Bot 离线'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Test Categories Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {testCategories.map((category) => {
            const IconComponent = (icons as any)[category.icon];
            const passedTests = category.tests.filter(test => test.status === 'passed').length;
            const totalTests = category.tests.length;
            const progress = (passedTests / totalTests) * 100;

            return (
              <div key={category.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                {/* Category Header */}
                <div className={`bg-gradient-to-r ${category.color} p-6 text-white`}>
                  <div className="flex items-center justify-between mb-3">
                    <IconComponent className="w-8 h-8" />
                    <button
                      onClick={() => runAllTests(category)}
                      disabled={isLoading || !botStatus.ready}
                      className="bg-white/20 hover:bg-white/30 px-3 py-1 rounded-lg text-sm font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isLoading ? '运行中...' : '全部测试'}
                    </button>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{category.name}</h3>
                  <p className="text-white/80 text-sm mb-4">{category.description}</p>
                  
                  {/* Progress Bar */}
                  <div className="bg-white/20 rounded-full h-2 mb-2">
                    <div 
                      className="bg-white h-2 rounded-full transition-all duration-500"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                  <div className="text-white/80 text-sm">
                    {passedTests}/{totalTests} 测试通过
                  </div>
                </div>

                {/* Test List */}
                <div className="p-6 space-y-4">
                  {category.tests.map((test) => (
                    <div key={test.id} className="border border-gray-200 dark:border-gray-700 rounded-xl p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-gray-900 dark:text-white">{test.name}</h4>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-lg text-xs font-medium ${getStatusColor(test.status)}`}>
                            {getStatusText(test.status)}
                          </span>
                          <button
                            onClick={() => runTest({ ...test, category: category.id })}
                            disabled={isLoading || !botStatus.ready}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <icons.Play className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">{test.description}</p>
                      
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span>
                          {test.lastRun ? `上次运行: ${test.lastRun}` : '尚未运行'}
                        </span>
                        <span>
                          预估时长: {test.duration}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>

        {/* Bot Command Reference */}
        <div className="mt-8 bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <icons.Terminal className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">可用 Slash 指令</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="border border-gray-200 dark:border-gray-700 rounded-xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <icons.Calendar className="w-5 h-5 text-green-600 dark:text-green-400" />
                <code className="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/签到</code>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm">每日签到获取积分奖励</p>
            </div>
            
            <div className="border border-gray-200 dark:border-gray-700 rounded-xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <icons.Star className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
                <code className="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/积分</code>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm">查看个人积分和排名</p>
            </div>
            
            <div className="border border-gray-200 dark:border-gray-700 rounded-xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <icons.Trophy className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                <code className="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/排行榜</code>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm">查看积分排行榜</p>
            </div>
            
            <div className="border border-gray-200 dark:border-gray-700 rounded-xl p-4">
              <div className="flex items-center space-x-2 mb-2">
                <icons.HelpCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                <code className="text-sm font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/帮助</code>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-sm">显示所有可用指令</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FunctionTesting;