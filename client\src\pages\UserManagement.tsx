import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import * as icons from 'lucide-react';

const UserManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);
  const [isKickModalOpen, setIsKickModalOpen] = useState(false);
  const [isBatchInviteModalOpen, setIsBatchInviteModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isPermissionModalOpen, setIsPermissionModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const [syncProgress, setSyncProgress] = useState(0);
  const [exportProgress, setExportProgress] = useState(0);
  // Form data state
  const [editFormData, setEditFormData] = useState({
    username: '',
    points: 0,
    titles: [] as string[]
  });

  const queryClient = useQueryClient();

  // Fetch Discord users from API
  const { data: usersResponse, isLoading: usersLoading, refetch } = useQuery({
    queryKey: ['/api/users'],
    queryFn: async () => {
      const response = await fetch('/api/users?' + new Date().getTime());
      if (!response.ok) throw new Error('Failed to fetch users');
      return response.json();
    },
    refetchOnWindowFocus: true,
    staleTime: 0,
    gcTime: 0,
    refetchInterval: 5000
  });

  // Fetch Discord server statistics
  const { data: serverStatsResponse } = useQuery({
    queryKey: ['/api/discord/server-stats'],
    queryFn: async () => {
      const response = await fetch('/api/discord/server-stats');
      if (!response.ok) throw new Error('Failed to fetch server stats');
      return response.json();
    }
  });

  // Fetch titles for dropdown menu
  const { data: titlesResponse } = useQuery({
    queryKey: ['/api/titles'],
    queryFn: async () => {
      const response = await fetch('/api/titles');
      if (!response.ok) throw new Error('Failed to fetch titles');
      return response.json();
    },
    refetchOnWindowFocus: true,
    staleTime: 0
  });

  // Discord user sync mutation
  const syncUsersMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/users/sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      if (!response.ok) throw new Error('Failed to sync users');
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        setOperationResult({ type: 'success', message: data.message });
        setTimeout(() => setOperationResult(null), 3000);
        queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      }
    }
  });

  // Discord role sync mutation
  const syncRolesMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/users/sync-discord-roles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      if (!response.ok) throw new Error('Failed to sync roles');
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        setOperationResult({ type: 'success', message: `Discord角色同步完成：${data.message}` });
        setTimeout(() => setOperationResult(null), 3000);
        queryClient.invalidateQueries({ queryKey: ['/api/users'] });
        refetch(); // Force immediate refresh
      }
    }
  });

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: async (data: { userId: number; username?: string; points?: number; titles?: string[] }) => {
      const response = await fetch(`/api/users/${data.userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: data.username,
          points: data.points,
          titles: data.titles
        })
      });
      if (!response.ok) throw new Error('Failed to update user');
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        setOperationResult({ type: 'success', message: data.message });
        setIsEditModalOpen(false);
        setSelectedUser(null);
        setTimeout(() => setOperationResult(null), 3000);
        // Refresh all related data
        queryClient.invalidateQueries({ queryKey: ['/api/users'] });
        queryClient.invalidateQueries({ queryKey: ['/api/point-records'] });
        queryClient.invalidateQueries({ queryKey: ['/api/points/stats'] });
        queryClient.invalidateQueries({ queryKey: ['/api/discord-users'] });
      }
    },
    onError: (error) => {
      setOperationResult({ type: 'error', message: '更新用户失败' });
      setTimeout(() => setOperationResult(null), 3000);
    }
  });

  // Auto-sync on component mount
  useEffect(() => {
    const autoSync = async () => {
      try {
        await syncUsersMutation.mutateAsync();
      } catch (error) {
        console.log('Auto-sync completed');
      }
    };
    
    const timer = setTimeout(autoSync, 500);
    return () => clearTimeout(timer);
  }, []);

  // Filter to show only Discord users (users with discord_id)
  const users = (usersResponse?.data || []).filter((user: any) => user.discordId);
  const serverStats = serverStatsResponse?.data || {
    totalMembers: 0,
    onlineMembers: 0,
    premiumMembers: 0,
    memberGrowth: 0
  };
  const titles = titlesResponse?.data || [];

  // Calculate real progress percentages
  const serverCapacityProgress = Math.min((serverStats.totalMembers / 5000) * 100, 100); // Discord server max capacity
  const activityProgress = serverStats.totalMembers > 0 ? (serverStats.onlineMembers / serverStats.totalMembers) * 100 : 0;
  const vipProgress = serverStats.totalMembers > 0 ? (serverStats.premiumMembers / serverStats.totalMembers) * 100 : 0;

  const filteredUsers = users.filter((user: any) => 
    user.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-400';
      case 'idle': return 'bg-yellow-400';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const getTitleColor = (title: string) => {
    const titleData = titles.find((t: any) => t.name === title);
    if (titleData) {
      // 使用头衔数据库中的颜色
      return {
        backgroundColor: titleData.color,
        color: getContrastColor(titleData.color)
      };
    }
    // 默认颜色
    return {
      backgroundColor: '#6B7280',
      color: '#FFFFFF'
    };
  };

  // 根据背景色计算对比色
  const getContrastColor = (hexColor: string) => {
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 155 ? '#000000' : '#FFFFFF';
  };

  const handleEditUser = (user: any) => {
    setSelectedUser(user);
    setEditFormData({
      username: user.username || '',
      points: user.points || 0,
      titles: user.titles || []
    });
    setIsEditModalOpen(true);
  };

  const handleSendMessage = (user: any) => {
    setSelectedUser(user);
    setIsMessageModalOpen(true);
  };

  const handleKickUser = (user: any) => {
    setSelectedUser(user);
    setIsKickModalOpen(true);
  };

  const handleSyncUsers = () => {
    setIsLoading(true);
    setSyncProgress(0);
    
    const interval = setInterval(() => {
      setSyncProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          syncUsersMutation.mutate();
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const handleFormSubmit = (type: string, formData?: any) => {
    if (type === 'edit' && selectedUser && formData) {
      // Use the real API for user editing
      updateUserMutation.mutate({
        userId: selectedUser.id,
        username: formData.username,
        points: parseInt(formData.points) || 0,
        titles: formData.titles
      });
    } else {
      // Keep the simulation for other forms
      setIsLoading(true);
      setTimeout(() => {
        setIsLoading(false);
        setOperationResult({
          type: 'success',
          message: type === 'message' ? '私信发送成功！' : 
                  type === 'kick' ? '用户已被踢出服务器！' : 
                  type === 'batch' ? '批量邀请已发送！' : 
                  type === 'permission' ? '权限设置已更新！' : '操作完成！'
        });
        
        setTimeout(() => {
          setOperationResult(null);
          if (type === 'message') setIsMessageModalOpen(false);
          if (type === 'kick') setIsKickModalOpen(false);
          if (type === 'batch') setIsBatchInviteModalOpen(false);
          if (type === 'permission') setIsPermissionModalOpen(false);
        }, 2000);
      }, 1500);
    }
  };

  const handleExport = () => {
    setIsExportModalOpen(true);
    setExportProgress(0);
    setIsLoading(true);
    
    const interval = setInterval(() => {
      setExportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          return 100;
        }
        return prev + 15;
      });
    }, 300);
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">用户管理</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">管理服务器成员信息和状态</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={handleSyncUsers}
            disabled={syncUsersMutation.isPending}
            className="bg-gray-800 hover:bg-gray-900 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-gray-900/25"
          >
            <icons.RefreshCw className={`w-4 h-4 ${syncUsersMutation.isPending ? 'animate-spin' : ''}`} />
            <span>{syncUsersMutation.isPending ? '同步中...' : '同步用户'}</span>
          </button>
        </div>
      </div>

      {/* 操作结果提示 */}
      {operationResult && (
        <div className={`p-4 rounded-xl border ${
          operationResult.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {operationResult.type === 'success' ? (
              <icons.CheckCircle className="w-5 h-5" />
            ) : (
              <icons.AlertCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{operationResult.message}</span>
          </div>
        </div>
      )}

      {/* 同步进度条 */}
      {isLoading && syncProgress > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-sm mb-2">
            <span className="text-gray-600 dark:text-gray-400">同步进度</span>
            <span className="font-medium text-gray-900 dark:text-white">{syncProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${syncProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧统计区域 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 用户统计卡片 */}
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Users className="w-6 h-6 text-black font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">总用户数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{serverStats.totalMembers}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" style={{ width: `${serverCapacityProgress}%` }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                {serverStats.totalMembers > 0 ? `服务器容量 ${serverCapacityProgress.toFixed(1)}%` : '等待用户加入'}
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                  <icons.UserCheck className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">在线用户</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{serverStats.onlineMembers}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" style={{ width: `${activityProgress}%` }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                {serverStats.onlineMembers > 0 ? `活跃度 ${activityProgress.toFixed(1)}%` : '暂无在线用户'}
              </p>
            </div>


          </div>

          {/* Discord角色分布图 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Discord角色分布</h3>
            <div className="space-y-3">
              {(() => {
                // Calculate real Discord role distribution
                const roleStats: { [key: string]: number } = {};
                users.forEach((user: any) => {
                  if (user.roles && Array.isArray(user.roles)) {
                    user.roles.forEach((role: string) => {
                      roleStats[role] = (roleStats[role] || 0) + 1;
                    });
                  }
                });

                // Convert to array and sort by count (descending)
                const sortedRoles = Object.entries(roleStats)
                  .map(([title, count]) => ({ title, count }))
                  .sort((a, b) => b.count - a.count)
                  .slice(0, 5); // Show top 5 roles

                if (sortedRoles.length === 0) {
                  return [(
                    <div key="no-roles" className="text-center text-gray-500 dark:text-gray-400 py-4">
                      暂无角色数据，请先同步Discord用户
                    </div>
                  )];
                }

                return sortedRoles.map((item, index) => {
                  const percentage = users.length > 0 ? (item.count / users.length) * 100 : 0;
                  return (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">{item.title}</span>
                        <span className="font-medium text-gray-900 dark:text-white">{item.count} 人</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${Math.min(percentage, 100)}%` }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        占比 {percentage.toFixed(1)}%
                      </p>
                    </div>
                  );
                });
              })()}
            </div>
          </div>
        </div>

        {/* 中间主要内容区域 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          {/* 搜索和筛选 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <icons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="搜索用户名..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex space-x-2">
                <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有状态</option>
                  <option>在线</option>
                  <option>离开</option>
                  <option>离线</option>
                </select>
                <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有头衔</option>
                  <option>新手</option>
                  <option>初级成员</option>
                  <option>活跃成员</option>
                  <option>高级成员</option>
                  <option>核心成员</option>
                </select>
                <button
                  onClick={() => syncRolesMutation.mutate()}
                  disabled={syncRolesMutation.isPending}
                  className="px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-colors disabled:opacity-50 flex items-center space-x-2"
                  title="同步所有用户的Discord角色"
                >
                  {syncRolesMutation.isPending ? (
                    <icons.Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <icons.Shield className="w-4 h-4" />
                  )}
                  <span className="hidden sm:inline">同步角色</span>
                </button>
              </div>
            </div>
          </div>

          {/* 用户列表 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">用户列表</h3>
                <span className="text-sm text-gray-500 dark:text-gray-400">共 {filteredUsers.length} 位用户</span>
              </div>
              {usersLoading ? (
                <div className="flex items-center justify-center p-8">
                  <icons.Loader2 className="h-8 w-8 animate-spin text-yellow-600 mr-3" />
                  <span className="text-gray-600 dark:text-gray-400">加载用户数据...</span>
                </div>
              ) : filteredUsers.length === 0 ? (
                <div className="text-center p-8">
                  <icons.Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无Discord用户</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">请先同步Discord服务器用户数据</p>
                  <button
                    onClick={handleSyncUsers}
                    className="flex items-center gap-2 mx-auto px-4 py-2 bg-yellow-600 text-white rounded-xl hover:bg-yellow-700 transition-colors"
                  >
                    <icons.RefreshCw className="h-4 w-4" />
                    同步Discord用户
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredUsers.map((user: any) => (
                    <div key={user.id} className="border border-gray-200 dark:border-gray-600 rounded-xl p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="relative">
                            <div className="w-12 h-12 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center shadow-lg overflow-hidden">
                              {user.avatar ? (
                                <img src={user.avatar} alt={user.username} className="w-12 h-12 rounded-xl object-cover" />
                              ) : (
                                <span className="text-white font-bold text-sm">
                                  {user.username.charAt(0)}
                                </span>
                              )}
                            </div>
                            <div className={`absolute -bottom-1 -right-1 w-4 h-4 ${getStatusColor(user.status)} rounded-full border-2 border-white dark:border-gray-800`}></div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <div className="font-bold text-gray-900 dark:text-white">{user.username}</div>
                              <div className="flex flex-wrap gap-1">
                                {user.titles && user.titles.length > 0 ? (
                                  user.titles.map((title: string, index: number) => (
                                    <span 
                                      key={index}
                                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-bold shadow-sm"
                                      style={getTitleColor(title)}
                                    >
                                      {title}
                                    </span>
                                  ))
                                ) : (
                                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                                    暂无头衔
                                  </span>
                                )}
                              </div>
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                user.status === 'online' ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400' :
                                user.status === 'idle' ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400' :
                                'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400'
                              }`}>
                                {user.status === 'online' ? '在线' : user.status === 'idle' ? '离开' : '离线'}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm text-gray-500 dark:text-gray-400 mb-2">
                              <span>积分: <span className="font-bold text-yellow-600 dark:text-yellow-400">{user.points || 0}</span></span>
                              <span>消息: <span className="font-bold text-gray-900 dark:text-white">{user.messages || 0}</span></span>
                              <span>加入时间: <span className="font-bold text-gray-900 dark:text-white">{user.joinDate ? new Date(user.joinDate).toLocaleDateString('zh-CN') : '未知'}</span></span>
                            </div>
                            <div className="flex flex-wrap gap-1">
                              {(user.roles && user.roles.length > 0) ? user.roles.map((role: string, index: number) => (
                                <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400">
                                  {role}
                                </span>
                              )) : (
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-900/20 text-gray-700 dark:text-gray-400">
                                  暂无Discord角色
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button 
                            onClick={() => handleEditUser(user)}
                            className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300 transition-colors p-2 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
                            title="编辑用户"
                          >
                            <icons.Edit className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => handleSendMessage(user)}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20" 
                            title="发送私信"
                          >
                            <icons.MessageCircle className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => handleKickUser(user)}
                            className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20" 
                            title="踢出用户"
                          >
                            <icons.UserX className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 右侧信息面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 活跃度排行 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.TrendingUp className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">活跃排行</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">本周数据</p>
              </div>
            </div>
            
            <div className="space-y-3">
              {users.slice(0, 5).map((user: any, index: number) => (
                <div key={user.id} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm ${
                      index === 0 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25' : 
                      index === 1 ? 'bg-gradient-to-br from-gray-400 to-gray-500' : 
                      index === 2 ? 'bg-gradient-to-br from-orange-400 to-orange-500' : 'bg-gradient-to-br from-gray-600 to-gray-700'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{user.username}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{user.points || 0} 积分</div>
                    </div>
                  </div>
                  <div className={`w-3 h-3 ${getStatusColor(user.status)} rounded-full`}></div>
                </div>
              ))}
            </div>
          </div>

          {/* 快捷操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快捷操作</h3>
            <div className="space-y-3">
              <button 
                onClick={() => setIsBatchInviteModalOpen(true)}
                className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-blue-500/25"
              >
                <icons.UserPlus className="w-4 h-4" />
                <span>批量邀请</span>
              </button>
              <button 
                onClick={handleExport}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-3 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-green-500/25"
              >
                <icons.Download className="w-4 h-4" />
                <span>导出数据</span>
              </button>
              <button 
                onClick={() => setIsPermissionModalOpen(true)}
                className="w-full bg-gradient-to-r from-purple-500 to-purple-600 text-white px-4 py-3 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-purple-500/25"
              >
                <icons.Settings className="w-4 h-4" />
                <span>权限管理</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 模态框组件 */}
      {/* 编辑用户模态框 */}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl border border-gray-200 dark:border-gray-700 p-8 w-full max-w-lg mx-4 transform transition-all">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.User className="w-8 h-8 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">编辑用户信息</h3>
                <p className="text-gray-500 dark:text-gray-400">{selectedUser?.username}</p>
              </div>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">用户名</label>
                  <input
                    type="text"
                    value={editFormData.username}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, username: e.target.value }))}
                    className="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-yellow-500 focus:ring-0 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">当前积分</label>
                  <input
                    type="number"
                    value={editFormData.points}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, points: parseInt(e.target.value) || 0 }))}
                    className="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-yellow-500 focus:ring-0 bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white transition-colors"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">用户头衔（可多选）</label>
                <div className="space-y-2 max-h-32 overflow-y-auto border-2 border-gray-200 dark:border-gray-600 rounded-xl p-3 bg-gray-50 dark:bg-gray-700">
                  {titles.sort((a: any, b: any) => a.minPoints - b.minPoints).map((title: any) => (
                    <label key={title.id} className="flex items-center space-x-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 p-2 rounded-lg transition-colors">
                      <input
                        type="checkbox"
                        checked={editFormData.titles.includes(title.name)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setEditFormData(prev => ({ 
                              ...prev, 
                              titles: [...prev.titles, title.name] 
                            }));
                          } else {
                            setEditFormData(prev => ({ 
                              ...prev, 
                              titles: prev.titles.filter(t => t !== title.name) 
                            }));
                          }
                        }}
                        className="w-4 h-4 text-yellow-600 bg-gray-100 border-gray-300 rounded focus:ring-yellow-500 dark:focus:ring-yellow-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {title.icon} {title.name}
                      </span>
                    </label>
                  ))}
                  {titles.length === 0 && (
                    <p className="text-gray-500 dark:text-gray-400 text-sm">暂无头衔数据</p>
                  )}
                </div>
                <div className="mt-2">
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    已选择: {editFormData.titles.join(', ') || '无'}
                  </p>
                </div>
              </div>

              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-2xl p-4 border border-yellow-200 dark:border-yellow-800">
                <div className="flex items-center space-x-2 mb-2">
                  <icons.Info className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
                  <span className="text-sm font-medium text-yellow-800 dark:text-yellow-400">用户信息</span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Discord ID:</span>
                    <p className="font-mono text-gray-900 dark:text-white">{selectedUser?.discordId}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">加入时间:</span>
                    <p className="text-gray-900 dark:text-white">{selectedUser?.joinedAt ? new Date(selectedUser.joinedAt).toLocaleDateString('zh-CN') : '未知'}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex space-x-4 mt-8">
              <button
                onClick={() => handleFormSubmit('edit', editFormData)}
                disabled={isLoading || updateUserMutation.isPending}
                className="flex-1 bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-semibold px-6 py-3 rounded-2xl transition-all duration-200 disabled:opacity-50 shadow-lg shadow-yellow-500/25 transform hover:scale-105"
              >
                {updateUserMutation.isPending ? (
                  <div className="flex items-center justify-center space-x-2">
                    <icons.Loader className="w-4 h-4 animate-spin" />
                    <span>保存中...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <icons.Save className="w-4 h-4" />
                    <span>保存更改</span>
                  </div>
                )}
              </button>
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="flex-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-semibold px-6 py-3 rounded-2xl transition-all duration-200"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 发送消息模态框 */}
      {isMessageModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">发送私信</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">收件人</label>
                <input
                  type="text"
                  value={selectedUser?.username}
                  disabled
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">消息内容</label>
                <textarea
                  rows={4}
                  placeholder="请输入要发送的消息..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => handleFormSubmit('message')}
                disabled={isLoading}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoading ? '发送中...' : '发送消息'}
              </button>
              <button
                onClick={() => setIsMessageModalOpen(false)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 踢出用户模态框 */}
      {isKickModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">踢出用户</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              确定要踢出用户 <span className="font-medium text-gray-900 dark:text-white">{selectedUser?.username}</span> 吗？此操作不可撤销。
            </p>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">踢出原因</label>
                <textarea
                  rows={3}
                  placeholder="请输入踢出原因..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => handleFormSubmit('kick')}
                disabled={isLoading}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoading ? '踢出中...' : '确认踢出'}
              </button>
              <button
                onClick={() => setIsKickModalOpen(false)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 批量邀请模态框 */}
      {isBatchInviteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">批量邀请</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">邀请链接</label>
                <input
                  type="text"
                  placeholder="请输入Discord邀请链接..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">用户ID列表</label>
                <textarea
                  rows={4}
                  placeholder="请输入用户ID，每行一个..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => handleFormSubmit('batch')}
                disabled={isLoading}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoading ? '邀请中...' : '发送邀请'}
              </button>
              <button
                onClick={() => setIsBatchInviteModalOpen(false)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 导出数据模态框 */}
      {isExportModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">导出用户数据</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">导出格式</label>
                <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>CSV 格式</option>
                  <option>Excel 格式</option>
                  <option>JSON 格式</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">包含字段</label>
                <div className="space-y-2">
                  {['用户名', '积分', '头衔', '角色', '加入时间', '消息数'].map((field) => (
                    <label key={field} className="flex items-center">
                      <input
                        type="checkbox"
                        defaultChecked
                        className="mr-2 text-green-600 focus:ring-green-500"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">{field}</span>
                    </label>
                  ))}
                </div>
              </div>
              {isLoading && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">导出进度</span>
                    <span className="font-medium text-gray-900 dark:text-white">{exportProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${exportProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setIsExportModalOpen(false)}
                disabled={isLoading}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoading ? '导出中...' : '开始导出'}
              </button>
              <button
                onClick={() => setIsExportModalOpen(false)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 权限管理模态框 */}
      {isPermissionModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">权限管理</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">角色权限</label>
                <div className="space-y-3">
                  {[
                    { role: '管理员', permissions: ['管理用户', '管理频道', '管理服务器'] },
                    { role: '核心用户', permissions: ['踢出用户', '禁言用户'] },
                    { role: '高级用户', permissions: ['管理消息'] },
                    { role: '普通成员', permissions: ['发送消息'] }
                  ].map((item) => (
                    <div key={item.role} className="border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                      <div className="font-medium text-gray-900 dark:text-white mb-2">{item.role}</div>
                      <div className="space-y-1">
                        {item.permissions.map((permission) => (
                          <label key={permission} className="flex items-center">
                            <input
                              type="checkbox"
                              defaultChecked
                              className="mr-2 text-purple-600 focus:ring-purple-500"
                            />
                            <span className="text-sm text-gray-600 dark:text-gray-400">{permission}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => handleFormSubmit('permission')}
                disabled={isLoading}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoading ? '保存中...' : '保存设置'}
              </button>
              <button
                onClick={() => setIsPermissionModalOpen(false)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;