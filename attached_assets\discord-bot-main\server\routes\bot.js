import express from 'express';
import { startBot, stopBot, restartBot, getBotStatus } from '../bot/index.js';
import { getSetting, setSetting } from '../utils/settings.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Get bot status
router.get('/status', (req, res) => {
  try {
    const status = getBotStatus();
    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('Error getting bot status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get bot status',
      error: error.message
    });
  }
});

// Test bot token
router.post('/test-token', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (!token || token.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Token is required'
      });
    }

    // Try to create a temporary client to test the token
    const { Client, GatewayIntentBits } = await import('discord.js');
    const testClient = new Client({
      intents: [GatewayIntentBits.Guilds]
    });

    try {
      await testClient.login(token);
      
      // Get bot info
      const botInfo = {
        id: testClient.user.id,
        username: testClient.user.username,
        discriminator: testClient.user.discriminator,
        avatar: testClient.user.avatar,
        guilds: testClient.guilds.cache.size,
        users: testClient.users.cache.size
      };

      await testClient.destroy();

      res.json({
        success: true,
        message: 'Token is valid',
        data: botInfo
      });
    } catch (error) {
      await testClient.destroy();
      throw error;
    }
  } catch (error) {
    logger.error('Error testing bot token:', error);
    res.status(400).json({
      success: false,
      message: 'Invalid token or connection failed',
      error: error.message
    });
  }
});

// Start bot
router.post('/start', async (req, res) => {
  try {
    const { token } = req.body;
    
    if (token && token.trim() !== '') {
      await setSetting('bot_token', token);
    }

    const botToken = await getSetting('bot_token');
    if (!botToken || botToken.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'No bot token configured'
      });
    }

    await startBot(botToken);
    
    res.json({
      success: true,
      message: 'Bot started successfully'
    });
  } catch (error) {
    logger.error('Error starting bot:', error);
    res.status(500).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
});

// Stop bot
router.post('/stop', async (req, res) => {
  try {
    await stopBot();
    
    res.json({
      success: true,
      message: 'Bot stopped successfully'
    });
  } catch (error) {
    logger.error('Error stopping bot:', error);
    res.status(500).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
});

// Restart bot
router.post('/restart', async (req, res) => {
  try {
    await restartBot();
    
    res.json({
      success: true,
      message: 'Bot restarted successfully'
    });
  } catch (error) {
    logger.error('Error restarting bot:', error);
    res.status(500).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
});

export default router;