async function handleCheckinCommand(interaction: any) {
  let hasReplied = false;
  
  try {
    console.log(`🔄 Processing checkin command for user: ${interaction.user.username} (${interaction.user.id})`);
    
    // Immediate response to prevent timeout with enhanced error handling
    try {
      await interaction.reply({ 
        content: '⏳ Processing your check-in request...', 
        ephemeral: false 
      });
      hasReplied = true;
    } catch (replyError) {
      console.error('Failed to reply to interaction:', replyError);
      return;
    }
    
    // First check if there's a database-stored command response with fallback
    let commands, checkinCommand;
    try {
      commands = await storage.getAllSlashCommands();
      checkinCommand = commands.find(cmd => cmd.name === 'checkin' || cmd.name === '/checkin' || cmd.name === '签到' || cmd.name === '/签到');
    } catch (dbError) {
      console.error('Database error getting commands:', dbError);
      // Continue with hardcoded fallback
      checkinCommand = null;
    }
    
    // Auto-register user if needed
    let user;
    try {
      user = await autoRegisterDiscordUser(interaction);
      if (!user) {
        console.error('❌ User registration failed');
        await interaction.editReply({ 
          content: '❌ Failed to register user automatically. Please contact administrator.'
        });
        return;
      }
      console.log(`✅ User confirmed: ${user.username} (ID: ${user.id})`);
    } catch (error) {
      console.error('Auto-registration failed:', error);
      if (hasReplied) {
        try {
          await interaction.editReply({ 
            content: '❌ Failed to register user automatically. Please contact administrator.'
          });
        } catch (editError) {
          console.error('Failed to edit reply:', editError);
        }
      }
      return;
    }

    // Check if already checked in today
    let hasCheckedInToday = false;
    try {
      const now = new Date();
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000 - 1);
      
      const todayRecords = await storage.getUserCheckinRecords(user.id);
      hasCheckedInToday = todayRecords.some(record => {
        if (!record.checkinDate) return false;
        const recordDate = new Date(record.checkinDate);
        return recordDate >= todayStart && recordDate <= todayEnd;
      });

      if (hasCheckedInToday) {
        const lastCheckinRecord = todayRecords.find(record => {
          if (!record.checkinDate) return false;
          const recordDate = new Date(record.checkinDate);
          return recordDate >= todayStart && recordDate <= todayEnd;
        });
        
        const lastCheckinTime = lastCheckinRecord ? new Date(lastCheckinRecord.checkinDate).toLocaleString('en-US', { 
          timeZone: 'UTC',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }) : 'earlier today';
        
        await interaction.editReply({
          content: `✅ You have already checked in today!\n\n📅 **Last Check-in**: ${lastCheckinTime} UTC\n🎁 **Points Earned**: ${lastCheckinRecord?.pointsEarned || 532} points\n⏰ **Next Check-in**: Tomorrow at 00:00 UTC\n\nRemember: You can only check in once per day!`
        });
        return;
      }
    } catch (checkError) {
      console.error('❌ Error checking today checkin status:', checkError);
      // Continue with checkin process if check fails
    }

    // Get active checkin config with robust fallback
    let activeConfig;
    try {
      const configs = await storage.getAllCheckinConfigs();
      activeConfig = configs.find(config => config.isActive);
    } catch (configError) {
      console.error('Database error getting checkin config:', configError);
      // Use hardcoded ultra-stable fallback
      activeConfig = {
        basePoints: 532,
        allowedChannels: ['1389537538431127653', '1389870757517066333'],
        streakEnabled: true,
        streakDays: 7,
        streakBonusPoints: 50
      };
    }

    // Perform checkin operations
    const consecutiveDays = await storage.getUserConsecutiveDays(user.id);
    const pointsEarned = activeConfig.basePoints || 532;
    let checkinSuccess = false;

    try {
      // Create checkin record
      await storage.createCheckinRecord({
        userId: user.id,
        checkinDate: new Date(),
        consecutiveDays: consecutiveDays + 1,
        pointsEarned: pointsEarned,
        bonusPoints: 0,
        randomBonus: 0,
        channelId: interaction.channelId
      });

      // Create point record
      await storage.createPointRecord({
        userId: user.id,
        amount: pointsEarned,
        reason: `Daily Check-in (${consecutiveDays + 1} consecutive days)`,
        type: 'earned'
      });

      // Update user points
      await storage.updateUser(user.id, {
        points: (user.points || 0) + pointsEarned
      });

      checkinSuccess = true;
    } catch (checkinError) {
      console.error('❌ Checkin operations failed:', checkinError);
      checkinSuccess = false;
    }

    // Get updated user data
    const updatedUser = await storage.getUser(user.id);
    const totalPoints = updatedUser?.points || 0;
    
    // Calculate rankings
    let userRank = 1;
    let checkinRank = 1;
    try {
      const allUsers = await storage.getAllUsers();
      const sortedByPoints = allUsers.sort((a, b) => (b.points || 0) - (a.points || 0));
      userRank = sortedByPoints.findIndex(u => u.id === user.id) + 1;
      
      const todayCheckins = await storage.getTodayCheckins();
      checkinRank = Math.min(todayCheckins, allUsers.length);
    } catch (rankError) {
      console.error('Error calculating rank:', rankError);
    }
    
    // Create embed response
    const embed = {
      color: 0xF39C12,
      title: '✅ Check-in Success!',
      fields: [
        {
          name: '• Consecutive Days',
          value: `${consecutiveDays + 1} days`,
          inline: true
        },
        {
          name: '• Current Points',
          value: `${totalPoints} points`,
          inline: true
        },
        {
          name: '🏆 Personal Ranking',
          value: 'Personal Ranking**',
          inline: true
        },
        {
          name: '• Today\'s Rank',
          value: `#${userRank}`,
          inline: false
        },
        {
          name: '• Check-in Rank',
          value: `#${checkinRank}`,
          inline: false
        }
      ],
      footer: {
        text: 'Remember to check-in again tomorrow 🌟'
      },
      timestamp: new Date().toISOString()
    };

    await interaction.editReply({ embeds: [embed] });
    
    // Record command usage
    if (updatedUser && checkinCommand) {
      try {
        await storage.createSlashCommandUsage({
          commandId: checkinCommand.id,
          userId: updatedUser.id,
          discordUserId: interaction.user.id,
          executionTime: Date.now() - interaction.createdTimestamp,
          success: checkinSuccess,
          errorMessage: checkinSuccess ? null : 'Partial failure in checkin process'
        });
      } catch (usageError) {
        console.error('❌ Failed to record command usage:', usageError);
      }
    }

  } catch (error) {
    console.error('❌ Checkin command failed:', error);
    if (hasReplied) {
      try {
        await interaction.editReply({
          content: '❌ Check-in failed due to system error. Please try again later.'
        });
      } catch (editError) {
        console.error('Failed to edit reply with error message:', editError);
      }
    }
  }
}