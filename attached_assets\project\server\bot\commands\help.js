import { SlashCommandBuilder } from 'discord.js';
import { getSetting } from '../../utils/settings.js';

export const helpCommand = {
  data: new SlashCommandBuilder()
    .setName('帮助')
    .setDescription('显示帮助信息'),

  async execute(interaction) {
    const prefix = await getSetting('bot_prefix') || '!';
    
    await interaction.reply({
      embeds: [{
        title: '🤖 Bot 帮助',
        description: '以下是可用的命令：',
        fields: [
          { name: '/签到', value: '每日签到获取积分', inline: true },
          { name: '/积分', value: '查看个人积分信息', inline: true },
          { name: '/排行榜', value: '查看积分排行榜', inline: true },
          { name: '/帮助', value: '显示此帮助信息', inline: true },
          { name: '\u200B', value: '\u200B', inline: false },
          { name: '传统命令', value: `也可以使用 ${prefix} 前缀的传统命令`, inline: false }
        ],
        color: 0xD6F36F,
        footer: {
          text: 'Discord Bot 管理系统'
        },
        timestamp: new Date().toISOString()
      }]
    });
  }
};