import React, { useState, useEffect } from 'react';
import * as icons from 'lucide-react';
import { useData } from '../contexts/DataContext';

const WebSocketStatus: React.FC = () => {
  const { isConnected, botStatus, serverData, stats, refreshData } = useData();
  const [connectionHistory, setConnectionHistory] = useState<Array<{
    timestamp: string;
    status: string;
    message: string;
  }>>([]);

  useEffect(() => {
    const newEntry = {
      timestamp: new Date().toLocaleTimeString(),
      status: isConnected ? 'connected' : 'disconnected',
      message: isConnected ? 'WebSocket 连接正常' : 'WebSocket 连接断开'
    };
    
    setConnectionHistory(prev => [newEntry, ...prev.slice(0, 9)]);
  }, [isConnected]);

  // 监听 Bot 状态变化
  useEffect(() => {
    if (botStatus.ready) {
      const newEntry = {
        timestamp: new Date().toLocaleTimeString(),
        status: 'bot_ready',
        message: 'Discord Bot 已就绪'
      };
      setConnectionHistory(prev => [newEntry, ...prev.slice(0, 9)]);
    } else if (botStatus.error) {
      const newEntry = {
        timestamp: new Date().toLocaleTimeString(),
        status: 'bot_error',
        message: `Bot 错误: ${botStatus.error}`
      };
      setConnectionHistory(prev => [newEntry, ...prev.slice(0, 9)]);
    }
  }, [botStatus.ready, botStatus.error]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': 
      case 'bot_ready': 
        return 'text-green-600 dark:text-green-400';
      case 'disconnected': 
      case 'bot_error': 
        return 'text-red-600 dark:text-red-400';
      default: 
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return icons.Wifi;
      case 'disconnected': return icons.WifiOff;
      case 'bot_ready': return icons.CheckCircle;
      case 'bot_error': return icons.AlertCircle;
      default: return icons.AlertCircle;
    }
  };

  const testConnection = () => {
    refreshData();
  };

  const getBotStatusText = () => {
    if (botStatus.error) return 'Bot 错误';
    if (botStatus.ready) return 'Bot 在线';
    if (botStatus.connected) return 'Bot 连接中';
    return 'Bot 离线';
  };

  const getBotStatusColor = () => {
    if (botStatus.error) return 'text-red-600 dark:text-red-400';
    if (botStatus.ready) return 'text-green-600 dark:text-green-400';
    if (botStatus.connected) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 w-80">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-gray-900 dark:text-white">系统状态</h3>
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
            <span className={`text-sm font-medium ${isConnected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
              {isConnected ? 'WebSocket 已连接' : 'WebSocket 断开'}
            </span>
          </div>
        </div>

        {/* 实时数据状态 */}
        <div className="space-y-3 mb-4">
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
              <div className="text-gray-500 dark:text-gray-400">Bot 状态</div>
              <div className={`font-medium ${getBotStatusColor()}`}>
                {getBotStatusText()}
              </div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
              <div className="text-gray-500 dark:text-gray-400">延迟</div>
              <div className="font-medium text-gray-900 dark:text-white">
                {botStatus.latency || 0}ms
              </div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
              <div className="text-gray-500 dark:text-gray-400">成员数</div>
              <div className="font-medium text-gray-900 dark:text-white">
                {serverData.memberCount}
              </div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
              <div className="text-gray-500 dark:text-gray-400">在线用户</div>
              <div className="font-medium text-gray-900 dark:text-white">
                {serverData.onlineCount}
              </div>
            </div>
          </div>
          
          {/* 数据库统计 */}
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
              <div className="text-gray-500 dark:text-gray-400">数据库用户</div>
              <div className="font-medium text-gray-900 dark:text-white">
                {stats.userCount}
              </div>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
              <div className="text-gray-500 dark:text-gray-400">今日签到</div>
              <div className="font-medium text-gray-900 dark:text-white">
                {stats.todayCheckins}
              </div>
            </div>
          </div>
        </div>

        {/* 连接历史 */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">状态历史</h4>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {connectionHistory.map((entry, index) => {
              const StatusIcon = getStatusIcon(entry.status);
              return (
                <div key={index} className="flex items-center space-x-2 text-xs">
                  <StatusIcon className={`w-3 h-3 ${getStatusColor(entry.status)}`} />
                  <span className="text-gray-500 dark:text-gray-400">{entry.timestamp}</span>
                  <span className={getStatusColor(entry.status)}>{entry.message}</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2">
          <button
            onClick={testConnection}
            className="flex-1 bg-yellow-400 hover:bg-yellow-500 text-black px-3 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            刷新数据
          </button>
          <button
            onClick={() => window.location.reload()}
            className="flex-1 bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            重新加载
          </button>
        </div>

        {/* 错误信息 */}
        {botStatus.error && (
          <div className="mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center space-x-2">
              <icons.AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
              <span className="text-sm text-red-600 dark:text-red-400">{botStatus.error}</span>
            </div>
          </div>
        )}

        {/* 数据更新时间 */}
        <div className="mt-3 text-xs text-gray-500 dark:text-gray-400 text-center">
          最后更新: {new Date(stats.timestamp).toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

export default WebSocketStatus;