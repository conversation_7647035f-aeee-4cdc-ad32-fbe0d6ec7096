import React from 'react';
import * as icons from 'lucide-react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: 'yellow' | 'black' | 'gray' | 'green' | 'red';
  subtitle?: string;
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  icon, 
  trend, 
  color = 'yellow', 
  subtitle,
  className = ''
}) => {
  const Icon = icons[icon as keyof typeof icons] as React.ComponentType<any>;
  
  const colorClasses = {
    yellow: 'from-yellow-400 to-yellow-500 shadow-yellow-500/25',
    black: 'from-gray-800 to-gray-900 shadow-gray-900/25',
    gray: 'from-gray-500 to-gray-600 shadow-gray-600/25',
    green: 'from-green-400 to-green-500 shadow-green-500/25',
    red: 'from-red-400 to-red-500 shadow-red-500/25'
  };

  const textColor = color === 'yellow' ? 'text-black' : 'text-white';

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 group ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className={`w-12 h-12 bg-gradient-to-br ${colorClasses[color]} rounded-xl flex items-center justify-center shadow-lg`}>
            <Icon className={`w-6 h-6 ${textColor} font-bold`} />
          </div>
          {trend && (
            <div className={`flex items-center text-sm font-semibold ${
              trend.isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
            }`}>
              {trend.isPositive ? (
                <icons.TrendingUp className="w-4 h-4 mr-1" />
              ) : (
                <icons.TrendingDown className="w-4 h-4 mr-1" />
              )}
              <span>{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>
        
        <div>
          <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{title}</p>
          <p className="text-3xl font-bold text-gray-900 dark:text-white mb-1">{value}</p>
          {subtitle && (
            <p className="text-xs text-gray-400 dark:text-gray-500">{subtitle}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default StatCard;