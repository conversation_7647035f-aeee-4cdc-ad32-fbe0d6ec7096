import express from 'express';
import { db } from '../database/init.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Get system logs
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 50, level = '', module = '' } = req.query;
    const offset = (page - 1) * limit;

    let query = 'SELECT * FROM system_logs WHERE 1=1';
    let params = [];

    if (level) {
      query += ' AND level = ?';
      params.push(level);
    }

    if (module) {
      query += ' AND module = ?';
      params.push(module);
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const logs = await db.allAsync(query, params);

    res.json({
      success: true,
      data: logs
    });
  } catch (error) {
    logger.error('Error getting logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get logs'
    });
  }
});

export default router;