import React, { useState, useEffect } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';
import { useData } from '../contexts/DataContext';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

interface Channel {
  id: string;
  name: string;
  type: number;
  category: string;
}

interface Title {
  id: number;
  name: string;
  description: string;
  minPoints: number;
  maxPoints: number | null;
  color: string;
  icon: string;
  isActive: boolean;
}

interface Prize {
  id?: number;
  name: string;
  quantity: number;
  probability: number;
}

interface Lottery {
  id: number;
  title: string;
  description: string;
  status: 'active' | 'ended' | 'pending';
  type: 'channel_entry' | 'manual';
  startTime: string;
  endTime: string;
  maxParticipants: number | null;
  cost: number;
  channels: string[] | null;
  entryMessage: string | null;
  allowedTitles: number[] | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface DrawRecord {
  id: number;
  lotteryId: number;
  userId: number;
  prizeId: number | null;
  prizeName: string | null;
  channelId: string | null;
  drawnAt: string;
}

const LotterySystem: React.FC = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isDrawModalOpen, setIsDrawModalOpen] = useState(false);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [selectedTitles, setSelectedTitles] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [lotteryType, setLotteryType] = useState<'manual' | 'channel_entry'>('manual');
  const [entryMessage, setEntryMessage] = useState('');
  const [prizes, setPrizes] = useState<Prize[]>([{ name: '', quantity: 1, probability: 10 }]);
  const [activeTab, setActiveTab] = useState<'basic' | 'advanced' | 'prizes'>('basic');
  const [prizeMode, setPrizeMode] = useState<'guaranteed' | 'probability'>('probability');
  const [customButtons, setCustomButtons] = useState([
    { name: '点击抽奖', action: 'participate' },
    { name: '查看规则', action: 'view_rules' }
  ]);
  const { showNotification } = useNotification();
  const queryClient = useQueryClient();

  // Helper function for title styling (same as UserManagement)
  const getContrastColor = (hexColor: string) => {
    const r = parseInt(hexColor.slice(1, 3), 16);
    const g = parseInt(hexColor.slice(3, 5), 16);
    const b = parseInt(hexColor.slice(5, 7), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#FFFFFF';
  };

  const getTitleColor = (title: any) => {
    if (title && title.color) {
      return {
        backgroundColor: title.color,
        color: getContrastColor(title.color)
      };
    }
    return {
      backgroundColor: '#6B7280',
      color: '#FFFFFF'
    };
  };

  // 从API获取数据
  const { data: channels = [] } = useQuery<Channel[]>({
    queryKey: ['/api/discord/server-stats'],
    select: (data: any) => data?.data?.channels || []
  });

  const { data: titles = [] } = useQuery<Title[]>({
    queryKey: ['/api/titles'],
    select: (data: any) => data?.data || []
  });

  const { data: lotteries = [], isLoading: lotteriesLoading, refetch: refetchLotteries } = useQuery<Lottery[]>({
    queryKey: ['/api/lotteries'],
    select: (data: any) => data?.data || [],
    refetchInterval: 30000 // Auto-refresh every 30 seconds
  });

  const { data: drawRecords = [] } = useQuery<DrawRecord[]>({
    queryKey: ['/api/lottery-draw-records'],
    select: (data: any) => data?.data || [],
    refetchInterval: 30000
  });

  // Calculate real-time statistics
  const activeLotteries = lotteries.filter(l => l.status === 'active');
  const endedLotteries = lotteries.filter(l => l.status === 'ended');
  const pendingLotteries = lotteries.filter(l => l.status === 'pending');
  const todayDrawRecords = drawRecords.filter(record => {
    const today = new Date().toDateString();
    return new Date(record.drawnAt).toDateString() === today;
  });
  const channelEntryLotteries = lotteries.filter(l => l.type === 'channel_entry');
  const manualLotteries = lotteries.filter(l => l.type === 'manual');

  // 创建抽奖活动
  const createLotteryMutation = useMutation({
    mutationFn: async (lotteryData: any) => {
      const response = await fetch('/api/lotteries', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(lotteryData)
      });
      if (!response.ok) throw new Error('Failed to create lottery');
      return response.json();
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['/api/lotteries'] });
      queryClient.invalidateQueries({ queryKey: ['/api/lottery-draw-records'] });
      showNotification('抽奖活动创建成功！', 'success');
      setIsCreateModalOpen(false);
      resetForm();
      console.log('Lottery created successfully:', response);
    },
    onError: () => {
      showNotification('创建抽奖活动失败！', 'error');
    }
  });

  // 删除抽奖活动
  const deleteLotteryMutation = useMutation({
    mutationFn: async (lotteryId: number) => {
      const response = await fetch(`/api/lotteries/${lotteryId}`, {
        method: 'DELETE'
      });
      if (!response.ok) throw new Error('Failed to delete lottery');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/lotteries'] });
      showNotification('抽奖活动删除成功！', 'success');
    },
    onError: () => {
      showNotification('删除抽奖活动失败！', 'error');
    }
  });

  // 抽奖开奖
  const drawLotteryMutation = useMutation({
    mutationFn: async ({ lotteryId, winnersCount }: { lotteryId: number; winnersCount: number }) => {
      const response = await fetch(`/api/lotteries/${lotteryId}/draw`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ winnersCount })
      });
      if (!response.ok) throw new Error('Failed to draw lottery');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/lottery-draw-records'] });
      showNotification('抽奖开奖成功！恭喜中奖用户！', 'success');
      setIsDrawModalOpen(false);
    },
    onError: () => {
      showNotification('抽奖开奖失败！', 'error');
    }
  });

  const resetForm = () => {
    setSelectedChannels([]);
    setSelectedTitles([]);
    setLotteryType('manual');
    setEntryMessage('');
    setPrizes([{ name: '', quantity: 1, probability: 10 }]);
    setCustomButtons([
      { name: '点击抽奖', action: 'participate' },
      { name: '查看规则', action: 'view_rules' }
    ]);
  };

  // Listen for real-time lottery updates
  useEffect(() => {
    const handleWebSocketMessage = (event: MessageEvent) => {
      const data = JSON.parse(event.data);
      if (data.type === 'lotteryCreated' || data.type === 'lotteryUpdated' || data.type === 'lotteryDeleted') {
        // Refresh lottery data
        queryClient.invalidateQueries({ queryKey: ['/api/lotteries'] });
        queryClient.invalidateQueries({ queryKey: ['/api/lottery-draw-records'] });
      }
    };

    // Add WebSocket event listener if WebSocket is available
    if (typeof window !== 'undefined' && window.WebSocket) {
      window.addEventListener('message', handleWebSocketMessage);
      return () => window.removeEventListener('message', handleWebSocketMessage);
    }
  }, [queryClient]);

  const filteredLotteries = lotteries.filter(lottery => 
    lottery.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (lottery.description && lottery.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400';
      case 'ended': return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
      case 'pending': return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400';
      default: return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '进行中';
      case 'ended': return '已结束';
      case 'pending': return '未开始';
      default: return '未知';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'channel_entry': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-400';
      case 'manual': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400';
      default: return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'channel_entry': return '频道进入';
      case 'manual': return '手动抽奖';
      default: return '未知';
    }
  };

  const handleSyncChannels = async () => {
    try {
      await queryClient.invalidateQueries({ queryKey: ['/api/discord/server-stats'] });
      showNotification('频道数据同步成功！', 'success');
    } catch (error) {
      showNotification('频道数据同步失败！', 'error');
    }
  };

  const handleChannelToggle = (channelId: string) => {
    setSelectedChannels(prev => 
      prev.includes(channelId) 
        ? prev.filter(c => c !== channelId)
        : [...prev, channelId]
    );
  };

  const handleTitleToggle = (titleId: number) => {
    setSelectedTitles(prev => 
      prev.includes(titleId) 
        ? prev.filter(t => t !== titleId)
        : [...prev, titleId]
    );
  };

  const handleCreateLottery = async (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);
    
    const lotteryData = {
      title: formData.get('title') as string,
      description: formData.get('description') as string,
      type: lotteryType,
      startTime: formData.get('startTime') as string,
      endTime: formData.get('endTime') as string,
      maxParticipants: formData.get('maxParticipants') ? parseInt(formData.get('maxParticipants') as string) : null,
      cost: formData.get('cost') ? parseInt(formData.get('cost') as string) : 0,
      channels: lotteryType === 'channel_entry' && selectedChannels.length > 0 ? selectedChannels : null,
      entryMessage: lotteryType === 'channel_entry' && entryMessage.trim() ? entryMessage : null,
      allowedTitles: lotteryType === 'channel_entry' && selectedTitles.length > 0 ? selectedTitles : null,
      customButtons: lotteryType === 'channel_entry' && customButtons.length > 0 ? customButtons : null,
      status: 'active',
      prizes: prizes.length > 0 ? prizes : null
    };

    createLotteryMutation.mutate(lotteryData);
  };

  const handleDrawLottery = async (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);
    
    const lotteryId = parseInt(formData.get('lotteryId') as string);
    const winnersCount = parseInt(formData.get('winnersCount') as string) || 1;

    drawLotteryMutation.mutate({ lotteryId, winnersCount });
  };

  const handleEditLottery = (lotteryId: number) => {
    showNotification('抽奖活动编辑功能即将开放！', 'info');
  };

  const handleDrawNow = (lotteryId: number) => {
    setIsDrawModalOpen(true);
  };

  const handleCopyLottery = (lotteryId: number) => {
    showNotification('抽奖活动已复制！', 'success');
  };

  const handleDeleteLottery = async (lotteryId: number) => {
    if (!confirm('确定要删除这个抽奖活动吗？')) return;
    deleteLotteryMutation.mutate(lotteryId);
  };

  const addPrize = () => {
    setPrizes([...prizes, { name: '', quantity: 1, probability: 10 }]);
  };

  const removePrize = (index: number) => {
    setPrizes(prizes.filter((_, i) => i !== index));
  };

  const updatePrize = (index: number, field: keyof Prize, value: string | number) => {
    const newPrizes = [...prizes];
    newPrizes[index] = { ...newPrizes[index], [field]: value };
    setPrizes(newPrizes);
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">抽奖系统</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">创建和管理抽奖活动</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setIsDrawModalOpen(true)}
            className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-purple-500/25 font-medium"
          >
            <icons.Shuffle className="w-4 h-4" />
            <span>立即开奖</span>
          </button>
          <button 
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-yellow-500/25 font-medium"
          >
            <icons.Plus className="w-4 h-4" />
            <span>创建抽奖</span>
          </button>
        </div>
      </div>

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧统计区域 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 抽奖统计卡片 */}
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Gift className="w-6 h-6 text-black font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">活动总数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{lotteries.length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" style={{ width: '100%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">系统运行正常</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                  <icons.Play className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">进行中</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{activeLotteries.length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" style={{ 
                  width: lotteries.length > 0 ? `${(activeLotteries.length / lotteries.length) * 100}%` : '0%' 
                }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                {lotteries.length > 0 ? `${Math.round((activeLotteries.length / lotteries.length) * 100)}% 活跃` : '无数据'}
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                  <icons.Users className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">总参与人数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">0</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-purple-400 to-purple-500 h-2 rounded-full" style={{ width: '75%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">参与度良好</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                  <icons.Award className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日中奖</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{todayDrawRecords.length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-orange-400 to-orange-500 h-2 rounded-full" style={{ 
                  width: activeLotteries.length > 0 ? `${Math.min((todayDrawRecords.length / activeLotteries.length) * 100, 100)}%` : '0%' 
                }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                {activeLotteries.length > 0 ? `中奖率 ${Math.round(Math.min((todayDrawRecords.length / activeLotteries.length) * 100, 100))}%` : '无活动'}
              </p>
            </div>
          </div>

          {/* 抽奖类型分布 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">抽奖类型</h3>
            <div className="space-y-3">
              {[
                { type: 'channel_entry', name: '频道进入', count: channelEntryLotteries.length, color: 'bg-purple-500' },
                { type: 'manual', name: '手动抽奖', count: manualLotteries.length, color: 'bg-blue-500' }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 ${item.color} rounded-full`}></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">{item.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-900 dark:text-white">{item.count}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {lotteries.length > 0 ? ((item.count / lotteries.length) * 100).toFixed(0) : 0}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 中间主要内容区域 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          {/* 搜索和筛选 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <icons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="搜索抽奖活动..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex space-x-2">
                <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有状态</option>
                  <option>进行中</option>
                  <option>已结束</option>
                  <option>未开始</option>
                </select>
                <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有类型</option>
                  <option>频道进入</option>
                  <option>手动抽奖</option>
                </select>
              </div>
            </div>
          </div>

          {/* 抽奖活动列表 */}
          <div className="space-y-4">
            {lotteriesLoading ? (
              <div className="text-center py-8">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"></div>
                <p className="mt-2 text-gray-600 dark:text-gray-400">加载中...</p>
              </div>
            ) : filteredLotteries.length === 0 ? (
              <div className="text-center py-8">
                <icons.Gift className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">暂无抽奖活动</p>
                <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">点击上方"创建抽奖"按钮开始创建</p>
              </div>
            ) : (
              filteredLotteries.map((lottery) => (
              <div key={lottery.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white">{lottery.title}</h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(lottery.status)}`}>
                        {getStatusText(lottery.status)}
                      </span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(lottery.type)}`}>
                        {getTypeText(lottery.type)}
                      </span>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">{lottery.description}</p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">最大人数</span>
                        <div className="font-medium text-gray-900 dark:text-white">{lottery.maxParticipants || '无限制'}</div>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">参与费用</span>
                        <div className="font-medium text-gray-900 dark:text-white">{lottery.cost} 积分</div>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">开始时间</span>
                        <div className="font-medium text-gray-900 dark:text-white">{new Date(lottery.startTime).toLocaleString()}</div>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">结束时间</span>
                        <div className="font-medium text-gray-900 dark:text-white">{new Date(lottery.endTime).toLocaleString()}</div>
                      </div>
                    </div>
                    
                    {lottery.channels && lottery.channels.length > 0 && (
                      <div className="mt-3">
                        <span className="text-gray-500 dark:text-gray-400 text-sm">关联频道：</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {lottery.channels.map((channelId, idx) => {
                            const channel = channels.find(c => c.id === channelId);
                            return (
                              <span key={idx} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                                {channel?.name || channelId}
                              </span>
                            );
                          })}
                        </div>
                      </div>
                    )}

                    {lottery.entryMessage && (
                      <div className="mt-3">
                        <span className="text-gray-500 dark:text-gray-400 text-sm">进入消息：</span>
                        <div className="mt-1 p-2 bg-gray-50 dark:bg-gray-700 rounded text-sm text-gray-900 dark:text-white">
                          {lottery.entryMessage}
                        </div>
                      </div>
                    )}

                    {lottery.allowedTitles && lottery.allowedTitles.length > 0 && (
                      <div className="mt-3">
                        <span className="text-gray-500 dark:text-gray-400 text-sm">允许头衔：</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {lottery.allowedTitles.map((titleId, idx) => {
                            const title = titles.find(t => t.id === titleId);
                            return (
                              <span key={idx} className="inline-flex items-center px-2 py-1 rounded-full text-xs" style={{ backgroundColor: title?.color + '20', color: title?.color }}>
                                {title?.name || `头衔${titleId}`}
                              </span>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <button 
                      onClick={() => handleEditLottery(lottery.id)}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20" 
                      title="编辑"
                    >
                      <icons.Edit className="w-4 h-4" />
                    </button>
                    <button 
                      onClick={() => handleDrawNow(lottery.id)}
                      className="text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 transition-colors p-2 rounded-lg hover:bg-purple-50 dark:hover:bg-purple-900/20" 
                      title="立即开奖"
                    >
                      <icons.Shuffle className="w-4 h-4" />
                    </button>
                    <button 
                      onClick={() => handleCopyLottery(lottery.id)}
                      className="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 transition-colors p-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20" 
                      title="复制"
                    >
                      <icons.Copy className="w-4 h-4" />
                    </button>
                    <button 
                      onClick={() => handleDeleteLottery(lottery.id)}
                      className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20" 
                      title="删除"
                    >
                      <icons.Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

              </div>
              ))
            )}
          </div>
        </div>

        {/* 右侧信息区域 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 频道管理 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">频道管理</h3>
              <button 
                onClick={handleSyncChannels}
                className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors"
              >
                <icons.RefreshCw className="w-4 h-4" />
              </button>
            </div>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {channels.map((channel) => (
                <div key={channel.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white text-sm">{channel.name}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{channel.category}</div>
                  </div>
                  <div className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded-full text-gray-600 dark:text-gray-300">
                    {channel.type === 0 ? '文字' : '语音'}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 最近中奖记录 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">最近中奖</h3>
            <div className="space-y-3">
              {drawRecords.slice(0, 5).map((record) => (
                <div key={record.id} className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-lg flex items-center justify-center">
                    <icons.Award className="w-4 h-4 text-black" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-gray-900 dark:text-white truncate">用户{record.userId}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 truncate">{record.prizeName || '奖品'}</div>
                    <div className="text-xs text-gray-400 dark:text-gray-500">{new Date(record.drawnAt).toLocaleString()}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 创建抽奖模态框 - 多页导航 */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-5xl shadow-2xl max-h-[90vh] overflow-y-auto">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">创建抽奖活动</h3>
            
            {/* Tab 导航 */}
            <div className="flex border-b border-gray-200 dark:border-gray-700 mb-6">
              <button
                type="button"
                onClick={() => setActiveTab('basic')}
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors duration-200 ${
                  activeTab === 'basic'
                    ? 'border-yellow-500 text-yellow-600 dark:text-yellow-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                基本设置
              </button>
              <button
                type="button"
                onClick={() => setActiveTab('advanced')}
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors duration-200 ${
                  activeTab === 'advanced'
                    ? 'border-yellow-500 text-yellow-600 dark:text-yellow-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                高级设置
              </button>
              <button
                type="button"
                onClick={() => setActiveTab('prizes')}
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors duration-200 ${
                  activeTab === 'prizes'
                    ? 'border-yellow-500 text-yellow-600 dark:text-yellow-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                奖品设置
              </button>
            </div>
            
            <form onSubmit={handleCreateLottery} className="space-y-6">
              {/* 基本设置页面 */}
              {activeTab === 'basic' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">活动标题</label>
                      <input
                        name="title"
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="输入活动标题"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">抽奖类型</label>
                      <select 
                        value={lotteryType}
                        onChange={(e) => setLotteryType(e.target.value as 'manual' | 'channel_entry')}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="manual">手动抽奖</option>
                        <option value="channel_entry">频道进入</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">活动描述</label>
                    <textarea
                      name="description"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      rows={3}
                      placeholder="输入活动描述"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">开始时间</label>
                      <input
                        name="startTime"
                        type="datetime-local"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">结束时间</label>
                      <input
                        name="endTime"
                        type="datetime-local"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最大参与人数</label>
                      <input
                        name="maxParticipants"
                        type="number"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="0 表示无限制"
                        min="0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">参与费用（积分）</label>
                      <input
                        name="cost"
                        type="number"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="0"
                        min="0"
                        defaultValue="0"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* 高级设置页面 */}
              {activeTab === 'advanced' && (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      设置频道
                      <span className="text-xs text-gray-500 ml-1">(Bot监听消息和推送抽奖信息的频道)</span>
                    </label>
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-2 mb-3">
                        {selectedChannels.map((channelId) => {
                          const channel = channels.find(c => c.id === channelId);
                          return (
                            <span key={channelId} className="inline-flex items-center px-3 py-1 rounded-xl text-sm bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                              #{channel?.name || channelId}
                              <button
                                type="button"
                                onClick={() => handleChannelToggle(channelId)}
                                className="ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                              >
                                <icons.X className="w-3 h-3" />
                              </button>
                            </span>
                          );
                        })}
                      </div>
                      <div className="max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-xl p-3 bg-white dark:bg-gray-700">
                        {channels.map((channel) => (
                          <label key={channel.id} className="flex items-center space-x-2 py-2 hover:bg-gray-50 dark:hover:bg-gray-600 rounded">
                            <input
                              type="checkbox"
                              className="rounded border-gray-300 text-yellow-500 focus:ring-yellow-500"
                              checked={selectedChannels.includes(channel.id)}
                              onChange={() => handleChannelToggle(channel.id)}
                            />
                            <span className="text-sm text-gray-900 dark:text-white">#{channel.name}</span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">({channel.category})</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      进入消息
                      <span className="text-xs text-gray-500 ml-1">(支持emoji、**粗体**、&lt;#频道ID&gt;等格式)</span>
                    </label>
                    <textarea
                      value={entryMessage}
                      onChange={(e) => setEntryMessage(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      rows={3}
                      placeholder="例如：🎉 欢迎进入抽奖频道 <#1234567890>！祝你好运！🍀"
                    />
                  </div>

                  {/* 自定义按钮设置 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      自定义按钮
                      <span className="text-xs text-gray-500 ml-1">(用户可点击的交互按钮)</span>
                    </label>
                    <div className="space-y-3">
                      {customButtons.map((button, index) => (
                        <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                          <div className="flex-1">
                            <input
                              type="text"
                              value={button.name}
                              onChange={(e) => {
                                const newButtons = [...customButtons];
                                newButtons[index].name = e.target.value;
                                setCustomButtons(newButtons);
                              }}
                              className="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                              placeholder="按钮名称"
                            />
                          </div>
                          <div className="flex-1">
                            <select
                              value={button.action}
                              onChange={(e) => {
                                const newButtons = [...customButtons];
                                newButtons[index].action = e.target.value;
                                setCustomButtons(newButtons);
                              }}
                              className="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm"
                            >
                              <option value="participate">开始抽奖</option>
                              <option value="view_rules">查看规则</option>
                              <option value="check_status">查看状态</option>
                              <option value="leave">不参与</option>
                            </select>
                          </div>
                          <button
                            type="button"
                            onClick={() => {
                              const newButtons = customButtons.filter((_, i) => i !== index);
                              setCustomButtons(newButtons);
                            }}
                            className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-1"
                          >
                            <icons.Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => setCustomButtons([...customButtons, { name: '新按钮', action: 'participate' }])}
                        className="w-full px-3 py-2 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:border-yellow-500 hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors text-sm"
                      >
                        + 添加按钮
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      可参与的头衔
                      <span className="text-xs text-gray-500 ml-1">(可多选，不选表示所有头衔都可参与)</span>
                    </label>
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-2 mb-3">
                        {selectedTitles.map((titleId) => {
                          const title = titles.find(t => t.id === titleId);
                          return (
                            <span key={titleId} className="inline-flex items-center px-3 py-1 rounded-xl text-sm font-medium shadow-sm" style={{ backgroundColor: title?.color + '20', color: title?.color }}>
                              {title?.icon} {title?.name}
                              <button
                                type="button"
                                onClick={() => handleTitleToggle(titleId)}
                                className="ml-2 opacity-70 hover:opacity-100"
                              >
                                <icons.X className="w-3 h-3" />
                              </button>
                            </span>
                          );
                        })}
                      </div>
                      <div className="max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-xl p-3 bg-white dark:bg-gray-700">
                        {titles.filter(title => title.isActive).map((title) => (
                          <label key={title.id} className="flex items-center space-x-2 py-2 hover:bg-gray-50 dark:hover:bg-gray-600 rounded">
                            <input
                              type="checkbox"
                              className="rounded border-gray-300 text-yellow-500 focus:ring-yellow-500"
                              checked={selectedTitles.includes(title.id)}
                              onChange={() => handleTitleToggle(title.id)}
                            />
                            <span 
                              className="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold shadow-lg"
                              style={getTitleColor(title)}
                            >
                              {title.icon} {title.name}
                            </span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              ({title.minPoints} - {title.maxPoints || '∞'} 积分)
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 奖品设置页面 */}
              {activeTab === 'prizes' && (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">奖品模式</label>
                    <div className="flex space-x-4">
                      <label className="flex items-center space-x-2">
                        <input
                          type="radio"
                          value="probability"
                          checked={prizeMode === 'probability'}
                          onChange={(e) => setPrizeMode(e.target.value as 'guaranteed' | 'probability')}
                          className="text-yellow-500 focus:ring-yellow-500"
                        />
                        <span className="text-sm text-gray-900 dark:text-white">概率抽奖</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input
                          type="radio"
                          value="guaranteed"
                          checked={prizeMode === 'guaranteed'}
                          onChange={(e) => setPrizeMode(e.target.value as 'guaranteed' | 'probability')}
                          className="text-yellow-500 focus:ring-yellow-500"
                        />
                        <span className="text-sm text-gray-900 dark:text-white">必中奖</span>
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {prizeMode === 'probability' ? '按概率抽取奖品，总概率可以不为100%' : '每个参与者都会中奖，按顺序或随机分配'}
                    </p>
                  </div>

                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">奖品列表</label>
                      <button
                        type="button"
                        onClick={addPrize}
                        className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 shadow-lg shadow-yellow-500/25 flex items-center space-x-1"
                      >
                        <icons.Plus className="w-4 h-4" />
                        <span>添加奖品</span>
                      </button>
                    </div>
                    <div className="space-y-3">
                      {prizes.map((prize, index) => (
                        <div key={index} className="flex items-center space-x-3 p-4 border border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-700">
                          <input
                            type="text"
                            placeholder="奖品名称"
                            value={prize.name}
                            onChange={(e) => updatePrize(index, 'name', e.target.value)}
                            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                          />
                          <input
                            type="number"
                            placeholder="数量"
                            value={prize.quantity}
                            onChange={(e) => updatePrize(index, 'quantity', parseInt(e.target.value) || 1)}
                            className="w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                            min="1"
                          />
                          {prizeMode === 'probability' && (
                            <input
                              type="number"
                              placeholder="概率%"
                              value={prize.probability}
                              onChange={(e) => updatePrize(index, 'probability', parseInt(e.target.value) || 10)}
                              className="w-24 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                              min="1"
                              max="100"
                            />
                          )}
                          {prizes.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removePrize(index)}
                              className="text-red-500 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900 transition-colors duration-200"
                            >
                              <icons.Trash2 className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="flex space-x-2">
                  {activeTab !== 'basic' && (
                    <button
                      type="button"
                      onClick={() => {
                        if (activeTab === 'advanced') setActiveTab('basic');
                        if (activeTab === 'prizes') setActiveTab('advanced');
                      }}
                      className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium transition-colors duration-200"
                    >
                      上一步
                    </button>
                  )}
                  {activeTab !== 'prizes' && (
                    <button
                      type="button"
                      onClick={() => {
                        if (activeTab === 'basic') setActiveTab('advanced');
                        if (activeTab === 'advanced') setActiveTab('prizes');
                      }}
                      className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-6 py-2 rounded-xl font-medium transition-all duration-200 shadow-lg shadow-blue-500/25"
                    >
                      下一步
                    </button>
                  )}
                </div>
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={() => setIsCreateModalOpen(false)}
                    className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium transition-colors duration-200"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={createLotteryMutation.isPending}
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-6 py-2 rounded-xl font-medium transition-all duration-200 shadow-lg shadow-yellow-500/25 disabled:opacity-50"
                  >
                    {createLotteryMutation.isPending ? '创建中...' : '创建抽奖'}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 立即开奖模态框 */}
      {isDrawModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">立即开奖</h3>
            
            <form onSubmit={handleDrawLottery} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">选择抽奖活动</label>
                <select name="lotteryId" className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  {lotteries.filter(l => l.status === 'active').map(lottery => (
                    <option key={lottery.id} value={lottery.id}>{lottery.title}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">中奖人数</label>
                <input
                  name="winnersCount"
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="1"
                  min="1"
                  defaultValue="1"
                  required
                />
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsDrawModalOpen(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={drawLotteryMutation.isPending}
                  className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-2 rounded-xl font-medium transition-all duration-200 shadow-lg shadow-purple-500/25 disabled:opacity-50"
                >
                  {drawLotteryMutation.isPending ? '开奖中...' : '开始抽奖'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default LotterySystem;