const { Client, GatewayIntentBits, REST, Routes } = require('discord.js');

// 从环境变量或数据库获取token
async function clearAllCommands() {
  try {
    // 直接使用你的bot token
    const token = process.env.DISCORD_TOKEN || 'YOUR_BOT_TOKEN_HERE';
    
    if (!token || token === 'YOUR_BOT_TOKEN_HERE') {
      console.error('请在脚本中设置正确的DISCORD_TOKEN');
      return;
    }
    
    const client = new Client({
      intents: [GatewayIntentBits.Guilds]
    });
    
    await client.login(token);
    
    console.log(`登录成功: ${client.user.tag}`);
    
    const rest = new REST({ version: '10' }).setToken(token);
    
    // 获取第一个服务器
    const guild = client.guilds.cache.first();
    
    if (guild) {
      console.log(`清理服务器: ${guild.name}`);
      
      // 获取所有服务器指令
      const guildCommands = await rest.get(
        Routes.applicationGuildCommands(client.user.id, guild.id)
      );
      
      console.log(`找到 ${guildCommands.length} 个服务器指令:`);
      guildCommands.forEach(cmd => console.log(`  - ${cmd.name}`));
      
      // 删除所有服务器指令
      for (const command of guildCommands) {
        try {
          await rest.delete(
            Routes.applicationGuildCommand(client.user.id, guild.id, command.id)
          );
          console.log(`✓ 删除服务器指令: ${command.name}`);
        } catch (error) {
          console.error(`✗ 删除服务器指令失败 ${command.name}:`, error.message);
        }
      }
    }
    
    // 获取所有全局指令
    const globalCommands = await rest.get(
      Routes.applicationCommands(client.user.id)
    );
    
    console.log(`找到 ${globalCommands.length} 个全局指令:`);
    globalCommands.forEach(cmd => console.log(`  - ${cmd.name}`));
    
    // 删除所有全局指令
    for (const command of globalCommands) {
      try {
        await rest.delete(
          Routes.applicationCommand(client.user.id, command.id)
        );
        console.log(`✓ 删除全局指令: ${command.name}`);
      } catch (error) {
        console.error(`✗ 删除全局指令失败 ${command.name}:`, error.message);
      }
    }
    
    console.log('\n所有Discord指令已清除完成!');
    
    // 等待一会再退出
    setTimeout(() => {
      client.destroy();
      process.exit(0);
    }, 2000);
    
  } catch (error) {
    console.error('清除指令时发生错误:', error);
    process.exit(1);
  }
}

clearAllCommands();