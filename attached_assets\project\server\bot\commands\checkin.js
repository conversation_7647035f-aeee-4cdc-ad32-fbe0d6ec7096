import { SlashCommandBuilder } from 'discord.js';
import { db } from '../../database/init.js';
import { getSetting } from '../../utils/settings.js';
import { logger } from '../../utils/logger.js';

export const checkinCommand = {
  data: new SlashCommandBuilder()
    .setName('签到')
    .setDescription('每日签到获取积分'),

  async execute(interaction) {
    try {
      const userId = interaction.user.id;
      const today = new Date().toISOString().split('T')[0];

      // Check if already checked in today
      const existingCheckin = await db.getAsync(`
        SELECT * FROM checkin_records 
        WHERE user_id = ? AND checkin_date = ?
      `, [userId, today]);

      if (existingCheckin) {
        await interaction.reply({
          content: '⏰ 您今天已经签到过了，明天再来吧！',
          ephemeral: true
        });
        return;
      }

      // Get user's current streak
      const user = await db.getAsync('SELECT checkin_streak FROM users WHERE id = ?', [userId]);
      const currentStreak = user?.checkin_streak || 0;
      const newStreak = currentStreak + 1;

      // Calculate points
      const basePoints = parseInt(await getSetting('checkin_points')) || 10;
      let bonusPoints = 0;
      
      if (newStreak >= 7) bonusPoints += 5;
      if (newStreak >= 30) bonusPoints += 15;

      const totalPoints = basePoints + bonusPoints;

      // Record checkin
      await db.runAsync(`
        INSERT INTO checkin_records (user_id, checkin_date, points_earned, streak_bonus)
        VALUES (?, ?, ?, ?)
      `, [userId, today, totalPoints, bonusPoints]);

      // Update user streak and points
      await db.runAsync(`
        UPDATE users 
        SET checkin_streak = ?, last_checkin = ?, points = points + ?
        WHERE id = ?
      `, [newStreak, today, totalPoints, userId]);

      // Create response embed
      const embed = {
        title: '✅ 签到成功！',
        description: `恭喜 ${interaction.user}！`,
        fields: [
          { name: '获得积分', value: totalPoints.toString(), inline: true },
          { name: '连续签到', value: `${newStreak} 天`, inline: true }
        ],
        color: 0xD6F36F,
        timestamp: new Date().toISOString()
      };

      if (bonusPoints > 0) {
        embed.fields.push({ name: '连续奖励', value: `+${bonusPoints} 积分`, inline: true });
      }

      await interaction.reply({ embeds: [embed] });
    } catch (error) {
      logger.error('Error in checkin command:', error);
      await interaction.reply({
        content: '❌ 签到失败，请稍后重试。',
        ephemeral: true
      });
    }
  }
};