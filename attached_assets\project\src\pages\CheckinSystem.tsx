import React, { useState } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';

const CheckinSystem: React.FC = () => {
  const [selectedChannel, setSelectedChannel] = useState('');
  const [isResetModalOpen, setIsResetModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isReminderModalOpen, setIsReminderModalOpen] = useState(false);
  const [isTicketModalOpen, setIsTicketModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isChannelRefreshing, setIsChannelRefreshing] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const { showNotification } = useNotification();

  const [channels, setChannels] = useState([
    { id: '1', name: '📅 签到大厅', type: 'text', category: '功能频道' },
    { id: '2', name: '🎮 游戏交流', type: 'text', category: '聊天频道' },
    { id: '3', name: '💬 闲聊水群', type: 'text', category: '聊天频道' },
    { id: '4', name: '📢 公告频道', type: 'text', category: '信息频道' },
  ]);

  const [tickets, setTickets] = useState([
    {
      id: '1',
      name: '每日签到',
      description: '标准的每日签到功能',
      channels: ['📅 签到大厅'],
      isActive: true,
      points: 10,
      consecutiveBonus: 5,
      maxConsecutive: 30
    },
    {
      id: '2',
      name: '活动签到',
      description: '特殊活动期间的签到',
      channels: ['🎮 游戏交流', '💬 闲聊水群'],
      isActive: false,
      points: 20,
      consecutiveBonus: 10,
      maxConsecutive: 7
    }
  ]);

  const handleSaveConfig = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('签到配置保存成功！');
    }, 1500);
  };

  const handleResetCheckin = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('签到数据重置成功！');
      setIsResetModalOpen(false);
    }, 2000);
  };

  const handleExport = () => {
    setIsExportModalOpen(true);
    setExportProgress(0);
    setIsLoading(true);
    
    const interval = setInterval(() => {
      setExportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          return 100;
        }
        return prev + 15;
      });
    }, 300);
  };

  const handleSetReminder = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('签到提醒设置成功！');
      setIsReminderModalOpen(false);
    }, 1500);
  };

  const handleRefreshChannels = () => {
    setIsChannelRefreshing(true);
    setTimeout(() => {
      setIsChannelRefreshing(false);
      showNotification('频道数据已刷新！');
      // 模拟刷新后的数据更新
      setChannels([
        ...channels,
        { id: '5', name: '🎵 音乐分享', type: 'text', category: '娱乐频道' }
      ]);
    }, 2000);
  };

  const handleCreateTicket = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('签到工单创建成功！');
      setIsTicketModalOpen(false);
    }, 1500);
  };

  const handleToggleTicket = (ticketId: string) => {
    setTickets(tickets.map(ticket => 
      ticket.id === ticketId 
        ? { ...ticket, isActive: !ticket.isActive }
        : ticket
    ));
    const ticket = tickets.find(t => t.id === ticketId);
    showNotification(`工单 "${ticket?.name}" 已${ticket?.isActive ? '禁用' : '启用'}！`);
  };

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white">签到系统</h2>
        <p className="text-gray-600 dark:text-gray-400 mt-1">配置每日签到功能和奖励设置</p>
      </div>

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧统计区域 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 签到统计卡片 */}
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Calendar className="w-6 h-6 text-black font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日签到</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">127</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" style={{ width: '78%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">签到率 78%</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                  <icons.Flame className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">连续签到</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">15天</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-orange-400 to-orange-500 h-2 rounded-full" style={{ width: '50%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">最高记录 30天</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl flex items-center justify-center shadow-lg shadow-gray-900/25">
                  <icons.Star className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">签到积分</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">6,350</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-gray-800 to-gray-900 h-2 rounded-full" style={{ width: '85%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">今日发放积分</p>
            </div>
          </div>

          {/* 签到时间分布 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">签到时间分布</h3>
            <div className="space-y-3">
              {[
                { time: '06:00-09:00', count: 45, percentage: 35 },
                { time: '09:00-12:00', count: 38, percentage: 30 },
                { time: '12:00-18:00', count: 32, percentage: 25 },
                { time: '18:00-24:00', count: 12, percentage: 10 }
              ].map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">{item.time}</span>
                    <span className="font-medium text-gray-900 dark:text-white">{item.count} 人</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${item.percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 中间主要内容区域 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          {/* 签到设置 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">签到配置</h3>
                <button
                  onClick={handleRefreshChannels}
                  disabled={isChannelRefreshing}
                  className="flex items-center space-x-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
                >
                  <icons.RefreshCw className={`w-3 h-3 ${isChannelRefreshing ? 'animate-spin' : ''}`} />
                  <span>{isChannelRefreshing ? '刷新中...' : '手动刷新'}</span>
                </button>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* 基础设置 */}
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">签到频道</label>
                    <select 
                      value={selectedChannel}
                      onChange={(e) => setSelectedChannel(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">请选择签到频道</option>
                      {channels.map((channel) => (
                        <option key={channel.id} value={channel.id}>
                          {channel.name}
                        </option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">选择用户可以进行签到的频道</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">签到时间限制</label>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">开始时间</label>
                        <input
                          type="time"
                          defaultValue="00:00"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">结束时间</label>
                        <input
                          type="time"
                          defaultValue="23:59"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* 奖励设置 */}
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">基础积分奖励</label>
                    <input
                      type="number"
                      placeholder="10"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">每次签到获得的基础积分</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">连续签到加成</label>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-xl">
                        <span className="text-sm">连续7天</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500 dark:text-gray-400">+</span>
                          <input
                            type="number"
                            placeholder="5"
                            className="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:ring-1 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                          <span className="text-sm text-gray-500 dark:text-gray-400">积分</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-xl">
                        <span className="text-sm">连续30天</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500 dark:text-gray-400">+</span>
                          <input
                            type="number"
                            placeholder="20"
                            className="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm focus:ring-1 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          />
                          <span className="text-sm text-gray-500 dark:text-gray-400">积分</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">随机奖励</label>
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center">
                        <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500" />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">启用随机奖励</span>
                      </label>
                    </div>
                    <div className="mt-2 grid grid-cols-2 gap-4">
                      <input
                        type="number"
                        placeholder="最小值"
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                      <input
                        type="number"
                        placeholder="最大值"
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 签到工单管理 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">签到工单管理</h3>
                <button 
                  onClick={() => setIsTicketModalOpen(true)}
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-black px-4 py-2 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-yellow-500/25 font-medium"
                >
                  <icons.Plus className="w-4 h-4" />
                  <span>创建工单</span>
                </button>
              </div>
              <div className="space-y-4">
                {tickets.map((ticket) => (
                  <div key={ticket.id} className="border border-gray-200 dark:border-gray-600 rounded-xl p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <h4 className="text-lg font-bold text-gray-900 dark:text-white">{ticket.name}</h4>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          ticket.isActive 
                            ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400' 
                            : 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400'
                        }`}>
                          {ticket.isActive ? '启用' : '禁用'}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button 
                          onClick={() => handleToggleTicket(ticket.id)}
                          className={`p-2 rounded-lg transition-colors ${
                            ticket.isActive 
                              ? 'text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20'
                              : 'text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20'
                          }`}
                          title={ticket.isActive ? '禁用工单' : '启用工单'}
                        >
                          {ticket.isActive ? <icons.Pause className="w-4 h-4" /> : <icons.Play className="w-4 h-4" />}
                        </button>
                        <button className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 transition-colors p-2 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20" title="编辑工单">
                          <icons.Edit className="w-4 h-4" />
                        </button>
                        <button className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20" title="删除工单">
                          <icons.Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{ticket.description}</p>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                      <div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">基础积分</span>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">{ticket.points} 分</p>
                      </div>
                      <div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">连续奖励</span>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">+{ticket.consecutiveBonus} 分</p>
                      </div>
                      <div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">最大连续</span>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">{ticket.maxConsecutive} 天</p>
                      </div>
                      <div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">作用频道</span>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">{ticket.channels.length} 个</p>
                      </div>
                    </div>
                    <div>
                      <span className="text-xs text-gray-500 dark:text-gray-400 mb-2 block">作用频道</span>
                      <div className="flex flex-wrap gap-2">
                        {ticket.channels.map((channel, index) => (
                          <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400">
                            {channel}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 签到消息模板 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">签到消息模板</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">成功签到消息</label>
                  <textarea
                    rows={3}
                    placeholder="✅ {{user}} 签到成功！获得 {{points}} 积分，已连续签到 {{streak}} 天！{{emoji}}"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">重复签到消息</label>
                  <textarea
                    rows={2}
                    placeholder="⏰ {{user}}，您今天已经签到过了，明天再来吧！"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">连续签到奖励消息</label>
                  <textarea
                    rows={2}
                    placeholder="🎉 恭喜 {{user}}！连续签到 {{streak}} 天，获得额外 {{bonus}} 积分奖励！"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  可用变量：{`{{user}}`} 用户名、{`{{points}}`} 积分、{`{{streak}}`} 连续天数、{`{{bonus}}`} 奖励积分、{`{{emoji}}`} 随机表情
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧信息面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 签到排行榜 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Trophy className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">签到排行</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">连续签到天数</p>
              </div>
            </div>
            
            <div className="space-y-3">
              {[
                { user: '张三', streak: 25, points: 1250, rank: 1 },
                { user: '李四', streak: 18, points: 890, rank: 2 },
                { user: '王五', streak: 15, points: 750, rank: 3 },
                { user: '赵六', streak: 12, points: 600, rank: 4 },
              ].map((item) => (
                <div key={item.user} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm ${
                      item.rank === 1 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25' : 
                      item.rank === 2 ? 'bg-gradient-to-br from-gray-400 to-gray-500' : 
                      item.rank === 3 ? 'bg-gradient-to-br from-orange-400 to-orange-500' : 'bg-gradient-to-br from-gray-600 to-gray-700'
                    }`}>
                      {item.rank}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{item.user}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">连续 {item.streak} 天</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-900 dark:text-white">{item.points}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">积分</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 签到趋势 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">7日趋势</h3>
            <div className="h-32 flex items-end justify-between space-x-2">
              {[95, 112, 87, 134, 127, 145, 127].map((value, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div 
                    className="w-full bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t transition-all duration-500"
                    style={{ height: `${(value / 150) * 100}%` }}
                  />
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    {['一', '二', '三', '四', '五', '六', '日'][index]}
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">827</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">本周总签到次数</div>
            </div>
          </div>

          {/* 快速操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快速操作</h3>
            <div className="space-y-3">
              <button 
                onClick={() => setIsResetModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.RefreshCw className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">重置签到</span>
              </button>
              <button 
                onClick={handleExport}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Download className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">导出数据</span>
              </button>
              <button 
                onClick={() => setIsReminderModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Bell className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">签到提醒</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 保存按钮 */}
      <div className="flex justify-end">
        <button 
          onClick={handleSaveConfig}
          disabled={isLoading}
          className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-black px-6 py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
        >
          {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
          <span>{isLoading ? '保存中...' : '保存配置'}</span>
        </button>
      </div>

      {/* 创建签到工单模态框 */}
      {isTicketModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">创建签到工单</h3>
              <button 
                onClick={() => setIsTicketModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={(e) => { e.preventDefault(); handleCreateTicket(); }} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">工单名称</label>
                  <input
                    type="text"
                    placeholder="请输入工单名称"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">基础积分</label>
                  <input
                    type="number"
                    placeholder="10"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">工单描述</label>
                <textarea
                  placeholder="请描述这个签到工单的用途..."
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">连续奖励积分</label>
                  <input
                    type="number"
                    placeholder="5"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最大连续天数</label>
                  <input
                    type="number"
                    placeholder="30"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">作用频道</label>
                  <button
                    type="button"
                    onClick={handleRefreshChannels}
                    disabled={isChannelRefreshing}
                    className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 flex items-center space-x-1"
                  >
                    <icons.RefreshCw className={`w-3 h-3 ${isChannelRefreshing ? 'animate-spin' : ''}`} />
                    <span>{isChannelRefreshing ? '刷新中...' : '刷新'}</span>
                  </button>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-xl p-4">
                  {channels.map((channel) => (
                    <label key={channel.id} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500"
                      />
                      <div className="flex items-center space-x-1">
                        <icons.Hash className="w-3 h-3 text-gray-400" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">{channel.name}</span>
                      </div>
                    </label>
                  ))}
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  选择此工单可以使用的频道
                </p>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button
                  type="button"
                  onClick={() => setIsTicketModalOpen(false)}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                >
                  {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                  <span>{isLoading ? '创建中...' : '创建工单'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 其他模态框保持不变... */}
    </div>
  );
};

export default CheckinSystem;