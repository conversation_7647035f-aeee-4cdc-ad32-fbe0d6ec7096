import React, { useState } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';

const LotterySystem: React.FC = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isDrawModalOpen, setIsDrawModalOpen] = useState(false);
  const [isChannelModalOpen, setIsChannelModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [newChannelName, setNewChannelName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const { showNotification } = useNotification();

  // 模拟频道数据
  const [channels, setChannels] = useState([
    { id: '1', name: '📅 签到大厅', type: 'text', category: '功能频道' },
    { id: '2', name: '🎮 游戏交流', type: 'text', category: '聊天频道' },
    { id: '3', name: '💬 闲聊水群', type: 'text', category: '聊天频道' },
    { id: '4', name: '📢 公告频道', type: 'text', category: '信息频道' },
    { id: '5', name: '🎁 抽奖专区', type: 'text', category: '活动频道' },
    { id: '6', name: '🏆 竞赛大厅', type: 'text', category: '活动频道' },
    { id: '7', name: '💰 积分商城', type: 'text', category: '功能频道' },
    { id: '8', name: '🎵 音乐分享', type: 'voice', category: '语音频道' },
  ]);

  const [lotteries, setLotteries] = useState([
    {
      id: '1',
      title: '新年大抽奖',
      description: '庆祝新年，丰厚奖品等你来拿！',
      status: 'active',
      type: 'channel_entry', // 新增类型：频道进入抽奖
      startTime: '2024-01-01 00:00',
      endTime: '2024-01-31 23:59',
      participants: 156,
      maxParticipants: 500,
      cost: 100,
      channels: ['🎁 抽奖专区', '🏆 竞赛大厅'], // 关联频道
      prizes: [
        { name: '一等奖：Steam游戏', quantity: 1, probability: 1 },
        { name: '二等奖：Discord Nitro', quantity: 3, probability: 5 },
        { name: '三等奖：积分奖励', quantity: 10, probability: 20 }
      ]
    },
    {
      id: '2',
      title: '每日签到抽奖',
      description: '每日签到即可参与，天天有机会！',
      status: 'active',
      type: 'manual', // 手动抽奖
      startTime: '2024-03-01 00:00',
      endTime: '2024-03-31 23:59',
      participants: 89,
      maxParticipants: 200,
      cost: 0,
      channels: [],
      prizes: [
        { name: '积分奖励', quantity: 5, probability: 30 },
        { name: '头衔升级', quantity: 2, probability: 10 }
      ]
    },
    {
      id: '3',
      title: '频道探索奖励',
      description: '进入指定频道即可获得随机奖励！',
      status: 'active',
      type: 'channel_entry',
      startTime: '2024-03-01 00:00',
      endTime: '2024-03-31 23:59',
      participants: 234,
      maxParticipants: 1000,
      cost: 0,
      channels: ['🎮 游戏交流', '💬 闲聊水群'],
      prizes: [
        { name: '小额积分', quantity: 20, probability: 50 },
        { name: '中等积分', quantity: 10, probability: 25 },
        { name: '大额积分', quantity: 5, probability: 10 }
      ]
    }
  ]);

  const drawRecords = [
    { id: '1', lottery: '新年大抽奖', user: '张三', prize: '二等奖：Discord Nitro', time: '2024-03-01 14:30', channel: '🎁 抽奖专区' },
    { id: '2', lottery: '频道探索奖励', user: '李四', prize: '小额积分', time: '2024-03-01 12:15', channel: '🎮 游戏交流' },
    { id: '3', lottery: '新年大抽奖', user: '王五', prize: '三等奖：积分奖励', time: '2024-03-01 09:45', channel: '🏆 竞赛大厅' },
    { id: '4', lottery: '频道探索奖励', user: '赵六', prize: '中等积分', time: '2024-02-29 18:20', channel: '💬 闲聊水群' },
  ];

  const filteredLotteries = lotteries.filter(lottery => 
    lottery.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lottery.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400';
      case 'ended': return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
      case 'pending': return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400';
      default: return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '进行中';
      case 'ended': return '已结束';
      case 'pending': return '未开始';
      default: return '未知';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'channel_entry': return 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-400';
      case 'manual': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400';
      default: return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'channel_entry': return '频道进入';
      case 'manual': return '手动抽奖';
      default: return '未知';
    }
  };

  const handleSyncChannels = () => {
    // 模拟同步频道数据
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('频道数据同步成功！');
      // 模拟新增一个频道
      setChannels([
        ...channels,
        { id: (channels.length + 1).toString(), name: '🎭 角色扮演', type: 'text', category: '聊天频道' }
      ]);
    }, 1500);
  };

  const handleAddChannel = () => {
    if (newChannelName.trim()) {
      const newChannel = {
        id: Date.now().toString(),
        name: newChannelName,
        type: 'text',
        category: '自定义频道'
      };
      setChannels([...channels, newChannel]);
      setNewChannelName('');
      showNotification('频道创建成功！');
    }
  };

  const handleDeleteChannel = (channelId: string) => {
    if (confirm('确定要删除这个频道吗？')) {
      setChannels(channels.filter(channel => channel.id !== channelId));
      showNotification('频道删除成功！');
    }
  };

  const handleChannelToggle = (channelName: string) => {
    setSelectedChannels(prev => 
      prev.includes(channelName) 
        ? prev.filter(c => c !== channelName)
        : [...prev, channelName]
    );
  };

  const handleCreateLottery = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('抽奖活动创建成功！');
      setIsCreateModalOpen(false);
    }, 1500);
  };

  const handleDrawLottery = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('抽奖开奖成功！恭喜中奖用户！');
      setIsDrawModalOpen(false);
    }, 2000);
  };

  const handleExportData = () => {
    setIsExportModalOpen(true);
    setExportProgress(0);
    setIsLoading(true);
    
    const interval = setInterval(() => {
      setExportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          return 100;
        }
        return prev + 20;
      });
    }, 300);
  };

  const handleEditLottery = (lotteryId: string) => {
    showNotification('抽奖活动编辑功能即将开放！');
  };

  const handleDrawNow = (lotteryId: string) => {
    setIsDrawModalOpen(true);
  };

  const handleCopyLottery = (lotteryId: string) => {
    showNotification('抽奖活动已复制！');
  };

  const handleDeleteLottery = (lotteryId: string) => {
    if (confirm('确定要删除这个抽奖活动吗？')) {
      setLotteries(lotteries.filter(lottery => lottery.id !== lotteryId));
      showNotification('抽奖活动删除成功！');
    }
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">抽奖系统</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">创建和管理抽奖活动</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setIsDrawModalOpen(true)}
            className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-purple-500/25 font-medium"
          >
            <icons.Shuffle className="w-4 h-4" />
            <span>立即开奖</span>
          </button>
          <button 
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-yellow-500/25 font-medium"
          >
            <icons.Plus className="w-4 h-4" />
            <span>创建抽奖</span>
          </button>
        </div>
      </div>

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧统计区域 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 抽奖统计卡片 */}
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Gift className="w-6 h-6 text-black font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">活动总数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{lotteries.length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" style={{ width: '100%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">系统运行正常</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                  <icons.Play className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">进行中</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{lotteries.filter(l => l.status === 'active').length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" style={{ width: '100%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">全部活跃</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                  <icons.Users className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">总参与人数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{lotteries.reduce((sum, l) => sum + l.participants, 0)}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-purple-400 to-purple-500 h-2 rounded-full" style={{ width: '75%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">参与度良好</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                  <icons.Award className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日中奖</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">12</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-orange-400 to-orange-500 h-2 rounded-full" style={{ width: '60%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">中奖率正常</p>
            </div>
          </div>

          {/* 抽奖类型分布 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">抽奖类型</h3>
            <div className="space-y-3">
              {[
                { type: 'channel_entry', name: '频道进入', count: 2, color: 'bg-purple-500' },
                { type: 'manual', name: '手动抽奖', count: 1, color: 'bg-blue-500' }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 ${item.color} rounded-full`}></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">{item.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-900 dark:text-white">{item.count}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {((item.count / lotteries.length) * 100).toFixed(0)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 中间主要内容区域 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          {/* 搜索和筛选 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <icons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="搜索抽奖活动..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex space-x-2">
                <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有状态</option>
                  <option>进行中</option>
                  <option>已结束</option>
                  <option>未开始</option>
                </select>
                <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有类型</option>
                  <option>频道进入</option>
                  <option>手动抽奖</option>
                </select>
              </div>
            </div>
          </div>

          {/* 抽奖活动列表 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">抽奖活动</h3>
              <div className="space-y-6">
                {filteredLotteries.map((lottery) => (
                  <div key={lottery.id} className="border border-gray-200 dark:border-gray-600 rounded-xl p-6 hover:border-yellow-400 dark:hover:border-yellow-500 transition-colors">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-lg font-bold text-gray-900 dark:text-white">{lottery.title}</h4>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(lottery.status)}`}>
                            {getStatusText(lottery.status)}
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(lottery.type)}`}>
                            {getTypeText(lottery.type)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{lottery.description}</p>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          <div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">参与费用</span>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {lottery.cost === 0 ? '免费' : `${lottery.cost} 积分`}
                            </p>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">参与人数</span>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {lottery.participants}/{lottery.maxParticipants}
                            </p>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">开始时间</span>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">{lottery.startTime}</p>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">结束时间</span>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">{lottery.endTime}</p>
                          </div>
                        </div>

                        {/* 关联频道显示 */}
                        {lottery.type === 'channel_entry' && lottery.channels.length > 0 && (
                          <div className="mb-4">
                            <span className="text-xs text-gray-500 dark:text-gray-400 mb-2 block">关联频道</span>
                            <div className="flex flex-wrap gap-2">
                              {lottery.channels.map((channel, index) => (
                                <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-400">
                                  <icons.Hash className="w-3 h-3 mr-1" />
                                  {channel}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}

                        <div>
                          <span className="text-xs text-gray-500 dark:text-gray-400 mb-2 block">奖品设置</span>
                          <div className="flex flex-wrap gap-2">
                            {lottery.prizes.map((prize, index) => (
                              <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 dark:bg-orange-900/20 text-orange-800 dark:text-orange-400">
                                {prize.name} (×{prize.quantity})
                              </span>
                            ))}
                          </div>
                        </div>

                        <div className="mt-4">
                          <div className="flex items-center justify-between text-sm mb-1">
                            <span className="text-gray-500 dark:text-gray-400">参与进度</span>
                            <span className="text-gray-900 dark:text-white font-medium">
                              {((lottery.participants / lottery.maxParticipants) * 100).toFixed(1)}%
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div 
                              className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${(lottery.participants / lottery.maxParticipants) * 100}%` }}
                            />
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <button 
                          onClick={() => handleEditLottery(lottery.id)}
                          className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 transition-colors p-2 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20" 
                          title="编辑活动"
                        >
                          <icons.Edit className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => handleDrawNow(lottery.id)}
                          className="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 transition-colors p-2 rounded-lg hover:bg-purple-50 dark:hover:bg-purple-900/20" 
                          title="立即开奖"
                        >
                          <icons.Shuffle className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => handleCopyLottery(lottery.id)}
                          className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 transition-colors p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900/20" 
                          title="复制活动"
                        >
                          <icons.Copy className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => handleDeleteLottery(lottery.id)}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20" 
                          title="删除活动"
                        >
                          <icons.Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧信息面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 中奖记录 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Trophy className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">最近中奖</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">实时更新</p>
              </div>
            </div>
            
            <div className="space-y-4">
              {drawRecords.map((record) => (
                <div key={record.id} className="p-3 border border-gray-200 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-orange-500 rounded-lg flex items-center justify-center shadow-lg">
                      <icons.Award className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{record.user}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{record.time}</div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 ml-11 mb-1">
                    {record.lottery}
                  </div>
                  <div className="text-xs text-purple-600 dark:text-purple-400 ml-11 font-medium mb-1">
                    {record.prize}
                  </div>
                  {record.channel && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 ml-11 flex items-center">
                      <icons.Hash className="w-3 h-3 mr-1" />
                      {record.channel}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* 频道活跃度 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">频道活跃度</h3>
            <div className="space-y-3">
              {[
                { channel: '🎁 抽奖专区', activity: 85, draws: 23 },
                { channel: '🏆 竞赛大厅', activity: 72, draws: 18 },
                { channel: '🎮 游戏交流', activity: 68, draws: 15 },
                { channel: '💬 闲聊水群', activity: 45, draws: 8 }
              ].map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900 dark:text-white flex items-center">
                      <icons.Hash className="w-3 h-3 mr-1" />
                      {item.channel}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">{item.draws} 次</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-purple-400 to-purple-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${item.activity}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 快速操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快速操作</h3>
            <div className="space-y-3">
              <button 
                onClick={handleSyncChannels}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.RefreshCw className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">同步频道</span>
              </button>
              <button 
                onClick={() => setIsChannelModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Hash className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">管理频道</span>
              </button>
              <button 
                onClick={handleExportData}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Download className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">导出数据</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 创建抽奖模态框 */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">创建抽奖活动</h3>
              <button 
                onClick={() => setIsCreateModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={handleCreateLottery} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">活动标题</label>
                  <input
                    type="text"
                    placeholder="请输入活动标题"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">抽奖类型</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="manual">手动抽奖</option>
                    <option value="channel_entry">频道进入抽奖</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">活动描述</label>
                <textarea
                  placeholder="请输入活动描述..."
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              {/* 频道选择区域 */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">关联频道（频道进入抽奖）</label>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={handleSyncChannels}
                      className="text-xs px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center space-x-1"
                    >
                      <icons.RefreshCw className="w-3 h-3" />
                      <span>同步</span>
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsChannelModalOpen(true)}
                      className="text-xs px-3 py-1 bg-yellow-100 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400 rounded-lg hover:bg-yellow-200 dark:hover:bg-yellow-900/30 transition-colors flex items-center space-x-1"
                    >
                      <icons.Plus className="w-3 h-3" />
                      <span>新增</span>
                    </button>
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-xl p-4">
                  {channels.map((channel) => (
                    <label key={channel.id} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedChannels.includes(channel.name)}
                        onChange={() => handleChannelToggle(channel.name)}
                        className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500"
                      />
                      <div className="flex items-center space-x-1">
                        <icons.Hash className="w-3 h-3 text-gray-400" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">{channel.name}</span>
                      </div>
                    </label>
                  ))}
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  选择的频道：用户进入这些频道时将自动参与抽奖
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">参与费用（积分）</label>
                  <input
                    type="number"
                    placeholder="0"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">开始时间</label>
                  <input
                    type="datetime-local"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">结束时间</label>
                  <input
                    type="datetime-local"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最大参与人数</label>
                <input
                  type="number"
                  placeholder="500"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">奖品设置</label>
                <div className="space-y-3">
                  <div className="grid grid-cols-3 gap-3">
                    <input
                      type="text"
                      placeholder="奖品名称"
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                    <input
                      type="number"
                      placeholder="数量"
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                    <input
                      type="number"
                      placeholder="中奖概率(%)"
                      className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <button type="button" className="text-yellow-600 dark:text-yellow-400 text-sm hover:text-yellow-700 dark:hover:text-yellow-300 flex items-center space-x-1">
                    <icons.Plus className="w-4 h-4" />
                    <span>添加奖品</span>
                  </button>
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                >
                  {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                  <span>{isLoading ? '创建中...' : '创建活动'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 频道管理模态框 */}
      {isChannelModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">频道管理</h3>
              <button 
                onClick={() => setIsChannelModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {/* 新增频道 */}
            <div className="mb-6 p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
              <h4 className="text-md font-bold text-gray-900 dark:text-white mb-3">新增频道</h4>
              <div className="flex space-x-3">
                <input
                  type="text"
                  placeholder="频道名称（如：🎁 新抽奖频道）"
                  value={newChannelName}
                  onChange={(e) => setNewChannelName(e.target.value)}
                  className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <button
                  onClick={handleAddChannel}
                  className="px-4 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium"
                >
                  添加
                </button>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                新增的频道将同步到Discord服务器
              </p>
            </div>

            {/* 频道列表 */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-md font-bold text-gray-900 dark:text-white">现有频道</h4>
                <button
                  onClick={handleSyncChannels}
                  className="text-sm px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center space-x-1"
                >
                  <icons.RefreshCw className="w-3 h-3" />
                  <span>同步频道</span>
                </button>
              </div>
              
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {channels.map((channel) => (
                  <div key={channel.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700">
                    <div className="flex items-center space-x-3">
                      <icons.Hash className="w-4 h-4 text-gray-400" />
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">{channel.name}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">{channel.category}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        channel.type === 'text' 
                          ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400'
                          : 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400'
                      }`}>
                        {channel.type === 'text' ? '文字' : '语音'}
                      </span>
                      <button 
                        onClick={() => handleDeleteChannel(channel.id)}
                        className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20"
                        title="删除频道"
                      >
                        <icons.Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600 mt-6">
              <button
                onClick={() => {
                  setIsChannelModalOpen(false);
                  showNotification('频道管理已完成！');
                }}
                className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium"
              >
                完成
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 立即开奖模态框 */}
      {isDrawModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">立即开奖</h3>
              <button 
                onClick={() => setIsDrawModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {isLoading ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.Shuffle className="w-8 h-8 text-white animate-spin" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">正在抽奖...</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">请稍候，正在随机选择中奖者</p>
              </div>
            ) : (
              <form onSubmit={handleDrawLottery} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">选择抽奖活动</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>请选择抽奖活动</option>
                    {lotteries.filter(l => l.status === 'active').map(lottery => (
                      <option key={lottery.id} value={lottery.id}>{lottery.title}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">开奖数量</label>
                  <input
                    type="number"
                    placeholder="1"
                    min="1"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsDrawModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg shadow-purple-500/25 font-medium"
                  >
                    开始抽奖
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 导出数据模态框 */}
      {isExportModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">导出抽奖数据</h3>
              {!isLoading && (
                <button 
                  onClick={() => setIsExportModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <icons.X className="w-5 h-5" />
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  {isLoading ? (
                    <icons.Download className="w-8 h-8 text-black animate-pulse" />
                  ) : (
                    <icons.CheckCircle className="w-8 h-8 text-black" />
                  )}
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {isLoading ? '正在导出数据...' : '导出完成！'}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {isLoading ? '请稍候，正在生成抽奖数据文件' : '抽奖数据已成功导出为Excel文件'}
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">导出进度</span>
                  <span className="font-medium text-gray-900 dark:text-white">{exportProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${exportProgress}%` }}
                  />
                </div>
              </div>
              
              {!isLoading && (
                <button
                  onClick={() => {
                    setIsExportModalOpen(false);
                    showNotification('抽奖数据导出成功！');
                  }}
                  className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-medium"
                >
                  完成
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LotterySystem;