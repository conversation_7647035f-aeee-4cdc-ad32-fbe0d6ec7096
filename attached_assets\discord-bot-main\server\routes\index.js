import express from 'express';

import settingsRoutes from './settings.js';
import botRoutes from './bot.js';
import usersRoutes from './users.js';
import pointsRoutes from './points.js';
import checkinRoutes from './checkin.js';
import titlesRoutes from './titles.js';
import lotteryRoutes from './lottery.js';
import automationRoutes from './automation.js';
import analyticsRoutes from './analytics.js';
import logsRoutes from './logs.js';

const router = express.Router();

router.use('/api/settings', settingsRoutes);
router.use('/api/bot', botRoutes);
router.use('/api/users', usersRoutes);
router.use('/api/points', pointsRoutes);
router.use('/api/checkin', checkinRoutes);
router.use('/api/titles', titlesRoutes);
router.use('/api/lottery', lotteryRoutes);
router.use('/api/automation', automationRoutes);
router.use('/api/analytics', analyticsRoutes);
router.use('/api/logs', logsRoutes);

export default router;