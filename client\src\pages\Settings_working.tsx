import React, { useState, useEffect } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';
import { useData } from '../contexts/DataContext';
import { getApiBaseUrl } from '../utils/api.js';

const Settings: React.FC = () => {
  const [isTokenModalOpen, setIsTokenModalOpen] = useState(false);
  const [isBotTestModalOpen, setIsBotTestModalOpen] = useState(false);
  const [isBackupModalOpen, setIsBackupModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [botToken, setBotToken] = useState('');
  const [testResult, setTestResult] = useState<any>(null);
  const [settings, setSettings] = useState<any>({});
  const { showNotification } = useNotification();
  const { botStatus, serverData, isConnected, refreshData } = useData();

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/settings`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Server returned non-JSON response');
      }
      
      const data = await response.json();
      if (data.success) {
        setSettings(data.data);
        setBotToken(data.data.bot_token || '');
      } else {
        throw new Error(data.message || 'Failed to load settings');
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      showNotification('加载设置失败，请检查服务器连接', 'error');
    }
  };

  const handleTestToken = async () => {
    if (!botToken.trim()) {
      showNotification('请输入机器人令牌', 'error');
      return;
    }

    setIsLoading(true);
    setTestResult(null);

    try {
      const response = await fetch(`${getApiBaseUrl()}/api/bot/test-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: botToken }),
      });

      const data = await response.json();
      setTestResult(data);

      if (data.success) {
        showNotification('Token验证成功！', 'success');
      } else {
        showNotification(data.message || 'Token验证失败', 'error');
      }
    } catch (error) {
      console.error('Token test error:', error);
      setTestResult({
        success: false,
        message: '网络错误，请稍后重试'
      });
      showNotification('测试失败，请检查网络连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveAndApplyToken = async () => {
    if (!botToken.trim()) {
      showNotification('请输入机器人令牌', 'error');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`${getApiBaseUrl()}/api/settings/bot_token`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: botToken }),
      });

      const data = await response.json();

      if (data.success) {
        showNotification(data.message || 'Token保存成功，正在连接Discord...', 'success');
        setIsTokenModalOpen(false);
        
        // Refresh data to get updated bot status
        setTimeout(() => {
          refreshData();
        }, 2000);
        
        // Reload settings to get the masked token
        setTimeout(() => {
          loadSettings();
        }, 1000);
        
      } else {
        showNotification(data.message || 'Token保存失败', 'error');
      }
    } catch (error) {
      console.error('Save token error:', error);
      showNotification('保存失败，请检查网络连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncData = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/sync/data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        showNotification('数据同步完成', 'success');
        refreshData();
      } else {
        showNotification(data.message || '同步失败', 'error');
      }
    } catch (error) {
      console.error('Sync error:', error);
      showNotification('同步失败，请检查网络连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartBot = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch(`${getApiBaseUrl()}/api/bot/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        showNotification('机器人启动成功', 'success');
        refreshData();
      } else {
        showNotification(data.message || '启动失败', 'error');
      }
    } catch (error) {
      console.error('Start bot error:', error);
      showNotification('启动失败，请检查网络连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            系统设置
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            配置机器人令牌和Discord API连接
          </p>
        </div>

        {/* Bot Status Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              机器人状态
            </h2>
            <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
              botStatus.connected 
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                botStatus.connected ? 'bg-green-500' : 'bg-red-500'
              }`} />
              {botStatus.connected ? '已连接' : '未连接'}
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {botStatus.guilds || 0}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">服务器</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {botStatus.users || 0}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">用户</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {botStatus.latency || 0}ms
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">延迟</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {Math.floor((Date.now() - (botStatus.uptime || 0)) / 1000)}s
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">运行时间</p>
            </div>
          </div>

          {botStatus.error && (
            <div className="bg-red-100 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
              <p className="text-red-800 dark:text-red-300 text-sm">
                <strong>错误:</strong> {botStatus.error}
              </p>
            </div>
          )}

          <div className="flex gap-3">
            <button
              onClick={() => setIsTokenModalOpen(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              配置Token
            </button>
            <button
              onClick={handleStartBot}
              disabled={isLoading}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              {isLoading ? '启动中...' : '启动机器人'}
            </button>
            <button
              onClick={handleSyncData}
              disabled={isLoading || !botStatus.connected}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
            >
              {isLoading ? '同步中...' : '同步数据'}
            </button>
          </div>
        </div>

        {/* Token Configuration Modal */}
        {isTokenModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
                配置Discord机器人令牌
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    机器人令牌
                  </label>
                  <input
                    type="password"
                    value={botToken}
                    onChange={(e) => setBotToken(e.target.value)}
                    placeholder="输入Discord机器人令牌"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={handleTestToken}
                    disabled={isLoading}
                    className="flex-1 px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors disabled:opacity-50"
                  >
                    {isLoading ? '测试中...' : '测试Token'}
                  </button>
                  <button
                    onClick={handleSaveAndApplyToken}
                    disabled={isLoading}
                    className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                  >
                    {isLoading ? '保存中...' : '保存并应用'}
                  </button>
                </div>

                {testResult && (
                  <div className={`p-3 rounded-lg ${
                    testResult.success 
                      ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300'
                      : 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300'
                  }`}>
                    <p className="text-sm font-medium">
                      {testResult.success ? '✓ 测试成功' : '✗ 测试失败'}
                    </p>
                    <p className="text-sm mt-1">{testResult.message}</p>
                    {testResult.data && testResult.success && (
                      <div className="mt-2 text-xs">
                        <p>机器人: {testResult.data.bot_name}</p>
                        <p>服务器: {testResult.data.guilds}</p>
                        <p>用户: {testResult.data.users}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  onClick={() => {
                    setIsTokenModalOpen(false);
                    setTestResult(null);
                  }}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Server Data Card */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            服务器数据同步
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {serverData.memberCount}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">总成员</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {serverData.activeChannels}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">活跃频道</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {serverData.roles?.length || 0}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">角色数量</p>
            </div>
          </div>

          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            所有数据都通过Discord API实时获取，确保信息的准确性和时效性。
          </p>
        </div>
      </div>
    </div>
  );
};

export default Settings;