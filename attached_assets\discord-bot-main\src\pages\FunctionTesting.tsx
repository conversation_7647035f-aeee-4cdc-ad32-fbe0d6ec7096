import React, { useState } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';

const FunctionTesting: React.FC = () => {
  const [isTestModalOpen, setIsTestModalOpen] = useState(false);
  const [selectedTest, setSelectedTest] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<{[key: string]: any}>({});
  const { showNotification } = useNotification();

  const testCategories = [
    {
      id: 'automation',
      name: '自动化规则测试',
      icon: 'Workflow',
      description: '测试自动化规则的触发和执行',
      color: 'from-purple-500 to-purple-600',
      tests: [
        {
          id: 'user_join_test',
          name: '用户加入测试',
          description: '模拟新用户加入服务器，测试欢迎消息和角色分配',
          status: 'ready',
          lastRun: null,
          duration: '2.3s'
        },
        {
          id: 'message_points_test',
          name: '消息积分测试',
          description: '模拟用户发送消息，测试积分奖励系统',
          status: 'passed',
          lastRun: '2024-03-01 14:30',
          duration: '0.8s'
        },
        {
          id: 'violation_detection_test',
          name: '违规检测测试',
          description: '模拟违规内容，测试自动删除和警告功能',
          status: 'failed',
          lastRun: '2024-03-01 12:15',
          duration: '1.2s'
        }
      ]
    },
    {
      id: 'checkin',
      name: '签到系统测试',
      icon: 'Calendar',
      description: '测试签到功能和奖励机制',
      color: 'from-green-500 to-green-600',
      tests: [
        {
          id: 'daily_checkin_test',
          name: '每日签到测试',
          description: '模拟用户签到，验证积分发放和连续天数计算',
          status: 'passed',
          lastRun: '2024-03-01 09:15',
          duration: '1.5s'
        },
        {
          id: 'consecutive_bonus_test',
          name: '连续签到奖励测试',
          description: '测试连续签到的额外奖励机制',
          status: 'ready',
          lastRun: null,
          duration: '2.1s'
        },
        {
          id: 'duplicate_checkin_test',
          name: '重复签到测试',
          description: '验证重复签到的防护机制',
          status: 'passed',
          lastRun: '2024-03-01 08:45',
          duration: '0.5s'
        }
      ]
    },
    {
      id: 'points',
      name: '积分系统测试',
      icon: 'Star',
      description: '测试积分发放、扣除和计算',
      color: 'from-yellow-400 to-yellow-500',
      tests: [
        {
          id: 'points_award_test',
          name: '积分发放测试',
          description: '测试手动和自动积分发放功能',
          status: 'passed',
          lastRun: '2024-03-01 11:20',
          duration: '1.0s'
        },
        {
          id: 'points_deduction_test',
          name: '积分扣除测试',
          description: '测试违规行为的积分扣除机制',
          status: 'ready',
          lastRun: null,
          duration: '0.8s'
        },
        {
          id: 'points_calculation_test',
          name: '积分计算测试',
          description: '验证积分计算的准确性和一致性',
          status: 'passed',
          lastRun: '2024-03-01 10:30',
          duration: '0.3s'
        }
      ]
    },
    {
      id: 'titles',
      name: '头衔系统测试',
      icon: 'Crown',
      description: '测试头衔晋升和权限分配',
      color: 'from-orange-500 to-orange-600',
      tests: [
        {
          id: 'title_upgrade_test',
          name: '头衔晋升测试',
          description: '模拟积分达到阈值，测试自动头衔晋升',
          status: 'ready',
          lastRun: null,
          duration: '1.8s'
        },
        {
          id: 'title_notification_test',
          name: '晋升通知测试',
          description: '测试头衔晋升时的通知消息发送',
          status: 'passed',
          lastRun: '2024-03-01 13:45',
          duration: '0.9s'
        }
      ]
    },
    {
      id: 'lottery',
      name: '抽奖系统测试',
      icon: 'Gift',
      description: '测试抽奖功能和奖品发放',
      color: 'from-pink-500 to-pink-600',
      tests: [
        {
          id: 'lottery_draw_test',
          name: '抽奖执行测试',
          description: '模拟抽奖过程，验证随机性和公平性',
          status: 'ready',
          lastRun: null,
          duration: '2.5s'
        },
        {
          id: 'prize_distribution_test',
          name: '奖品发放测试',
          description: '测试中奖后的奖品发放机制',
          status: 'failed',
          lastRun: '2024-02-29 16:20',
          duration: '3.2s'
        }
      ]
    },
    {
      id: 'commands',
      name: 'Slash 指令测试',
      icon: 'Terminal',
      description: '测试 Slash 指令的响应和执行',
      color: 'from-blue-500 to-blue-600',
      tests: [
        {
          id: 'command_response_test',
          name: '指令响应测试',
          description: '测试各种 Slash 指令的响应速度和准确性',
          status: 'passed',
          lastRun: '2024-03-01 15:10',
          duration: '0.4s'
        },
        {
          id: 'command_permission_test',
          name: '指令权限测试',
          description: '验证指令权限控制和频道限制',
          status: 'ready',
          lastRun: null,
          duration: '0.6s'
        }
      ]
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400';
      case 'failed': return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400';
      case 'running': return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400';
      case 'ready': return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
      default: return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'passed': return '通过';
      case 'failed': return '失败';
      case 'running': return '运行中';
      case 'ready': return '就绪';
      default: return '未知';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed': return icons.CheckCircle;
      case 'failed': return icons.XCircle;
      case 'running': return icons.Loader2;
      case 'ready': return icons.Play;
      default: return icons.Circle;
    }
  };

  const handleRunTest = (test: any) => {
    setSelectedTest(test);
    setIsTestModalOpen(true);
  };

  const handleExecuteTest = () => {
    setIsLoading(true);
    
    // 模拟测试执行
    setTimeout(() => {
      const success = Math.random() > 0.3; // 70% 成功率
      const newStatus = success ? 'passed' : 'failed';
      const duration = (Math.random() * 3 + 0.5).toFixed(1) + 's';
      
      setTestResults({
        ...testResults,
        [selectedTest.id]: {
          status: newStatus,
          lastRun: new Date().toLocaleString('zh-CN'),
          duration: duration,
          details: success 
            ? '测试执行成功，所有检查点都通过了验证。'
            : '测试执行失败，发现了一些需要修复的问题。'
        }
      });
      
      setIsLoading(false);
      showNotification(
        success ? `测试 "${selectedTest.name}" 执行成功！` : `测试 "${selectedTest.name}" 执行失败！`,
        success ? 'success' : 'error'
      );
      
      setTimeout(() => {
        setIsTestModalOpen(false);
      }, 2000);
    }, 3000);
  };

  const handleRunAllTests = () => {
    showNotification('批量测试功能即将开放！', 'info');
  };

  const handleRunCategoryTests = (categoryId: string) => {
    showNotification(`${testCategories.find(c => c.id === categoryId)?.name}批量测试功能即将开放！`, 'info');
  };

  const getTotalTests = () => {
    return testCategories.reduce((total, category) => total + category.tests.length, 0);
  };

  const getPassedTests = () => {
    return testCategories.reduce((total, category) => 
      total + category.tests.filter(test => 
        (testResults[test.id]?.status || test.status) === 'passed'
      ).length, 0
    );
  };

  const getFailedTests = () => {
    return testCategories.reduce((total, category) => 
      total + category.tests.filter(test => 
        (testResults[test.id]?.status || test.status) === 'failed'
      ).length, 0
    );
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">功能测试</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">测试和验证 Bot 各项功能的正常运行</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={handleRunAllTests}
            className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-green-500/25 font-medium"
          >
            <icons.PlayCircle className="w-4 h-4" />
            <span>运行所有测试</span>
          </button>
        </div>
      </div>

      {/* 测试统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
              <icons.TestTube className="w-6 h-6 text-black font-bold" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">测试总数</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{getTotalTests()}</p>
            </div>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" style={{ width: '100%' }}></div>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">覆盖所有核心功能</p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
              <icons.CheckCircle className="w-6 h-6 text-white font-bold" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">通过测试</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{getPassedTests()}</p>
            </div>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" style={{ width: `${(getPassedTests() / getTotalTests()) * 100}%` }}></div>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">成功率 {((getPassedTests() / getTotalTests()) * 100).toFixed(1)}%</p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-red-500 rounded-xl flex items-center justify-center shadow-lg shadow-red-500/25">
              <icons.XCircle className="w-6 h-6 text-white font-bold" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">失败测试</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">{getFailedTests()}</p>
            </div>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div className="bg-gradient-to-r from-red-400 to-red-500 h-2 rounded-full" style={{ width: `${(getFailedTests() / getTotalTests()) * 100}%` }}></div>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">需要修复</p>
        </div>
      </div>

      {/* 测试分类列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {testCategories.map((category) => {
          const Icon = icons[category.icon as keyof typeof icons] as React.ComponentType<any>;
          const totalTests = category.tests.length;
          const passedTests = category.tests.filter(test => 
            (testResults[test.id]?.status || test.status) === 'passed'
          ).length;
          
          return (
            <div key={category.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className={`bg-gradient-to-r ${category.color} p-4 text-white`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                      <Icon className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold">{category.name}</h3>
                      <p className="text-sm text-white/80">{category.description}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold">{passedTests}/{totalTests}</div>
                    <div className="text-xs text-white/80">通过率</div>
                  </div>
                </div>
              </div>
              
              <div className="p-4">
                <div className="space-y-3">
                  {category.tests.map((test) => {
                    const currentTest = testResults[test.id] || test;
                    const StatusIcon = getStatusIcon(currentTest.status);
                    
                    return (
                      <div key={test.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getStatusColor(currentTest.status)}`}>
                            <StatusIcon className={`w-4 h-4 ${currentTest.status === 'running' ? 'animate-spin' : ''}`} />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white text-sm">{test.name}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {currentTest.lastRun ? `最后运行: ${currentTest.lastRun}` : '从未运行'}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500 dark:text-gray-400">{currentTest.duration}</span>
                          <button 
                            onClick={() => handleRunTest(test)}
                            disabled={currentTest.status === 'running'}
                            className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 transition-colors p-2 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20 disabled:opacity-50" 
                            title="运行测试"
                          >
                            {currentTest.status === 'running' ? (
                              <icons.Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <icons.Play className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      </div>
                    );
                  })}
                </div>
                
                <div className="mt-4 flex justify-end">
                  <button 
                    onClick={() => handleRunCategoryTests(category.id)}
                    className={`bg-gradient-to-r ${category.color} text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg font-medium text-sm`}
                  >
                    <icons.Play className="w-3 h-3" />
                    <span>运行全部</span>
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 运行测试模态框 */}
      {isTestModalOpen && selectedTest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">运行测试</h3>
              {!isLoading && (
                <button 
                  onClick={() => setIsTestModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <icons.X className="w-5 h-5" />
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  {isLoading ? (
                    <icons.Loader2 className="w-8 h-8 text-black animate-spin" />
                  ) : (
                    <icons.TestTube className="w-8 h-8 text-black" />
                  )}
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {isLoading ? '正在执行测试...' : selectedTest.name}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {isLoading ? '请稍候，正在验证功能是否正常工作' : selectedTest.description}
                </p>
              </div>
              
              {isLoading && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">测试进度</span>
                    <span className="font-medium text-gray-900 dark:text-white">执行中...</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full animate-pulse"></div>
                  </div>
                </div>
              )}
              
              {!isLoading && (
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    onClick={() => setIsTestModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleExecuteTest}
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium"
                  >
                    开始测试
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FunctionTesting;