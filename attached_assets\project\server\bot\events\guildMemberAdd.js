import { logger } from '../../utils/logger.js';
import { db } from '../../database/init.js';
import { addPoints } from '../../utils/points.js';

export async function handleGuildMemberAdd(member) {
  try {
    // Add user to database
    await db.runAsync(`
      INSERT OR REPLACE INTO users (
        id, username, discriminator, avatar, join_date, last_active
      ) VALUES (?, ?, ?, ?, ?, ?)
    `, [
      member.id,
      member.user.username,
      member.user.discriminator,
      member.user.avatar,
      new Date().toISOString(),
      new Date().toISOString()
    ]);

    // Send welcome message
    const welcomeChannel = member.guild.systemChannel;
    if (welcomeChannel) {
      await welcomeChannel.send({
        embeds: [{
          title: '🎉 欢迎新成员！',
          description: `欢迎 ${member.user} 加入我们的服务器！\n\n请阅读规则并开始您的旅程！`,
          color: 0xD6F36F,
          thumbnail: {
            url: member.user.displayAvatarURL()
          },
          timestamp: new Date().toISOString()
        }]
      });
    }

    // Give welcome points
    await addPoints(member.id, 50, '新用户欢迎奖励', 'earned');

    logger.info(`New member joined: ${member.user.tag} (${member.id})`);
  } catch (error) {
    logger.error('Error handling new member:', error);
  }
}