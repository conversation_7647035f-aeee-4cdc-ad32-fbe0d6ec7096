import { REST, Routes } from 'discord.js';
import { getSetting } from '../../utils/settings.js';
import { logger } from '../../utils/logger.js';

// Import command files
import { checkinCommand } from './checkin.js';
import { pointsCommand } from './points.js';
import { leaderboardCommand } from './leaderboard.js';
import { helpCommand } from './help.js';

const commands = [
  checkinCommand,
  pointsCommand,
  leaderboardCommand,
  helpCommand
];

export async function registerCommands(client) {
  try {
    const token = await getSetting('bot_token');
    if (!token) return;

    // Add commands to client collection
    commands.forEach(command => {
      client.commands.set(command.data.name, command);
    });

    // Register slash commands with Discord
    const rest = new REST().setToken(token);
    const commandData = commands.map(command => command.data.toJSON());

    logger.info('Started refreshing application (/) commands.');

    // Register commands globally (takes up to 1 hour to update)
    await rest.put(
      Routes.applicationCommands(client.user.id),
      { body: commandData }
    );

    logger.info('Successfully reloaded application (/) commands.');
  } catch (error) {
    logger.error('Error registering commands:', error);
  }
}