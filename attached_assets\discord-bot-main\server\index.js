import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import { createServer } from 'http';
import { setupWebSocket } from './websocket/index.js';
import routes from './routes/index.js';
import { initDatabase } from './database/init.js';
import { initBot } from './bot/index.js';
import { logger } from './utils/logger.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// 更宽松的 CORS 配置
app.use(cors({
  origin: true, // 允许所有来源
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 更宽松的 Helmet 配置
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: false
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 更宽松的速率限制
app.use(
  rateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 1000, // 增加到1000次请求
    standardHeaders: true,
    legacyHeaders: false,
  })
);

// 添加请求日志中间件
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path} - ${req.ip}`);
  next();
});

// Routes
app.use(routes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    port: PORT,
    env: process.env.NODE_ENV || 'development'
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`
  });
});

// 全局错误处理
app.use((err, req, res, next) => {
  logger.error('Global error handler:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

const httpServer = createServer(app);

// Setup WebSocket
try {
  setupWebSocket(httpServer);
  logger.info('WebSocket server setup completed');
} catch (error) {
  logger.error('Failed to setup WebSocket:', error);
}

// Initialize database and start server
async function startServer() {
  try {
    logger.info('Starting Discord Bot Admin Server...');
    
    // Initialize database
    try {
      await initDatabase();
      logger.info('Database initialized successfully');
    } catch (dbError) {
      logger.error('Database initialization failed:', dbError);
      // 继续启动服务器，即使数据库初始化失败
    }
    
    // Start HTTP server
    httpServer.listen(PORT, '0.0.0.0', () => {
      logger.info(`✅ Server running at http://0.0.0.0:${PORT}`);
      logger.info(`✅ Health check: http://localhost:${PORT}/health`);
      
      // Initialize bot after server starts
      setTimeout(() => {
        initBot().catch(error => {
          logger.error('Failed to initialize bot:', error);
        });
      }, 2000);
    });

    // 处理服务器错误
    httpServer.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        logger.error(`Port ${PORT} is already in use`);
        process.exit(1);
      } else {
        logger.error('Server error:', error);
      }
    });
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  httpServer.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  httpServer.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
startServer();