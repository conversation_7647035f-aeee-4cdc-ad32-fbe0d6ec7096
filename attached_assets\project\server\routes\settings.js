import express from 'express';
import { getSetting, setSetting, getAllSettings } from '../utils/settings.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Get all settings
router.get('/', async (req, res) => {
  try {
    const settings = await getAllSettings();
    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    logger.error('Error getting settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get settings'
    });
  }
});

// Get specific setting
router.get('/:key', async (req, res) => {
  try {
    const value = await getSetting(req.params.key);
    res.json({
      success: true,
      data: { key: req.params.key, value }
    });
  } catch (error) {
    logger.error('Error getting setting:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get setting'
    });
  }
});

// Update setting
router.put('/:key', async (req, res) => {
  try {
    const { value } = req.body;
    const success = await setSetting(req.params.key, value);
    
    if (success) {
      res.json({
        success: true,
        message: 'Setting updated successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to update setting'
      });
    }
  } catch (error) {
    logger.error('Error updating setting:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update setting'
    });
  }
});

export default router;