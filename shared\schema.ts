import { pgTable, text, serial, integer, boolean, timestamp, jsonb, varchar, unique } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password"), // Optional for Discord users
  discordId: text("discord_id").unique(),
  avatar: text("avatar"),
  joinDate: timestamp("join_date").defaultNow(),
  points: integer("points").default(0),
  titles: text("titles").array(),
  status: text("status").default("offline"),
  roles: text("roles").array().default([]),
  lastActive: timestamp("last_active").defaultNow(),
  messages: integer("messages").default(0),
  voiceTime: text("voice_time").default("0h 0m"),
});

export const pointRecords = pgTable("point_records", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  amount: integer("amount").notNull(),
  reason: text("reason").notNull(),
  type: text("type").notNull(), // 'earned' or 'deducted'
  oldPoints: integer("old_points").default(0), // 操作前的积分
  newPoints: integer("new_points").default(0), // 操作后的积分
  createdAt: timestamp("created_at").defaultNow(),
});

export const checkinRecords = pgTable("checkin_records", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  checkinDate: timestamp("checkin_date").defaultNow(),
  consecutiveDays: integer("consecutive_days").default(1),
  pointsEarned: integer("points_earned").default(10),
  bonusPoints: integer("bonus_points").default(0), // 连续签到奖励
  randomBonus: integer("random_bonus").default(0), // 随机奖励
  channelId: text("channel_id"), // 签到频道
});

// 签到配置表
export const checkinConfig = pgTable("checkin_config", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  basePoints: integer("base_points").default(10).notNull(), // 单次签到基础积分
  streakEnabled: boolean("streak_enabled").default(true), // 连续签到加成
  streakDays: integer("streak_days").default(7).notNull(), // 连续多少天触发加成
  streakBonusPoints: integer("streak_bonus_points").default(50).notNull(), // 连续签到暴击积分
  allowedChannels: text("allowed_channels").array().default([]).notNull(), // 允许签到的频道ID数组
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const userConsecutiveCheckins = pgTable("user_consecutive_checkins", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  consecutiveDays: integer("consecutive_days").default(0).notNull(),
  lastCheckinDate: text("last_checkin_date"), // 使用text存储日期字符串
  lastUpdated: timestamp("last_updated").defaultNow().notNull(),
}, (table) => ({
  uniqueUserId: unique().on(table.userId),
}));

export const botSettings = pgTable("bot_settings", {
  id: serial("id").primaryKey(),
  key: text("key").notNull().unique(),
  value: text("value"),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const automationRules = pgTable("automation_rules", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  trigger: text("trigger").notNull(),
  actions: text("actions").array(),
  isActive: boolean("is_active").default(true),
  lastTriggered: timestamp("last_triggered"),
  triggerCount: integer("trigger_count").default(0),
  priority: text("priority").default("medium"),
  createdAt: timestamp("created_at").defaultNow(),
});

export const systemLogs = pgTable("system_logs", {
  id: serial("id").primaryKey(),
  time: timestamp("time").defaultNow(),
  user: text("user").notNull(),
  module: text("module").notNull(),
  action: text("action").notNull(),
  level: text("level").notNull(), // 'info', 'warning', 'error', 'success'
  ip: text("ip"),
  userAgent: text("user_agent"),
  details: text("details"),
  duration: text("duration"),
});

export const titles = pgTable("titles", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),
  description: text("description").notNull(),
  minPoints: integer("min_points").notNull(),
  maxPoints: integer("max_points"),
  color: text("color").notNull(),
  icon: text("icon").notNull(),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  pointRecords: many(pointRecords),
  checkinRecords: many(checkinRecords),
}));

export const pointRecordsRelations = relations(pointRecords, ({ one }) => ({
  user: one(users, {
    fields: [pointRecords.userId],
    references: [users.id],
  }),
}));

export const checkinRecordsRelations = relations(checkinRecords, ({ one }) => ({
  user: one(users, {
    fields: [checkinRecords.userId],
    references: [users.id],
  }),
}));

// Insert schemas  
export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  points: true,
  status: true,
  roles: true,
  lastActive: true,
  messages: true,
  voiceTime: true,
});

export const insertPointRecordSchema = createInsertSchema(pointRecords).omit({
  id: true,
  createdAt: true,
});

export const insertCheckinRecordSchema = createInsertSchema(checkinRecords).omit({
  id: true,
  checkinDate: true,
});

export const insertBotSettingsSchema = createInsertSchema(botSettings).omit({
  id: true,
  updatedAt: true,
});

export const insertAutomationRuleSchema = createInsertSchema(automationRules).omit({
  id: true,
  createdAt: true,
  lastTriggered: true,
  triggerCount: true,
});

export const insertSystemLogSchema = createInsertSchema(systemLogs).omit({
  id: true,
  time: true,
});

export const insertTitleSchema = createInsertSchema(titles).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertCheckinConfigSchema = createInsertSchema(checkinConfig).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertUserConsecutiveCheckinSchema = createInsertSchema(userConsecutiveCheckins).omit({
  id: true,
  lastUpdated: true,
});

// Types
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type PointRecord = typeof pointRecords.$inferSelect;
export type InsertPointRecord = z.infer<typeof insertPointRecordSchema>;
export type CheckinRecord = typeof checkinRecords.$inferSelect;
export type InsertCheckinRecord = z.infer<typeof insertCheckinRecordSchema>;
export type BotSettings = typeof botSettings.$inferSelect;
export type InsertBotSettings = z.infer<typeof insertBotSettingsSchema>;
export type AutomationRule = typeof automationRules.$inferSelect;
export type InsertAutomationRule = z.infer<typeof insertAutomationRuleSchema>;
export type SystemLog = typeof systemLogs.$inferSelect;
export type InsertSystemLog = z.infer<typeof insertSystemLogSchema>;
export type Title = typeof titles.$inferSelect;
export type InsertTitle = z.infer<typeof insertTitleSchema>;
export type CheckinConfig = typeof checkinConfig.$inferSelect;
export type InsertCheckinConfig = z.infer<typeof insertCheckinConfigSchema>;
export type UserConsecutiveCheckin = typeof userConsecutiveCheckins.$inferSelect;
export type InsertUserConsecutiveCheckin = z.infer<typeof insertUserConsecutiveCheckinSchema>;

// 抽奖系统表
export const lotteries = pgTable("lotteries", {
  id: serial("id").primaryKey(),
  title: varchar("title", { length: 200 }).notNull(),
  description: text("description"),
  type: varchar("type", { length: 50 }).notNull(), // 'channel_entry' or 'manual'
  status: varchar("status", { length: 50 }).notNull().default("pending"), // 'pending', 'active', 'ended'
  startTime: timestamp("start_time").notNull(),
  endTime: timestamp("end_time").notNull(),
  maxParticipants: integer("max_participants"),
  cost: integer("cost").notNull().default(0), // 积分费用
  channels: text("channels").array(), // Discord频道ID数组
  entryMessage: text("entry_message"), // 频道进入时的自定义消息
  allowedTitles: integer("allowed_titles").array(), // 允许的头衔ID数组
  isActive: boolean("is_active").notNull().default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});

export const lotteryPrizes = pgTable("lottery_prizes", {
  id: serial("id").primaryKey(),
  lotteryId: integer("lottery_id").notNull().references(() => lotteries.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 200 }).notNull(),
  quantity: integer("quantity").notNull(),
  probability: integer("probability").notNull(), // 中奖概率百分比
  createdAt: timestamp("created_at").defaultNow()
});

export const lotteryParticipants = pgTable("lottery_participants", {
  id: serial("id").primaryKey(),
  lotteryId: integer("lottery_id").notNull().references(() => lotteries.id, { onDelete: "cascade" }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  joinedAt: timestamp("joined_at").defaultNow(),
  channelId: varchar("channel_id", { length: 50 }) // Discord频道ID
});

export const lotteryDrawRecords = pgTable("lottery_draw_records", {
  id: serial("id").primaryKey(),
  lotteryId: integer("lottery_id").notNull().references(() => lotteries.id, { onDelete: "cascade" }),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  prizeId: integer("prize_id").references(() => lotteryPrizes.id, { onDelete: "cascade" }),
  prizeName: varchar("prize_name", { length: 200 }), // 冗余存储奖品名称
  channelId: varchar("channel_id", { length: 50 }), // Discord频道ID
  drawnAt: timestamp("drawn_at").defaultNow()
});

// Contextual Emoji Suggestion Engine Tables
export const emojiSuggestions = pgTable("emoji_suggestions", {
  id: serial("id").primaryKey(),
  keyword: varchar("keyword", { length: 100 }).notNull(),
  emoji: varchar("emoji", { length: 50 }).notNull(),
  context: varchar("context", { length: 200 }), // Context where this emoji is relevant
  relevanceScore: integer("relevance_score").default(100), // 0-100 relevance score
  category: varchar("category", { length: 50 }), // emotion, action, object, etc.
  language: varchar("language", { length: 10 }).default("en"), // Language code
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});

export const emojiUsageHistory = pgTable("emoji_usage_history", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  channelId: varchar("channel_id", { length: 50 }),
  messageContent: text("message_content"), // Original message content
  suggestedEmoji: varchar("suggested_emoji", { length: 50 }),
  selectedEmoji: varchar("selected_emoji", { length: 50 }), // What user actually chose
  contextKeywords: text("context_keywords").array(), // Keywords that triggered suggestion
  wasAccepted: boolean("was_accepted").default(false), // Did user use the suggestion?
  responseTime: integer("response_time"), // Time taken to respond (milliseconds)
  createdAt: timestamp("created_at").defaultNow()
});

export const emojiContextRules = pgTable("emoji_context_rules", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 100 }).notNull(),
  description: text("description"),
  triggerKeywords: text("trigger_keywords").array(), // Keywords that trigger this rule
  suggestedEmojis: text("suggested_emojis").array(), // Emojis to suggest
  contextType: varchar("context_type", { length: 50 }), // greeting, celebration, sadness, etc.
  priority: integer("priority").default(50), // Higher number = higher priority
  channels: text("channels").array(), // Specific Discord channels (optional)
  timeOfDay: varchar("time_of_day", { length: 20 }), // morning, afternoon, evening, night
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});

export const emojiLearningData = pgTable("emoji_learning_data", {
  id: serial("id").primaryKey(),
  messagePattern: text("message_pattern").notNull(), // Regex or text pattern
  commonEmojis: text("common_emojis").array(), // Most used emojis for this pattern
  frequency: integer("frequency").default(1), // How often this pattern appears
  accuracy: integer("accuracy").default(0), // Success rate percentage
  language: varchar("language", { length: 10 }).default("en"),
  lastUsed: timestamp("last_used").defaultNow(),
  createdAt: timestamp("created_at").defaultNow()
});

// 抽奖系统关系
export const lotteriesRelations = relations(lotteries, ({ many }) => ({
  prizes: many(lotteryPrizes),
  participants: many(lotteryParticipants),
  drawRecords: many(lotteryDrawRecords)
}));

export const lotteryPrizesRelations = relations(lotteryPrizes, ({ one, many }) => ({
  lottery: one(lotteries, {
    fields: [lotteryPrizes.lotteryId],
    references: [lotteries.id]
  }),
  drawRecords: many(lotteryDrawRecords)
}));

export const lotteryParticipantsRelations = relations(lotteryParticipants, ({ one }) => ({
  lottery: one(lotteries, {
    fields: [lotteryParticipants.lotteryId],
    references: [lotteries.id]
  }),
  user: one(users, {
    fields: [lotteryParticipants.userId],
    references: [users.id]
  })
}));

export const lotteryDrawRecordsRelations = relations(lotteryDrawRecords, ({ one }) => ({
  lottery: one(lotteries, {
    fields: [lotteryDrawRecords.lotteryId],
    references: [lotteries.id]
  }),
  user: one(users, {
    fields: [lotteryDrawRecords.userId],
    references: [users.id]
  }),
  prize: one(lotteryPrizes, {
    fields: [lotteryDrawRecords.prizeId],
    references: [lotteryPrizes.id]
  })
}));

// 抽奖系统schemas
export const insertLotterySchema = createInsertSchema(lotteries).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertLotteryPrizeSchema = createInsertSchema(lotteryPrizes).omit({
  id: true,
  createdAt: true,
});

export const insertLotteryParticipantSchema = createInsertSchema(lotteryParticipants).omit({
  id: true,
  joinedAt: true,
});

export const insertLotteryDrawRecordSchema = createInsertSchema(lotteryDrawRecords).omit({
  id: true,
  drawnAt: true,
});

// 抽奖系统types
export type Lottery = typeof lotteries.$inferSelect;
export type InsertLottery = z.infer<typeof insertLotterySchema>;
export type LotteryPrize = typeof lotteryPrizes.$inferSelect;
export type InsertLotteryPrize = z.infer<typeof insertLotteryPrizeSchema>;
export type LotteryParticipant = typeof lotteryParticipants.$inferSelect;
export type InsertLotteryParticipant = z.infer<typeof insertLotteryParticipantSchema>;
export type LotteryDrawRecord = typeof lotteryDrawRecords.$inferSelect;
export type InsertLotteryDrawRecord = z.infer<typeof insertLotteryDrawRecordSchema>;

// Slash commands table
export const slashCommands = pgTable("slash_commands", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 100 }).notNull().unique(),
  description: text("description").notNull(),
  category: varchar("category", { length: 50 }).notNull(), // 'points', 'system', etc.
  usage: integer("usage").default(0).notNull(),
  isEnabled: boolean("is_enabled").default(true).notNull(),
  parameters: text("parameters"), // JSON string for command parameters
  response: text("response"), // Default response text
  cooldown: integer("cooldown").default(0).notNull(), // cooldown in seconds
  permissions: varchar("permissions", { length: 50 }).default('everyone').notNull(), // 'everyone', 'admin', 'moderator'
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});

// Slash command usage logs
export const slashCommandUsage = pgTable("slash_command_usage", {
  id: serial("id").primaryKey(),
  commandId: integer("command_id").notNull().references(() => slashCommands.id, { onDelete: "cascade" }),
  userId: integer("user_id").notNull().references(() => users.id),
  discordUserId: varchar("discord_user_id", { length: 50 }),
  usedAt: timestamp("used_at").defaultNow().notNull(),
  executionTime: integer("execution_time"), // execution time in ms
  success: boolean("success").default(true).notNull(),
  errorMessage: text("error_message")
});

// Zod schemas for slash commands
export const insertSlashCommandSchema = createInsertSchema(slashCommands).omit({
  id: true,
  usage: true,
  createdAt: true,
  updatedAt: true
});

export const insertSlashCommandUsageSchema = createInsertSchema(slashCommandUsage).omit({
  id: true,
  usedAt: true
});

// Slash command types
export type SlashCommand = typeof slashCommands.$inferSelect;
export type InsertSlashCommand = z.infer<typeof insertSlashCommandSchema>;
export type SlashCommandUsage = typeof slashCommandUsage.$inferSelect;
export type InsertSlashCommandUsage = z.infer<typeof insertSlashCommandUsageSchema>;

// Emoji suggestion relations
export const emojiSuggestionsRelations = relations(emojiSuggestions, ({ many }) => ({
  usageHistory: many(emojiUsageHistory)
}));

export const emojiUsageHistoryRelations = relations(emojiUsageHistory, ({ one }) => ({
  user: one(users, {
    fields: [emojiUsageHistory.userId],
    references: [users.id]
  })
}));

export const emojiContextRulesRelations = relations(emojiContextRules, ({ many }) => ({
  learningData: many(emojiLearningData)
}));

// Emoji suggestion Zod schemas
export const insertEmojiSuggestionSchema = createInsertSchema(emojiSuggestions).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertEmojiUsageHistorySchema = createInsertSchema(emojiUsageHistory).omit({
  id: true,
  createdAt: true
});

export const insertEmojiContextRuleSchema = createInsertSchema(emojiContextRules).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export const insertEmojiLearningDataSchema = createInsertSchema(emojiLearningData).omit({
  id: true,
  createdAt: true
});

// Redemption code schema
export const redemptionCodes = pgTable("redemption_codes", {
  id: serial("id").primaryKey(),
  code: varchar("code", { length: 50 }).notNull().unique(),
  description: text("description").notNull(),
  points_reward: integer("points_reward").notNull(),
  max_uses: integer("max_uses"),
  current_uses: integer("current_uses").default(0),
  expires_at: timestamp("expires_at"),
  is_active: boolean("is_active").default(true),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow()
});

export const redemptionUsage = pgTable("redemption_usage", {
  id: serial("id").primaryKey(),
  user_id: integer("user_id").references(() => users.id),
  code_id: integer("code_id").references(() => redemptionCodes.id),
  used_at: timestamp("used_at").defaultNow()
}, (table) => ({
  unq: unique().on(table.user_id, table.code_id)
}));

// Redemption code insert schemas
export const insertRedemptionCodeSchema = createInsertSchema(redemptionCodes).omit({
  id: true,
  currentUses: true,
  createdAt: true,
  updatedAt: true
});

export const insertRedemptionUsageSchema = createInsertSchema(redemptionUsage).omit({
  id: true,
  usedAt: true
});

// Redemption code types
export type RedemptionCode = typeof redemptionCodes.$inferSelect;
export type InsertRedemptionCode = z.infer<typeof insertRedemptionCodeSchema>;
export type RedemptionUsage = typeof redemptionUsage.$inferSelect;
export type InsertRedemptionUsage = z.infer<typeof insertRedemptionUsageSchema>;

// Activity system tables
export const activityRecords = pgTable("activity_records", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id),
  activityType: varchar("activity_type", { length: 100 }).notNull(), // 'message', 'voice', 'reaction', 'command', 'join', 'leave'
  points: integer("points").default(0),
  description: text("description").notNull(),
  channelId: varchar("channel_id", { length: 50 }),
  channelName: varchar("channel_name", { length: 100 }),
  metadata: jsonb("metadata"), // Additional activity data
  createdAt: timestamp("created_at").defaultNow(),
});

export const activityConfig = pgTable("activity_config", {
  id: serial("id").primaryKey(),
  activityType: varchar("activity_type", { length: 100 }).notNull().unique(),
  pointsPerActivity: integer("points_per_activity").default(1),
  description: text("description"),
  isActive: boolean("is_active").default(true),
  cooldownMinutes: integer("cooldown_minutes").default(0), // Cooldown between activities
  dailyLimit: integer("daily_limit"), // Max activities per day
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Activity record relations
export const activityRecordsRelations = relations(activityRecords, ({ one }) => ({
  user: one(users, {
    fields: [activityRecords.userId],
    references: [users.id]
  })
}));

// Activity insert schemas
export const insertActivityRecordSchema = createInsertSchema(activityRecords).omit({
  id: true,
  createdAt: true
});

export const insertActivityConfigSchema = createInsertSchema(activityConfig).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

// Activity types
export type ActivityRecord = typeof activityRecords.$inferSelect;
export type InsertActivityRecord = z.infer<typeof insertActivityRecordSchema>;
export type ActivityConfig = typeof activityConfig.$inferSelect;
export type InsertActivityConfig = z.infer<typeof insertActivityConfigSchema>;


