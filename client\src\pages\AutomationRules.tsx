import React, { useState } from 'react';
import * as icons from 'lucide-react';

const AutomationRules: React.FC = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedRule, setSelectedRule] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);

  const [rules, setRules] = useState([
    {
      id: '1',
      name: '新用户欢迎',
      description: '当新用户加入服务器时自动发送欢迎消息',
      trigger: 'user_join',
      actions: ['send_message', 'assign_role'],
      isActive: true,
      lastTriggered: '2024-03-01 14:30',
      triggerCount: 156,
      priority: 'high'
    },
    {
      id: '2',
      name: '积分奖励',
      description: '用户发送消息时自动给予积分奖励',
      trigger: 'message_sent',
      actions: ['add_points'],
      isActive: true,
      lastTriggered: '2024-03-01 14:25',
      triggerCount: 2847,
      priority: 'medium'
    },
    {
      id: '3',
      name: '违规检测',
      description: '检测到违规内容时自动删除消息并警告用户',
      trigger: 'message_content',
      actions: ['delete_message', 'warn_user'],
      isActive: false,
      lastTriggered: '2024-03-01 12:15',
      triggerCount: 23,
      priority: 'high'
    },
    {
      id: '4',
      name: '头衔晋升',
      description: '用户积分达到阈值时自动晋升头衔',
      trigger: 'points_threshold',
      actions: ['upgrade_title', 'send_notification'],
      isActive: true,
      lastTriggered: '2024-03-01 10:45',
      triggerCount: 89,
      priority: 'medium'
    },
    {
      id: '5',
      name: '活跃度检测',
      description: '检测长期不活跃用户并发送提醒',
      trigger: 'inactivity_period',
      actions: ['send_reminder'],
      isActive: true,
      lastTriggered: '2024-02-29 18:00',
      triggerCount: 45,
      priority: 'low'
    }
  ]);

  const handleCreateRule = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setOperationResult({ type: 'success', message: '自动化规则创建成功！' });
      setIsCreateModalOpen(false);
      setTimeout(() => setOperationResult(null), 3000);
    }, 1500);
  };

  const handleEditRule = (rule: any) => {
    setSelectedRule(rule);
    setIsEditModalOpen(true);
  };

  const handleDeleteRule = (rule: any) => {
    setSelectedRule(rule);
    setIsDeleteModalOpen(true);
  };

  const handleToggleRule = (ruleId: string) => {
    setRules(rules.map(rule => 
      rule.id === ruleId 
        ? { ...rule, isActive: !rule.isActive }
        : rule
    ));
    const rule = rules.find(r => r.id === ruleId);
    setOperationResult({ 
      type: 'success', 
      message: `规则 "${rule?.name}" 已${rule?.isActive ? '暂停' : '启用'}！` 
    });
    setTimeout(() => setOperationResult(null), 3000);
  };

  const handleFormSubmit = (type: string) => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setOperationResult({
        type: 'success',
        message: type === 'edit' ? '规则更新成功！' : 
                type === 'delete' ? '规则删除成功！' : '操作完成！'
      });
      
      setTimeout(() => {
        setOperationResult(null);
        if (type === 'edit') setIsEditModalOpen(false);
        if (type === 'delete') {
          setIsDeleteModalOpen(false);
          // 从列表中移除规则
          setRules(rules.filter(rule => rule.id !== selectedRule?.id));
        }
      }, 2000);
    }, 1500);
  };

  const getTriggerText = (trigger: string) => {
    const triggerMap: { [key: string]: string } = {
      'user_join': '用户加入',
      'message_sent': '发送消息',
      'message_content': '消息内容',
      'points_threshold': '积分阈值',
      'inactivity_period': '不活跃期间'
    };
    return triggerMap[trigger] || trigger;
  };

  const getActionText = (action: string) => {
    const actionMap: { [key: string]: string } = {
      'send_message': '发送消息',
      'assign_role': '分配角色',
      'add_points': '增加积分',
      'delete_message': '删除消息',
      'warn_user': '警告用户',
      'upgrade_title': '晋升头衔',
      'send_notification': '发送通知',
      'send_reminder': '发送提醒'
    };
    return actionMap[action] || action;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400';
      case 'medium': return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400';
      case 'low': return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400';
      default: return 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400';
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case 'high': return '高优先级';
      case 'medium': return '中优先级';
      case 'low': return '低优先级';
      default: return '未知';
    }
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">自动化规则</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">配置自动化触发器和响应动作</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-yellow-500/25 font-medium"
          >
            <icons.Plus className="w-4 h-4" />
            <span>创建规则</span>
          </button>
        </div>
      </div>

      {/* 操作结果提示 */}
      {operationResult && (
        <div className={`p-4 rounded-xl border ${
          operationResult.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {operationResult.type === 'success' ? (
              <icons.CheckCircle className="w-5 h-5" />
            ) : (
              <icons.AlertCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{operationResult.message}</span>
          </div>
        </div>
      )}

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧统计区域 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 规则统计卡片 */}
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Workflow className="w-6 h-6 text-black font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">规则总数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{rules.length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" style={{ width: '100%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">系统完整度 100%</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                  <icons.Play className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">活跃规则</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{rules.filter(r => r.isActive).length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" style={{ width: `${(rules.filter(r => r.isActive).length / rules.length) * 100}%` }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">运行正常</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                  <icons.Zap className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日触发</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">3,160</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-purple-400 to-purple-500 h-2 rounded-full" style={{ width: '85%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">比昨日增长 15%</p>
            </div>
          </div>

          {/* 规则类型分布 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">触发器类型</h3>
            <div className="space-y-3">
              {[
                { type: 'user_join', name: '用户加入', count: 1, color: 'bg-blue-500' },
                { type: 'message_sent', name: '消息发送', count: 1, color: 'bg-green-500' },
                { type: 'message_content', name: '内容检测', count: 1, color: 'bg-red-500' },
                { type: 'points_threshold', name: '积分阈值', count: 1, color: 'bg-yellow-400' },
                { type: 'inactivity_period', name: '不活跃检测', count: 1, color: 'bg-purple-500' }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 ${item.color} rounded-full`}></div>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">{item.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-900 dark:text-white">{item.count}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {((item.count / rules.length) * 100).toFixed(0)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 中间主要内容区域 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          {/* 规则列表 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">自动化规则列表</h3>
              <div className="space-y-6">
                {rules.map((rule) => (
                  <div key={rule.id} className="border border-gray-200 dark:border-gray-600 rounded-xl p-6 hover:border-yellow-400 dark:hover:border-yellow-500 transition-colors">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="text-lg font-bold text-gray-900 dark:text-white">{rule.name}</h4>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            rule.isActive 
                              ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400' 
                              : 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400'
                          }`}>
                            {rule.isActive ? '运行中' : '已暂停'}
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(rule.priority)}`}>
                            {getPriorityText(rule.priority)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{rule.description}</p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">触发器</span>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {getTriggerText(rule.trigger)}
                            </p>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">触发次数</span>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">{rule.triggerCount}</p>
                          </div>
                          <div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">最后触发</span>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">{rule.lastTriggered}</p>
                          </div>
                        </div>

                        <div>
                          <span className="text-xs text-gray-500 dark:text-gray-400 mb-2 block">执行动作</span>
                          <div className="flex flex-wrap gap-2">
                            {rule.actions.map((action, index) => (
                              <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400">
                                {getActionText(action)}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <button 
                          onClick={() => handleToggleRule(rule.id)}
                          className={`p-2 rounded-lg transition-colors ${
                            rule.isActive 
                              ? 'text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20'
                              : 'text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20'
                          }`}
                          title={rule.isActive ? '暂停规则' : '启用规则'}
                        >
                          {rule.isActive ? <icons.Pause className="w-4 h-4" /> : <icons.Play className="w-4 h-4" />}
                        </button>
                        <button 
                          onClick={() => handleEditRule(rule)}
                          className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 transition-colors p-2 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20" 
                          title="编辑规则"
                        >
                          <icons.Edit className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => {
                            setOperationResult({ type: 'success', message: `规则 "${rule.name}" 复制成功！` });
                            setTimeout(() => setOperationResult(null), 3000);
                          }}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20" 
                          title="复制规则"
                        >
                          <icons.Copy className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => handleDeleteRule(rule)}
                          className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20" 
                          title="删除规则"
                        >
                          <icons.Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧信息面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 最近触发记录 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Zap className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">最近触发</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">实时更新</p>
              </div>
            </div>
            
            <div className="space-y-4">
              {[
                { rule: '新用户欢迎', user: '张三', time: '2分钟前', action: '发送欢迎消息' },
                { rule: '积分奖励', user: '李四', time: '5分钟前', action: '增加积分 +1' },
                { rule: '头衔晋升', user: '王五', time: '10分钟前', action: '晋升为活跃成员' },
                { rule: '积分奖励', user: '赵六', time: '15分钟前', action: '增加积分 +1' },
              ].map((record, index) => (
                <div key={index} className="p-3 border border-gray-200 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-500 rounded-lg flex items-center justify-center shadow-lg">
                      <icons.Zap className="w-4 h-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{record.rule}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{record.time}</div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 ml-11 mb-1">
                    用户: {record.user}
                  </div>
                  <div className="text-xs text-purple-600 dark:text-purple-400 ml-11 font-medium">
                    {record.action}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 规则性能统计 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">性能统计</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">平均响应时间</span>
                <span className="text-sm font-bold text-green-600 dark:text-green-400">45ms</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">成功执行率</span>
                <span className="text-sm font-bold text-green-600 dark:text-green-400">99.8%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">错误次数</span>
                <span className="text-sm font-bold text-red-600 dark:text-red-400">3</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">CPU 使用率</span>
                <span className="text-sm font-bold text-yellow-600 dark:text-yellow-400">12%</span>
              </div>
            </div>
          </div>

          {/* 快速操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快速操作</h3>
            <div className="space-y-3">
              <button 
                onClick={() => {
                  setOperationResult({ type: 'success', message: '所有规则已暂停！' });
                  setRules(rules.map(rule => ({ ...rule, isActive: false })));
                  setTimeout(() => setOperationResult(null), 3000);
                }}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.PauseCircle className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">暂停所有规则</span>
              </button>
              <button 
                onClick={() => {
                  setOperationResult({ type: 'success', message: '所有规则已启用！' });
                  setRules(rules.map(rule => ({ ...rule, isActive: true })));
                  setTimeout(() => setOperationResult(null), 3000);
                }}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.PlayCircle className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">启用所有规则</span>
              </button>
              <button 
                onClick={() => {
                  setOperationResult({ type: 'success', message: '规则导出功能即将开放！' });
                  setTimeout(() => setOperationResult(null), 3000);
                }}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Download className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">导出规则</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 创建规则模态框 */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">创建自动化规则</h3>
              <button 
                onClick={() => setIsCreateModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={(e) => { e.preventDefault(); handleCreateRule(); }} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">规则名称</label>
                  <input
                    type="text"
                    placeholder="请输入规则名称"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">优先级</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="high">高优先级</option>
                    <option value="medium">中优先级</option>
                    <option value="low">低优先级</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">规则描述</label>
                <textarea
                  placeholder="请描述这个规则的作用..."
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">触发条件</label>
                <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option value="user_join">用户加入服务器</option>
                  <option value="message_sent">用户发送消息</option>
                  <option value="message_content">消息内容匹配</option>
                  <option value="points_threshold">积分达到阈值</option>
                  <option value="inactivity_period">用户不活跃时间</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">条件参数</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="参数名称"
                    className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                  <input
                    type="text"
                    placeholder="参数值"
                    className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <button type="button" className="text-yellow-600 dark:text-yellow-400 text-sm hover:text-yellow-700 dark:hover:text-yellow-300 mt-2 flex items-center space-x-1">
                  <icons.Plus className="w-4 h-4" />
                  <span>添加参数</span>
                </button>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">执行动作</label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <select className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                      <option value="send_message">发送消息</option>
                      <option value="assign_role">分配角色</option>
                      <option value="add_points">增加积分</option>
                      <option value="delete_message">删除消息</option>
                      <option value="warn_user">警告用户</option>
                      <option value="upgrade_title">晋升头衔</option>
                    </select>
                    <button type="button" className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300">
                      <icons.Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <button type="button" className="text-yellow-600 dark:text-yellow-400 text-sm hover:text-yellow-700 dark:hover:text-yellow-300 mt-2 flex items-center space-x-1">
                  <icons.Plus className="w-4 h-4" />
                  <span>添加动作</span>
                </button>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                >
                  {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                  <span>{isLoading ? '创建中...' : '创建规则'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 编辑规则模态框 */}
      {isEditModalOpen && selectedRule && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">编辑规则 - {selectedRule.name}</h3>
              <button 
                onClick={() => setIsEditModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('更新') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">更新成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">规则已成功更新</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('edit'); }} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">规则名称</label>
                    <input
                      type="text"
                      defaultValue={selectedRule.name}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">优先级</label>
                    <select 
                      defaultValue={selectedRule.priority}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="high">高优先级</option>
                      <option value="medium">中优先级</option>
                      <option value="low">低优先级</option>
                    </select>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">规则描述</label>
                  <textarea
                    defaultValue={selectedRule.description}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">触发条件</label>
                  <select 
                    defaultValue={selectedRule.trigger}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="user_join">用户加入服务器</option>
                    <option value="message_sent">用户发送消息</option>
                    <option value="message_content">消息内容匹配</option>
                    <option value="points_threshold">积分达到阈值</option>
                    <option value="inactivity_period">用户不活跃时间</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">执行动作</label>
                  <div className="space-y-3">
                    {selectedRule.actions.map((action, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <select 
                          defaultValue={action}
                          className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        >
                          <option value="send_message">发送消息</option>
                          <option value="assign_role">分配角色</option>
                          <option value="add_points">增加积分</option>
                          <option value="delete_message">删除消息</option>
                          <option value="warn_user">警告用户</option>
                          <option value="upgrade_title">晋升头衔</option>
                          <option value="send_notification">发送通知</option>
                          <option value="send_reminder">发送提醒</option>
                        </select>
                        <button type="button" className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300">
                          <icons.Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                  <button type="button" className="text-yellow-600 dark:text-yellow-400 text-sm hover:text-yellow-700 dark:hover:text-yellow-300 mt-2 flex items-center space-x-1">
                    <icons.Plus className="w-4 h-4" />
                    <span>添加动作</span>
                  </button>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <button
                    type="button"
                    onClick={() => setIsEditModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '保存中...' : '保存更改'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 删除规则确认模态框 */}
      {isDeleteModalOpen && selectedRule && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">删除规则确认</h3>
              <button 
                onClick={() => setIsDeleteModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('删除') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">删除成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">规则已成功删除</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <icons.AlertTriangle className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">确认删除规则</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    您确定要删除规则 <span className="font-bold">"{selectedRule.name}"</span> 吗？
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                    此规则已触发 {selectedRule.triggerCount} 次，删除后将不再执行。此操作不可撤销。
                  </p>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    onClick={() => setIsDeleteModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={() => handleFormSubmit('delete')}
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg shadow-red-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '删除中...' : '确认删除'}</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default AutomationRules;