import { Client, GatewayIntentBits, Collection } from 'discord.js';
import { logger } from '../utils/logger.js';
import { db } from '../database/init.js';
import { getSetting } from '../utils/settings.js';
import { broadcastEvent } from '../websocket/index.js';

// Import event handlers
import { handleReady } from './events/ready.js';
import { handleGuildMemberAdd } from './events/guildMemberAdd.js';
import { handleMessageCreate } from './events/messageCreate.js';
import { handleInteractionCreate } from './events/interactionCreate.js';

// Import commands
import { registerCommands } from './commands/index.js';

let client = null;
let botStatus = {
  connected: false,
  ready: false,
  latency: 0,
  uptime: 0,
  guilds: 0,
  users: 0,
  error: null
};

export async function initBot() {
  try {
    const token = await getSetting('bot_token');
    
    if (!token || token.trim() === '') {
      logger.warn('No bot token found, bot will not start');
      botStatus.error = 'No bot token configured';
      return;
    }

    await startBot(token);
  } catch (error) {
    logger.error('Failed to initialize bot:', error);
    botStatus.error = error.message;
    broadcastEvent('bot_error', { error: error.message });
  }
}

export async function startBot(token) {
  try {
    if (!token || token.trim() === '') {
      throw new Error('Bot token is required');
    }

    if (client) {
      await stopBot();
    }

    client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildPresences
      ]
    });

    // Initialize commands collection
    client.commands = new Collection();

    // Register event handlers
    client.once('ready', () => {
      handleReady(client, botStatus);
      broadcastEvent('bot_ready', { 
        botTag: client.user.tag,
        guilds: client.guilds.cache.size,
        users: client.users.cache.size
      });
      
      // Start syncing users from Discord
      syncDiscordUsers();
    });
    
    client.on('guildMemberAdd', (member) => {
      handleGuildMemberAdd(member);
      broadcastEvent('member_joined', {
        username: member.user.username,
        id: member.id
      });
    });
    
    client.on('messageCreate', (message) => {
      handleMessageCreate(message);
      if (!message.author.bot) {
        broadcastEvent('message_sent', {
          username: message.author.username,
          content: message.content.substring(0, 100),
          channel: message.channel.name
        });
      }
    });
    
    client.on('interactionCreate', (interaction) => handleInteractionCreate(interaction));

    // Error handling
    client.on('error', (error) => {
      logger.error('Discord client error:', error);
      botStatus.error = error.message;
      botStatus.connected = false;
      botStatus.ready = false;
      broadcastEvent('bot_error', { error: error.message });
    });

    client.on('disconnect', () => {
      logger.warn('Discord client disconnected');
      botStatus.connected = false;
      botStatus.ready = false;
      broadcastEvent('bot_disconnected', {});
    });

    // Register slash commands
    await registerCommands(client);

    // Login to Discord
    await client.login(token);
    
    botStatus.connected = true;
    botStatus.error = null;
    
    logger.info('Bot login successful');
    
    return client;
  } catch (error) {
    logger.error('Failed to start bot:', error);
    botStatus.error = error.message;
    botStatus.connected = false;
    botStatus.ready = false;
    broadcastEvent('bot_error', { error: error.message });
    throw error;
  }
}

export async function stopBot() {
  try {
    if (client) {
      await client.destroy();
      client = null;
      botStatus.connected = false;
      botStatus.ready = false;
      botStatus.error = null;
      logger.info('Bot stopped successfully');
      broadcastEvent('bot_stopped', {});
    }
  } catch (error) {
    logger.error('Failed to stop bot:', error);
    throw error;
  }
}

export async function restartBot() {
  try {
    const token = await getSetting('bot_token');
    if (!token || token.trim() === '') {
      throw new Error('No bot token configured');
    }

    await stopBot();
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    await startBot(token);
    
    logger.info('Bot restarted successfully');
    broadcastEvent('bot_restarted', {});
  } catch (error) {
    logger.error('Failed to restart bot:', error);
    throw error;
  }
}

export function getBotStatus() {
  if (client && client.isReady()) {
    botStatus.latency = client.ws.ping;
    botStatus.uptime = client.uptime;
    botStatus.guilds = client.guilds.cache.size;
    botStatus.users = client.users.cache.size;
  }
  
  return botStatus;
}

export function getClient() {
  return client;
}

// Sync Discord users to database
async function syncDiscordUsers() {
  if (!client || !client.isReady()) return;
  
  try {
    const guild = client.guilds.cache.first();
    if (!guild) return;

    await guild.members.fetch();
    
    for (const [memberId, member] of guild.members.cache) {
      if (member.user.bot) continue;

      await db.runAsync(`
        INSERT OR REPLACE INTO users (
          id, username, discriminator, avatar, join_date, last_active, status, roles
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        member.id,
        member.user.username,
        member.user.discriminator,
        member.user.avatar,
        member.joinedAt?.toISOString() || new Date().toISOString(),
        new Date().toISOString(),
        member.presence?.status || 'offline',
        JSON.stringify(member.roles.cache.map(role => role.name))
      ]);
    }

    logger.info(`Synced ${guild.members.cache.size} users from Discord`);
    broadcastEvent('users_synced', { count: guild.members.cache.size });
  } catch (error) {
    logger.error('Error syncing Discord users:', error);
  }
}

// Auto-sync users every 5 minutes
setInterval(syncDiscordUsers, 5 * 60 * 1000);