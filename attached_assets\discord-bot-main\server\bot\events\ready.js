import { logger } from '../../utils/logger.js';

export async function handleReady(client, botStatus) {
  logger.info(`<PERSON><PERSON> is ready! Logged in as ${client.user.tag}`);
  
  // Update bot status
  botStatus.ready = true;
  botStatus.connected = true;
  botStatus.latency = client.ws.ping;
  botStatus.guilds = client.guilds.cache.size;
  botStatus.users = client.users.cache.size;
  
  // Set bot activity
  client.user.setActivity('管理服务器', { type: 'WATCHING' });
  
  // Log bot information
  logger.info(`<PERSON><PERSON> is in ${client.guilds.cache.size} guilds`);
  logger.info(`<PERSON><PERSON> can see ${client.users.cache.size} users`);
}