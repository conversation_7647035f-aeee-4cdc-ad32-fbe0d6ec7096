import { SlashCommandBuilder } from 'discord.js';
import { db } from '../../database/init.js';
import { logger } from '../../utils/logger.js';

export const leaderboardCommand = {
  data: new SlashCommandBuilder()
    .setName('排行榜')
    .setDescription('查看积分排行榜')
    .addIntegerOption(option =>
      option.setName('数量')
        .setDescription('显示的用户数量（默认10）')
        .setRequired(false)
        .setMinValue(5)
        .setMaxValue(20)
    ),

  async execute(interaction) {
    try {
      const limit = interaction.options.getInteger('数量') || 10;
      
      const topUsers = await db.allAsync(`
        SELECT username, points, title 
        FROM users 
        ORDER BY points DESC 
        LIMIT ?
      `, [limit]);

      if (topUsers.length === 0) {
        await interaction.reply({
          content: '📊 暂无排行榜数据。',
          ephemeral: true
        });
        return;
      }

      const leaderboardText = topUsers.map((user, index) => {
        const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
        return `${medal} **${user.username}** - ${user.points} 积分 (${user.title})`;
      }).join('\n');

      await interaction.reply({
        embeds: [{
          title: '🏆 积分排行榜',
          description: leaderboardText,
          color: 0xD6F36F,
          footer: {
            text: `显示前 ${topUsers.length} 名用户`
          },
          timestamp: new Date().toISOString()
        }]
      });
    } catch (error) {
      logger.error('Error in leaderboard command:', error);
      await interaction.reply({
        content: '❌ 查询排行榜失败，请稍后重试。',
        ephemeral: true
      });
    }
  }
};