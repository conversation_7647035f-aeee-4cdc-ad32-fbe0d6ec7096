import { WebSocketServer } from 'ws';
import { logger } from '../utils/logger.js';
import { getBotStatus, getClient } from '../bot/index.js';
import { getDb } from '../database/init.js';

let wss;
let clients = new Set();

export function setupWebSocket(httpServer) {
  try {
    // 创建 WebSocket 服务器
    wss = new WebSocketServer({ 
      server: httpServer,
      path: '/',
      perMessageDeflate: false,
      clientTracking: true
    });

    wss.on('connection', (ws, req) => {
      logger.info(`New WebSocket client connected from ${req.socket.remoteAddress}`);
      clients.add(ws);

      // Send initial data immediately
      sendInitialData(ws);

      // Handle client messages
      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message.toString());
          handleClientMessage(ws, data);
        } catch (error) {
          logger.error('Error parsing WebSocket message:', error);
          // 发送错误响应
          if (ws.readyState === ws.OPEN) {
            ws.send(JSON.stringify({
              type: 'error',
              message: 'Invalid message format'
            }));
          }
        }
      });

      // Handle client disconnect
      ws.on('close', (code, reason) => {
        logger.info(`WebSocket client disconnected: ${code} ${reason}`);
        clients.delete(ws);
      });

      ws.on('error', (error) => {
        logger.error('WebSocket error:', error);
        clients.delete(ws);
      });

      // Send ping every 30 seconds to keep connection alive
      const pingInterval = setInterval(() => {
        if (ws.readyState === ws.OPEN) {
          try {
            ws.ping();
          } catch (error) {
            logger.error('Error sending ping:', error);
            clearInterval(pingInterval);
            clients.delete(ws);
          }
        } else {
          clearInterval(pingInterval);
          clients.delete(ws);
        }
      }, 30000);

      ws.on('close', () => {
        clearInterval(pingInterval);
      });
    });

    wss.on('error', (error) => {
      logger.error('WebSocket Server error:', error);
    });

    // Start real-time data broadcasting
    startDataBroadcasting();
    
    logger.info('WebSocket server initialized successfully');
  } catch (error) {
    logger.error('Failed to setup WebSocket:', error);
    throw error;
  }
}

async function sendInitialData(ws) {
  try {
    const botStatus = getBotStatus();
    const client = getClient();
    
    let serverData = {
      memberCount: 0,
      onlineCount: 0,
      channels: [],
      roles: [],
      activeChannels: 0
    };

    if (client && client.isReady()) {
      const guild = client.guilds.cache.first();
      if (guild) {
        try {
          await guild.members.fetch();
          serverData = {
            memberCount: guild.memberCount,
            onlineCount: guild.members.cache.filter(member => 
              member.presence?.status === 'online' || 
              member.presence?.status === 'idle' || 
              member.presence?.status === 'dnd'
            ).size,
            channels: guild.channels.cache
              .filter(channel => channel.type === 0) // Text channels only
              .map(channel => ({
                id: channel.id,
                name: channel.name,
                type: channel.type,
                category: channel.parent?.name || '未分类'
              })),
            roles: guild.roles.cache
              .filter(role => !role.managed && role.name !== '@everyone')
              .map(role => ({
                id: role.id,
                name: role.name,
                color: role.hexColor,
                memberCount: role.members.size
              })),
            activeChannels: guild.channels.cache.filter(channel => channel.type === 0).size
          };
        } catch (error) {
          logger.error('Error fetching guild data:', error);
        }
      }
    }

    // Get database stats (with fallback)
    let stats = {
      userCount: 0,
      todayCheckins: 0,
      totalPoints: 0,
      recentActiveUsers: 0,
      timestamp: new Date().toISOString()
    };

    try {
      const db = getDb();
      const userCount = await db.get('SELECT COUNT(*) as count FROM users');
      const todayCheckins = await db.get(`
        SELECT COUNT(*) as count FROM checkin_records 
        WHERE checkin_date = date('now')
      `);
      const totalPoints = await db.get('SELECT SUM(points) as total FROM users');
      const recentActiveUsers = await db.get(`
        SELECT COUNT(*) as count FROM users 
        WHERE last_active > datetime('now', '-1 hour')
      `);

      stats = {
        userCount: userCount?.count || 0,
        todayCheckins: todayCheckins?.count || 0,
        totalPoints: totalPoints?.total || 0,
        recentActiveUsers: recentActiveUsers?.count || 0,
        timestamp: new Date().toISOString()
      };
    } catch (dbError) {
      logger.error('Error getting database stats:', dbError);
    }

    const initialData = {
      type: 'initial_data',
      data: {
        botStatus,
        serverData,
        stats
      }
    };

    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify(initialData));
    }
  } catch (error) {
    logger.error('Error sending initial data:', error);
    // 发送错误状态
    if (ws.readyState === ws.OPEN) {
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Failed to load initial data'
      }));
    }
  }
}

function handleClientMessage(ws, data) {
  try {
    switch (data.type) {
      case 'ping':
        if (ws.readyState === ws.OPEN) {
          ws.send(JSON.stringify({ 
            type: 'pong', 
            timestamp: new Date().toISOString() 
          }));
        }
        break;
      case 'request_update':
        sendInitialData(ws);
        break;
      case 'subscribe':
        // Handle subscription to specific data types
        logger.info(`Client subscribed to: ${data.channels?.join(', ') || 'all'}`);
        break;
      default:
        logger.warn('Unknown WebSocket message type:', data.type);
    }
  } catch (error) {
    logger.error('Error handling client message:', error);
  }
}

function startDataBroadcasting() {
  // Broadcast comprehensive updates every 30 seconds
  setInterval(async () => {
    if (clients.size === 0) return;

    try {
      const botStatus = getBotStatus();
      const client = getClient();
      
      let serverData = {
        memberCount: 0,
        onlineCount: 0,
        activeChannels: 0,
        channels: [],
        roles: []
      };

      if (client && client.isReady()) {
        const guild = client.guilds.cache.first();
        if (guild) {
          try {
            serverData = {
              memberCount: guild.memberCount,
              onlineCount: guild.members.cache.filter(member => 
                member.presence?.status === 'online' || 
                member.presence?.status === 'idle' || 
                member.presence?.status === 'dnd'
              ).size,
              activeChannels: guild.channels.cache.filter(channel => 
                channel.type === 0 // Text channels
              ).size,
              channels: guild.channels.cache
                .filter(channel => channel.type === 0) // Text channels only
                .map(channel => ({
                  id: channel.id,
                  name: channel.name,
                  type: channel.type,
                  category: channel.parent?.name || '未分类'
                })),
              roles: guild.roles.cache
                .filter(role => !role.managed && role.name !== '@everyone')
                .map(role => ({
                  id: role.id,
                  name: role.name,
                  color: role.hexColor,
                  memberCount: role.members.size
                }))
            };
          } catch (error) {
            logger.error('Error getting guild data for broadcast:', error);
          }
        }
      }

      // Get real-time database stats (with fallback)
      let stats = {
        userCount: 0,
        todayCheckins: 0,
        recentActiveUsers: 0,
        totalPoints: 0,
        timestamp: new Date().toISOString()
      };

      try {
        const db = getDb();
        const userCount = await db.get('SELECT COUNT(*) as count FROM users');
        const todayCheckins = await db.get(`
          SELECT COUNT(*) as count FROM checkin_records 
          WHERE checkin_date = date('now')
        `);
        const recentMessages = await db.get(`
          SELECT COUNT(*) as count FROM users 
          WHERE last_active > datetime('now', '-1 hour')
        `);
        const totalPoints = await db.get('SELECT SUM(points) as total FROM users');

        stats = {
          userCount: userCount?.count || 0,
          todayCheckins: todayCheckins?.count || 0,
          recentActiveUsers: recentMessages?.count || 0,
          totalPoints: totalPoints?.total || 0,
          timestamp: new Date().toISOString()
        };
      } catch (dbError) {
        logger.error('Error getting database stats for broadcast:', dbError);
      }

      const updateData = {
        type: 'real_time_update',
        data: {
          botStatus,
          serverData,
          stats
        }
      };

      broadcastToAllClients(updateData);
    } catch (error) {
      logger.error('Error broadcasting data:', error);
    }
  }, 30000); // 30 seconds

  // Broadcast bot status updates more frequently
  setInterval(async () => {
    if (clients.size === 0) return;

    try {
      const botStatus = getBotStatus();
      const eventData = {
        type: 'bot_status_update',
        data: {
          botStatus,
          timestamp: new Date().toISOString()
        }
      };

      broadcastToAllClients(eventData);
    } catch (error) {
      logger.error('Error broadcasting bot status:', error);
    }
  }, 10000); // 10 seconds for bot status
}

function broadcastToAllClients(data) {
  if (clients.size === 0) return;

  const message = JSON.stringify(data);
  const deadClients = new Set();

  clients.forEach(client => {
    if (client.readyState === client.OPEN) {
      try {
        client.send(message);
      } catch (error) {
        logger.error('Error sending message to client:', error);
        deadClients.add(client);
      }
    } else {
      deadClients.add(client);
    }
  });

  // Clean up dead connections
  deadClients.forEach(client => {
    clients.delete(client);
  });
}

// Export function to broadcast custom events
export function broadcastEvent(eventType, data) {
  try {
    const message = {
      type: eventType,
      data,
      timestamp: new Date().toISOString()
    };
    broadcastToAllClients(message);
  } catch (error) {
    logger.error('Error broadcasting event:', error);
  }
}

// Export function to get connection count
export function getConnectionCount() {
  return clients.size;
}