import React, { useState } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';

interface DataAnalyticsProps {
  onTabChange?: (tabId: string) => void;
}

const DataAnalytics: React.FC<DataAnalyticsProps> = ({ onTabChange }) => {
  const [timeRange, setTimeRange] = useState('7d');
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const [exportProgress, setExportProgress] = useState(0);
  const { showNotification } = useNotification();

  const handleExport = (type: string) => {
    setIsExportModalOpen(true);
    setExportProgress(0);
    setIsLoading(true);
    
    const interval = setInterval(() => {
      setExportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          setOperationResult({ 
            type: 'success', 
            message: `${type === 'csv' ? 'CSV' : type === 'excel' ? 'Excel' : 'PDF'}文件导出成功！` 
          });
          return 100;
        }
        return prev + 20;
      });
    }, 300);
  };

  const handleGenerateReport = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('分析报告生成成功！');
      setIsReportModalOpen(false);
    }, 2000);
  };

  const handleAdvancedFilter = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      showNotification('高级筛选应用成功！');
      setIsFilterModalOpen(false);
    }, 1500);
  };

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">数据分析</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">深入了解服务器活跃度和用户行为</p>
        </div>
        <div className="flex space-x-2">
          {['24h', '7d', '30d', '90d'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-4 py-2 text-sm rounded-xl transition-colors font-medium ${
                timeRange === range
                  ? 'bg-yellow-400 text-black shadow-lg shadow-yellow-500/25'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              {range === '24h' ? '24小时' : range === '7d' ? '7天' : range === '30d' ? '30天' : '90天'}
            </button>
          ))}
        </div>
      </div>

      {/* 操作结果提示 */}
      {operationResult && (
        <div className={`p-4 rounded-xl border ${
          operationResult.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {operationResult.type === 'success' ? (
              <icons.CheckCircle className="w-5 h-5" />
            ) : (
              <icons.AlertCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{operationResult.message}</span>
          </div>
        </div>
      )}

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧关键指标 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 关键指标卡片 */}
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.MessageCircle className="w-6 h-6 text-black font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日消息</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">2,847</p>
                </div>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-green-600 dark:text-green-400 flex items-center">
                  <icons.TrendingUp className="w-3 h-3 mr-1" />
                  +12.5%
                </span>
                <span className="text-gray-500 dark:text-gray-400">vs 昨日 2,532</span>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                  <icons.Users className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">活跃用户</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">456</p>
                </div>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-green-600 dark:text-green-400 flex items-center">
                  <icons.TrendingUp className="w-3 h-3 mr-1" />
                  +8.3%
                </span>
                <span className="text-gray-500 dark:text-gray-400">活跃率 73.2%</span>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl flex items-center justify-center shadow-lg shadow-gray-900/25">
                  <icons.Clock className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">平均在线时长</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">3.2h</p>
                </div>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-red-600 dark:text-red-400 flex items-center">
                  <icons.TrendingDown className="w-3 h-3 mr-1" />
                  -2.1%
                </span>
                <span className="text-gray-500 dark:text-gray-400">峰值 5.8h</span>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                  <icons.UserPlus className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">新用户</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">23</p>
                </div>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-green-600 dark:text-green-400 flex items-center">
                  <icons.TrendingUp className="w-3 h-3 mr-1" />
                  +15.2%
                </span>
                <span className="text-gray-500 dark:text-gray-400">留存率 87.3%</span>
              </div>
            </div>
          </div>

          {/* 实时数据 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Activity className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">实时数据</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">当前状态</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">在线用户</span>
                <span className="text-sm font-bold text-green-600 dark:text-green-400">127</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">语音频道</span>
                <span className="text-sm font-bold text-blue-600 dark:text-blue-400">34</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">正在输入</span>
                <span className="text-sm font-bold text-purple-600 dark:text-purple-400">8</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">服务器负载</span>
                <span className="text-sm font-bold text-yellow-600 dark:text-yellow-400">23%</span>
              </div>
            </div>
          </div>
        </div>

        {/* 中间主要图表区域 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          {/* 每日消息量趋势 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">消息量趋势</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">过去7天数据对比</p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-lg shadow-yellow-400/50"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">消息数</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">用户数</span>
                </div>
              </div>
            </div>
            <div className="h-64 flex items-end justify-between space-x-2">
              {[
                { day: '周一', messages: 2100, users: 180, messageHeight: 65, userHeight: 45 },
                { day: '周二', messages: 2450, users: 210, messageHeight: 78, userHeight: 52 },
                { day: '周三', messages: 1890, users: 165, messageHeight: 56, userHeight: 41 },
                { day: '周四', messages: 3200, users: 245, messageHeight: 100, userHeight: 61 },
                { day: '周五', messages: 2750, users: 220, messageHeight: 89, userHeight: 55 },
                { day: '周六', messages: 3100, users: 235, messageHeight: 93, userHeight: 58 },
                { day: '周日', messages: 2847, users: 205, messageHeight: 85, userHeight: 51 }
              ].map((item, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div className="w-full flex space-x-1 mb-2" style={{ height: '200px' }}>
                    <div className="flex-1 bg-gray-100 dark:bg-gray-700 rounded-t-lg relative overflow-hidden">
                      <div 
                        className="absolute bottom-0 w-full bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t-lg transition-all duration-1000 ease-out shadow-lg"
                        style={{ height: `${item.messageHeight}%` }}
                      />
                      <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-xs font-bold text-gray-700 dark:text-gray-300">
                        {item.messages}
                      </div>
                    </div>
                    <div className="flex-1 bg-gray-100 dark:bg-gray-700 rounded-t-lg relative overflow-hidden">
                      <div 
                        className="absolute bottom-0 w-full bg-gradient-to-t from-purple-500 to-purple-400 rounded-t-lg transition-all duration-1000 ease-out"
                        style={{ height: `${item.userHeight}%` }}
                      />
                      <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-xs font-bold text-gray-700 dark:text-gray-300">
                        {item.users}
                      </div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 text-center font-medium">
                    {item.day}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 用户行为分析 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">用户行为分析</h3>
            <div className="space-y-4">
              {[
                { action: '发送消息', count: 2847, icon: 'MessageCircle', color: 'bg-yellow-400', percentage: 45 },
                { action: '语音聊天', count: 156, icon: 'Mic', color: 'bg-green-500', percentage: 25 },
                { action: '分享图片', count: 89, icon: 'Image', color: 'bg-purple-500', percentage: 15 },
                { action: '发送表情', count: 234, icon: 'Smile', color: 'bg-blue-500', percentage: 35 },
                { action: '文件上传', count: 45, icon: 'Upload', color: 'bg-red-500', percentage: 8 },
                { action: '参与投票', count: 67, icon: 'Vote', color: 'bg-indigo-500', percentage: 12 }
              ].map((item, index) => {
                const Icon = icons[item.icon as keyof typeof icons] as React.ComponentType<any>;
                return (
                  <div key={index} className="flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 ${item.color} rounded-xl flex items-center justify-center shadow-lg`}>
                        <Icon className={`w-5 h-5 ${item.color === 'bg-yellow-400' ? 'text-black' : 'text-white'} font-bold`} />
                      </div>
                      <div>
                        <span className="text-sm font-bold text-gray-900 dark:text-white">{item.action}</span>
                        <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                          <div 
                            className={`${item.color} h-2 rounded-full transition-all duration-500`}
                            style={{ width: `${item.percentage}%` }}
                          />
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900 dark:text-white">{item.count}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{item.percentage}%</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* 右侧信息面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 频道活跃度排行 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Hash className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">频道排行</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">消息活跃度</p>
              </div>
            </div>
            
            <div className="space-y-4">
              {[
                { name: '💬 闲聊水群', messages: 1250, percentage: 85, growth: 12 },
                { name: '🎮 游戏交流', messages: 890, percentage: 62, growth: 8 },
                { name: '📷 图片分享', messages: 560, percentage: 40, growth: -3 },
                { name: '🎵 音乐分享', messages: 340, percentage: 25, growth: 15 },
                { name: '📚 学习讨论', messages: 230, percentage: 18, growth: 5 }
              ].map((channel, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">{channel.name}</span>
                      <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                        channel.growth > 0 
                          ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400' 
                          : 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400'
                      }`}>
                        {channel.growth > 0 ? '+' : ''}{channel.growth}%
                      </span>
                    </div>
                    <span className="text-sm text-gray-500 dark:text-gray-400">{channel.messages}</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${channel.percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 用户活跃时段分析 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">24小时热力图</h3>
            <div className="grid grid-cols-6 gap-2">
              {Array.from({ length: 24 }, (_, hour) => {
                const activity = Math.floor(Math.random() * 100);
                const isHot = activity > 70;
                const isMedium = activity > 40;
                return (
                  <div key={hour} className="text-center">
                    <div 
                      className={`w-full rounded-lg mb-2 transition-all duration-300 hover:scale-105 cursor-pointer ${
                        isHot ? 'bg-red-500' : isMedium ? 'bg-yellow-400' : 'bg-blue-500'
                      }`}
                      style={{ height: `${Math.max(activity, 20)}px` }}
                      title={`${hour}:00 - 活跃度: ${activity}%`}
                    />
                    <div className="text-xs text-gray-500 dark:text-gray-400">{hour}h</div>
                  </div>
                );
              })}
            </div>
            <div className="mt-6 flex items-center justify-between text-xs">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  <span className="text-gray-500 dark:text-gray-400">低活跃</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-yellow-400 rounded"></div>
                  <span className="text-gray-500 dark:text-gray-400">中活跃</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-red-500 rounded"></div>
                  <span className="text-gray-500 dark:text-gray-400">高活跃</span>
                </div>
              </div>
              <span className="text-gray-500 dark:text-gray-400">峰值: 21:00</span>
            </div>
          </div>

          {/* 用户增长趋势 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">用户增长</h3>
            <div className="h-32 flex items-end justify-between space-x-2">
              {[
                { label: '第1周', newUsers: 12, retention: 89 },
                { label: '第2周', newUsers: 18, retention: 92 },
                { label: '第3周', newUsers: 15, retention: 87 },
                { label: '第4周', newUsers: 23, retention: 94 }
              ].map((week, index) => (
                <div key={index} className="flex-1 text-center">
                  <div className="relative mb-4">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-t-lg h-24 relative overflow-hidden">
                      <div 
                        className="absolute bottom-0 w-full bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t-lg transition-all duration-1000"
                        style={{ height: `${(week.newUsers / 25) * 100}%` }}
                      />
                    </div>
                    <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs font-bold text-gray-900 dark:text-white bg-white dark:bg-gray-800 px-2 py-1 rounded shadow">
                      +{week.newUsers}
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-xs text-gray-500 dark:text-gray-400">{week.label}</div>
                    <div className="text-xs text-green-600 dark:text-green-400 font-medium">留存: {week.retention}%</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 数据导出 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">数据导出</h3>
            <div className="space-y-3">
              <button 
                onClick={() => handleExport('csv')}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Download className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">用户数据 CSV</span>
              </button>
              <button 
                onClick={() => handleExport('excel')}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.FileText className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">消息统计 Excel</span>
              </button>
              <button 
                onClick={() => setIsReportModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.BarChart3 className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">分析报告 PDF</span>
              </button>
              <button 
                onClick={() => setIsFilterModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Filter className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">高级筛选</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 导出数据模态框 */}
      {isExportModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">导出数据</h3>
              {!isLoading && (
                <button 
                  onClick={() => setIsExportModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <icons.X className="w-5 h-5" />
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  {isLoading ? (
                    <icons.Download className="w-8 h-8 text-black animate-pulse" />
                  ) : (
                    <icons.CheckCircle className="w-8 h-8 text-black" />
                  )}
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {isLoading ? '正在导出数据...' : '导出完成！'}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {isLoading ? '请稍候，正在生成数据文件' : operationResult?.message || '数据已成功导出'}
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">导出进度</span>
                  <span className="font-medium text-gray-900 dark:text-white">{exportProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${exportProgress}%` }}
                  />
                </div>
              </div>
              
              {!isLoading && (
                <button
                  onClick={() => setIsExportModalOpen(false)}
                  className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-medium"
                >
                  完成
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 分析报告模态框 */}
      {isReportModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">生成分析报告</h3>
              <button 
                onClick={() => setIsReportModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('报告') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">报告生成成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">分析报告已生成并可下载</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleGenerateReport(); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">报告类型</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>综合分析报告</option>
                    <option>用户活跃度报告</option>
                    <option>消息统计报告</option>
                    <option>增长趋势报告</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">时间范围</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>过去7天</option>
                    <option>过去30天</option>
                    <option>过去90天</option>
                    <option>自定义范围</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">报告格式</label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="format" className="text-yellow-600 focus:ring-yellow-500" defaultChecked />
                      <span className="text-sm text-gray-700 dark:text-gray-300">PDF 文档</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="format" className="text-yellow-600 focus:ring-yellow-500" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">Excel 表格</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="format" className="text-yellow-600 focus:ring-yellow-500" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">在线仪表板</span>
                    </label>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsReportModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '生成中...' : '生成报告'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 高级筛选模态框 */}
      {isFilterModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">高级数据筛选</h3>
              <button 
                onClick={() => setIsFilterModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('筛选') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">筛选应用成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">数据已根据筛选条件更新</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleAdvancedFilter(); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">用户类型</label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500" defaultChecked />
                      <span className="text-sm text-gray-700 dark:text-gray-300">所有用户</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">仅活跃用户</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">仅新用户</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">数据类型</label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500" defaultChecked />
                      <span className="text-sm text-gray-700 dark:text-gray-300">消息数据</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500" defaultChecked />
                      <span className="text-sm text-gray-700 dark:text-gray-300">用户活跃度</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500" defaultChecked />
                      <span className="text-sm text-gray-700 dark:text-gray-300">增长数据</span>
                    </label>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">时间范围</label>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">开始日期</label>
                      <input
                        type="date"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">结束日期</label>
                      <input
                        type="date"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsFilterModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '应用中...' : '应用筛选'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default DataAnalytics;