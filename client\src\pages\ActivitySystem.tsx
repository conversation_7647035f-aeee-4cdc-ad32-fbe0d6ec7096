import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useData } from '../contexts/DataContext';
import { useNotificationContext } from '../contexts/NotificationContext';
import { useWebSocket } from '../hooks/useWebSocket';
import { queryClient } from '../lib/queryClient';
import { Search, Plus, Edit2, Trash2, Award, TrendingUp, Users, Calendar, Filter } from 'lucide-react';

interface ActivityRecord {
  id: number;
  userId: number;
  activityType: string;
  points: number;
  description: string;
  channelId?: string;
  channelName?: string;
  metadata?: any;
  createdAt: string;
  user?: {
    id: number;
    username: string;
    avatar?: string;
  };
}

interface ActivityStats {
  totalActivities: number;
  totalPoints: number;
  todayActivities: number;
  activeUsers: number;
  topActivityTypes: Array<{
    type: string;
    count: number;
    points: number;
  }>;
}

const ActivitySystem = () => {
  const { showNotification } = useNotificationContext();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedActivityType, setSelectedActivityType] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  // WebSocket connection for real-time updates
  useWebSocket();

  // 获取活跃度记录
  const { data: activityRecords = [], isLoading } = useQuery({
    queryKey: ['/api/activity-records'],
    staleTime: 30000,
    refetchInterval: 30000,
  });

  // 获取活跃度统计
  const { data: activityStats } = useQuery<ActivityStats>({
    queryKey: ['/api/activity/stats'],
    staleTime: 60000,
    refetchInterval: 60000,
  });

  // 获取用户列表
  const { data: users = [] } = useQuery({
    queryKey: ['/api/discord-users'],
    staleTime: 300000,
  });

  // 筛选活跃度记录
  const filteredRecords = activityRecords.filter((record: ActivityRecord) => {
    const matchesSearch = record.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.user?.username.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedActivityType === 'all' || record.activityType === selectedActivityType;
    
    let matchesDate = true;
    if (dateRange !== 'all') {
      const recordDate = new Date(record.createdAt);
      const now = new Date();
      if (dateRange === 'today') {
        matchesDate = recordDate.toDateString() === now.toDateString();
      } else if (dateRange === 'week') {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        matchesDate = recordDate >= weekAgo;
      } else if (dateRange === 'month') {
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        matchesDate = recordDate >= monthAgo;
      }
    }
    
    return matchesSearch && matchesType && matchesDate;
  });

  // 分页
  const totalPages = Math.ceil(filteredRecords.length / itemsPerPage);
  const paginatedRecords = filteredRecords.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // 获取活跃度类型列表
  const activityTypes = [...new Set(activityRecords.map((record: ActivityRecord) => record.activityType))];

  // 获取活跃度类型的中文映射
  const getActivityTypeLabel = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'message': '发送消息',
      'voice': '语音活动',
      'reaction': '添加反应',
      'command': '使用命令',
      'join': '加入频道',
      'leave': '离开频道',
      'checkin': '每日签到',
      'code_redemption': '兑换码使用'
    };
    return typeMap[type] || type;
  };

  // 获取活跃度类型的图标
  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case 'message': return '💬';
      case 'voice': return '🎙️';
      case 'reaction': return '⭐';
      case 'command': return '⚡';
      case 'join': return '👋';
      case 'leave': return '👋';
      case 'checkin': return '✅';
      case 'code_redemption': return '🎁';
      default: return '🔔';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-yellow-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载活跃度数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">活跃度系统</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            管理和跟踪用户活跃度记录
          </p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <TrendingUp className="w-6 h-6 text-yellow-600 dark:text-yellow-300" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">总活跃度</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {activityStats?.totalActivities || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
                <Award className="w-6 h-6 text-green-600 dark:text-green-300" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">活跃度积分</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {activityStats?.totalPoints || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Calendar className="w-6 h-6 text-blue-600 dark:text-blue-300" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日活跃</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {activityStats?.todayActivities || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <Users className="w-6 h-6 text-purple-600 dark:text-purple-300" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">活跃用户</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {activityStats?.activeUsers || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* 筛选和搜索 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                <input
                  type="text"
                  placeholder="搜索活跃度记录..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <select
                value={selectedActivityType}
                onChange={(e) => setSelectedActivityType(e.target.value)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">所有类型</option>
                {activityTypes.map(type => (
                  <option key={type} value={type}>{getActivityTypeLabel(type)}</option>
                ))}
              </select>

              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="all">所有时间</option>
                <option value="today">今天</option>
                <option value="week">本周</option>
                <option value="month">本月</option>
              </select>
            </div>

            <div className="text-sm text-gray-500 dark:text-gray-400">
              显示 {paginatedRecords.length} / {filteredRecords.length} 条记录
            </div>
          </div>
        </div>

        {/* 活跃度记录表格 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    用户
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    活动类型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    积分
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    描述
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    时间
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {paginatedRecords.map((record: ActivityRecord) => (
                  <tr key={record.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-yellow-400 flex items-center justify-center">
                            <span className="text-sm font-medium text-black">
                              {record.user?.username.charAt(0).toUpperCase() || '?'}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {record.user?.username || '未知用户'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-lg mr-2">{getActivityTypeIcon(record.activityType)}</span>
                        <span className="text-sm text-gray-900 dark:text-white">
                          {getActivityTypeLabel(record.activityType)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white font-medium">
                        +{record.points}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {record.description}
                      </div>
                      {record.channelName && (
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          #{record.channelName}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {new Date(record.createdAt).toLocaleString('zh-CN')}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  第 {currentPage} 页，共 {totalPages} 页
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-gray-900 dark:text-white"
                  >
                    上一页
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-gray-900 dark:text-white"
                  >
                    下一页
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ActivitySystem;