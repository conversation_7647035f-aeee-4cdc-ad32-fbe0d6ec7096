import React, { useState, Suspense } from 'react';
import { ThemeProvider } from './contexts/ThemeContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { DataProvider } from './contexts/DataContext';
import Layout from './components/Layout';
import WebSocketStatus from './components/WebSocketStatus';

// 懒加载页面组件
const Dashboard = React.lazy(() => import('./pages/Dashboard').catch(error => {
  console.error('Failed to load Dashboard:', error);
  return { default: () => <div className="p-8 text-center text-red-600">Dashboard 加载失败</div> };
}));

const UserManagement = React.lazy(() => import('./pages/UserManagement').catch(error => {
  console.error('Failed to load UserManagement:', error);
  return { default: () => <div className="p-8 text-center text-red-600">UserManagement 加载失败</div> };
}));

const PointSystem = React.lazy(() => import('./pages/PointSystem').catch(error => {
  console.error('Failed to load PointSystem:', error);
  return { default: () => <div className="p-8 text-center text-red-600">PointSystem 加载失败</div> };
}));

const CheckinSystem = React.lazy(() => import('./pages/CheckinSystem').catch(error => {
  console.error('Failed to load CheckinSystem:', error);
  return { default: () => <div className="p-8 text-center text-red-600">CheckinSystem 加载失败</div> };
}));

const TitleSystem = React.lazy(() => import('./pages/TitleSystem').catch(error => {
  console.error('Failed to load TitleSystem:', error);
  return { default: () => <div className="p-8 text-center text-red-600">TitleSystem 加载失败</div> };
}));

const AutomationRules = React.lazy(() => import('./pages/AutomationRules').catch(error => {
  console.error('Failed to load AutomationRules:', error);
  return { default: () => <div className="p-8 text-center text-red-600">AutomationRules 加载失败</div> };
}));

const DataAnalytics = React.lazy(() => import('./pages/DataAnalytics').catch(error => {
  console.error('Failed to load DataAnalytics:', error);
  return { default: () => <div className="p-8 text-center text-red-600">DataAnalytics 加载失败</div> };
}));

const FunctionTesting = React.lazy(() => import('./pages/FunctionTesting').catch(error => {
  console.error('Failed to load FunctionTesting:', error);
  return { default: () => <div className="p-8 text-center text-red-600">FunctionTesting 加载失败</div> };
}));

const SystemLogs = React.lazy(() => import('./pages/SystemLogs').catch(error => {
  console.error('Failed to load SystemLogs:', error);
  return { default: () => <div className="p-8 text-center text-red-600">SystemLogs 加载失败</div> };
}));

const Settings = React.lazy(() => import('./pages/Settings').catch(error => {
  console.error('Failed to load Settings:', error);
  return { default: () => <div className="p-8 text-center text-red-600">Settings 加载失败</div> };
}));

const LotterySystem = React.lazy(() => import('./pages/LotterySystem').catch(error => {
  console.error('Failed to load LotterySystem:', error);
  return { default: () => <div className="p-8 text-center text-red-600">LotterySystem 加载失败</div> };
}));

const tabs = [
  { id: 'dashboard', name: '仪表板', icon: 'BarChart3', component: Dashboard },
  { id: 'users', name: '用户管理', icon: 'Users', component: UserManagement },
  { id: 'points', name: '积分系统', icon: 'Star', component: PointSystem },
  { id: 'checkin', name: '签到系统', icon: 'Calendar', component: CheckinSystem },
  { id: 'titles', name: '头衔系统', icon: 'Crown', component: TitleSystem },
  { id: 'lottery', name: '抽奖系统', icon: 'Gift', component: LotterySystem },
  { id: 'automation', name: '自动化规则', icon: 'Workflow', component: AutomationRules },
  { id: 'analytics', name: '数据分析', icon: 'TrendingUp', component: DataAnalytics },
  { id: 'testing', name: '功能测试', icon: 'TestTube', component: FunctionTesting },
  { id: 'logs', name: '系统日志', icon: 'FileText', component: SystemLogs },
  { id: 'settings', name: '设置中心', icon: 'Settings', component: Settings },
];

// 加载组件
const LoadingSpinner = () => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
    <div className="text-center">
      <div className="w-16 h-16 border-4 border-yellow-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <p className="text-gray-600 dark:text-gray-400">加载中...</p>
    </div>
  </div>
);

// 错误回退组件
const ErrorFallback = ({ error, resetError }: { error: Error, resetError: () => void }) => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
    <div className="text-center p-8 max-w-md">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <span className="text-red-600 text-2xl">⚠️</span>
      </div>
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">页面加载出错</h2>
      <p className="text-gray-600 dark:text-gray-400 mb-4">页面加载失败，请重试</p>
      <div className="space-y-2">
        <button
          onClick={resetError}
          className="w-full bg-yellow-400 hover:bg-yellow-500 text-black px-6 py-3 rounded-lg font-medium transition-colors"
        >
          重新加载
        </button>
        <button
          onClick={() => window.location.reload()}
          className="w-full bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors"
        >
          刷新页面
        </button>
      </div>
      <details className="mt-4 text-left">
        <summary className="cursor-pointer text-sm text-gray-500">错误详情</summary>
        <pre className="mt-2 text-xs text-gray-400 bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto">
          {error.message}
        </pre>
      </details>
    </div>
  </div>
);

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showWebSocketStatus, setShowWebSocketStatus] = useState(true);
  
  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || Dashboard;

  return (
    <ThemeProvider>
      <NotificationProvider>
        <DataProvider>
          <Layout 
            tabs={tabs} 
            activeTab={activeTab} 
            onTabChange={setActiveTab}
          >
            <Suspense fallback={<LoadingSpinner />}>
              <ActiveComponent onTabChange={setActiveTab} />
            </Suspense>
          </Layout>
          
          {/* WebSocket 状态监控器 */}
          {showWebSocketStatus && <WebSocketStatus />}
          
          {/* 隐藏/显示状态监控器的按钮 */}
          <button
            onClick={() => setShowWebSocketStatus(!showWebSocketStatus)}
            className="fixed bottom-4 left-4 z-50 w-12 h-12 bg-yellow-400 hover:bg-yellow-500 text-black rounded-full flex items-center justify-center shadow-lg transition-colors"
            title={showWebSocketStatus ? '隐藏 WebSocket 状态' : '显示 WebSocket 状态'}
          >
            {showWebSocketStatus ? '🔌' : '📊'}
          </button>
        </DataProvider>
      </NotificationProvider>
    </ThemeProvider>
  );
}

export default App;