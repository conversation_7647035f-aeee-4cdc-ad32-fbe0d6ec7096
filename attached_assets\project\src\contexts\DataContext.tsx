import React, { createContext, useContext, useState, useEffect } from 'react';
import { useWebSocket } from '../hooks/useWebSocket';

interface BotStatus {
  connected: boolean;
  ready: boolean;
  latency: number;
  uptime: number;
  guilds: number;
  users: number;
  error: string | null;
}

interface ServerData {
  memberCount: number;
  onlineCount: number;
  activeChannels: number;
}

interface Stats {
  userCount: number;
  todayCheckins: number;
  recentActiveUsers: number;
  totalPoints?: number;
  timestamp: string;
}

interface DataContextType {
  botStatus: BotStatus;
  serverData: ServerData;
  stats: Stats;
  isConnected: boolean;
  refreshData: () => void;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [botStatus, setBotStatus] = useState<BotStatus>({
    connected: false,
    ready: false,
    latency: 0,
    uptime: 0,
    guilds: 0,
    users: 0,
    error: null
  });

  const [serverData, setServerData] = useState<ServerData>({
    memberCount: 0,
    onlineCount: 0,
    activeChannels: 0
  });

  const [stats, setStats] = useState<Stats>({
    userCount: 0,
    todayCheckins: 0,
    recentActiveUsers: 0,
    timestamp: new Date().toISOString()
  });

  // 使用统一的 WebSocket 连接（不需要自定义 URL）
  const { isConnected, lastMessage, sendMessage } = useWebSocket();

  // Handle WebSocket messages
  useEffect(() => {
    if (!lastMessage) return;

    switch (lastMessage.type) {
      case 'initial_data':
        setBotStatus(lastMessage.data.botStatus);
        setServerData(lastMessage.data.serverData);
        setStats(lastMessage.data.stats);
        break;
      
      case 'real_time_update':
        setBotStatus(lastMessage.data.botStatus);
        setServerData(lastMessage.data.serverData);
        setStats(lastMessage.data.stats);
        break;
      
      case 'bot_status_update':
        setBotStatus(lastMessage.data.botStatus);
        break;
      
      case 'bot_ready':
        setBotStatus(prev => ({
          ...prev,
          connected: true,
          ready: true,
          guilds: lastMessage.data.guilds,
          users: lastMessage.data.users
        }));
        break;
      
      case 'bot_error':
        setBotStatus(prev => ({
          ...prev,
          error: lastMessage.data.error,
          connected: false,
          ready: false
        }));
        break;
      
      case 'member_joined':
        setServerData(prev => ({
          ...prev,
          memberCount: prev.memberCount + 1
        }));
        break;
      
      case 'users_synced':
        setStats(prev => ({
          ...prev,
          userCount: lastMessage.data.count,
          timestamp: new Date().toISOString()
        }));
        break;
    }
  }, [lastMessage]);

  const refreshData = () => {
    sendMessage({ type: 'request_update' });
  };

  return (
    <DataContext.Provider value={{
      botStatus,
      serverData,
      stats,
      isConnected,
      refreshData
    }}>
      {children}
    </DataContext.Provider>
  );
};

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};