import { getDb } from '../database/init.js';
import { logger } from './logger.js';
import { broadcastEvent } from '../websocket/index.js';

export async function addPoints(userId, amount, reason, type = 'earned', adminId = null) {
  try {
    const db = getDb();
    
    // Add points to user
    await db.run(`
      UPDATE users 
      SET points = points + ?, updated_at = ?
      WHERE id = ?
    `, [amount, new Date().toISOString(), userId]);

    // Record in points history
    await db.run(`
      INSERT INTO points_history (user_id, amount, reason, type, admin_id)
      VALUES (?, ?, ?, ?, ?)
    `, [userId, amount, reason, type, adminId]);

    // Update user title based on new points
    await updateUserTitle(userId);

    // Broadcast real-time update
    try {
      broadcastEvent('points_updated', {
        userId,
        amount,
        reason,
        type
      });
    } catch (broadcastError) {
      logger.error('Failed to broadcast points update:', broadcastError);
    }

    logger.info(`Points added: ${amount} to user ${userId} for ${reason}`);
    return true;
  } catch (error) {
    logger.error('Error adding points:', error);
    return false;
  }
}

export async function deductPoints(userId, amount, reason, adminId = null) {
  try {
    const db = getDb();
    
    // Check if user has enough points
    const user = await db.get('SELECT points FROM users WHERE id = ?', [userId]);
    if (!user || user.points < amount) {
      throw new Error('Insufficient points');
    }

    // Deduct points from user
    await db.run(`
      UPDATE users 
      SET points = points - ?, updated_at = ?
      WHERE id = ?
    `, [amount, new Date().toISOString(), userId]);

    // Record in points history
    await db.run(`
      INSERT INTO points_history (user_id, amount, reason, type, admin_id)
      VALUES (?, ?, ?, ?, ?)
    `, [userId, -amount, reason, 'deducted', adminId]);

    // Update user title based on new points
    await updateUserTitle(userId);

    // Broadcast real-time update
    try {
      broadcastEvent('points_updated', {
        userId,
        amount: -amount,
        reason,
        type: 'deducted'
      });
    } catch (broadcastError) {
      logger.error('Failed to broadcast points deduction:', broadcastError);
    }

    logger.info(`Points deducted: ${amount} from user ${userId} for ${reason}`);
    return true;
  } catch (error) {
    logger.error('Error deducting points:', error);
    return false;
  }
}

async function updateUserTitle(userId) {
  try {
    const db = getDb();
    const user = await db.get('SELECT points FROM users WHERE id = ?', [userId]);
    if (!user) return;

    const title = await db.get(`
      SELECT name FROM titles 
      WHERE min_points <= ? AND (max_points IS NULL OR max_points >= ?)
      ORDER BY min_points DESC
      LIMIT 1
    `, [user.points, user.points]);

    if (title) {
      await db.run('UPDATE users SET title = ? WHERE id = ?', [title.name, userId]);
      
      // Broadcast title update
      try {
        broadcastEvent('title_updated', {
          userId,
          newTitle: title.name
        });
      } catch (broadcastError) {
        logger.error('Failed to broadcast title update:', broadcastError);
      }
    }
  } catch (error) {
    logger.error('Error updating user title:', error);
  }
}