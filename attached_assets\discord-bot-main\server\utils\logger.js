import winston from 'winston';
import { mkdirSync, existsSync } from 'fs';

// Ensure logs directory exists
try {
  if (!existsSync('./logs')) {
    mkdirSync('./logs', { recursive: true });
  }
} catch (error) {
  // Directory already exists or cannot be created
  console.warn('Could not create logs directory:', error.message);
}

// Create logger
export const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'discord-bot' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Add file transports only if logs directory exists
if (existsSync('./logs')) {
  logger.add(new winston.transports.File({ filename: 'logs/error.log', level: 'error' }));
  logger.add(new winston.transports.File({ filename: 'logs/combined.log' }));
}

// Custom log function that also saves to database
export async function logToDatabase(level, module, action, details, userId = null, ipAddress = null, userAgent = null, duration = null) {
  try {
    // This will be implemented when database is available
    logger.info('Database logging not yet implemented');
  } catch (error) {
    logger.error('Failed to log to database:', error);
  }
}