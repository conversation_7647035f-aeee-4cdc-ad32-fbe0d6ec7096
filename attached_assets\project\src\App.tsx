import React, { useState } from 'react';
import { ThemeProvider } from './contexts/ThemeContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { DataProvider } from './contexts/DataContext';
import Layout from './components/Layout';
import WebSocketStatus from './components/WebSocketStatus';
import Dashboard from './pages/Dashboard';
import UserManagement from './pages/UserManagement';
import PointSystem from './pages/PointSystem';
import CheckinSystem from './pages/CheckinSystem';
import TitleSystem from './pages/TitleSystem';
import AutomationRules from './pages/AutomationRules';
import DataAnalytics from './pages/DataAnalytics';
import FunctionTesting from './pages/FunctionTesting';
import SystemLogs from './pages/SystemLogs';
import Settings from './pages/Settings';
import LotterySystem from './pages/LotterySystem';

const tabs = [
  { id: 'dashboard', name: '仪表板', icon: 'BarChart3', component: Dashboard },
  { id: 'users', name: '用户管理', icon: 'Users', component: UserManagement },
  { id: 'points', name: '积分系统', icon: 'Star', component: PointSystem },
  { id: 'checkin', name: '签到系统', icon: 'Calendar', component: CheckinSystem },
  { id: 'titles', name: '头衔系统', icon: 'Crown', component: TitleSystem },
  { id: 'lottery', name: '抽奖系统', icon: 'Gift', component: LotterySystem },
  { id: 'automation', name: '自动化规则', icon: 'Workflow', component: AutomationRules },
  { id: 'analytics', name: '数据分析', icon: 'TrendingUp', component: DataAnalytics },
  { id: 'testing', name: '功能测试', icon: 'TestTube', component: FunctionTesting },
  { id: 'logs', name: '系统日志', icon: 'FileText', component: SystemLogs },
  { id: 'settings', name: '设置中心', icon: 'Settings', component: Settings },
];

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showWebSocketStatus, setShowWebSocketStatus] = useState(true);
  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || Dashboard;

  return (
    <ThemeProvider>
      <NotificationProvider>
        <DataProvider>
          <Layout 
            tabs={tabs} 
            activeTab={activeTab} 
            onTabChange={setActiveTab}
          >
            <ActiveComponent onTabChange={setActiveTab} />
          </Layout>
          
          {/* WebSocket 状态监控器 */}
          {showWebSocketStatus && <WebSocketStatus />}
          
          {/* 隐藏/显示状态监控器的按钮 */}
          <button
            onClick={() => setShowWebSocketStatus(!showWebSocketStatus)}
            className="fixed bottom-4 left-4 z-50 w-12 h-12 bg-yellow-400 hover:bg-yellow-500 text-black rounded-full flex items-center justify-center shadow-lg transition-colors"
            title={showWebSocketStatus ? '隐藏 WebSocket 状态' : '显示 WebSocket 状态'}
          >
            {showWebSocketStatus ? '🔌' : '📊'}
          </button>
        </DataProvider>
      </NotificationProvider>
    </ThemeProvider>
  );
}

export default App;