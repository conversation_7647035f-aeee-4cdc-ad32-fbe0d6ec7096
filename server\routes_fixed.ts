import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import { Client, GatewayIntentBits, Events } from 'discord.js';

// Discord bot client and configuration
let discordClient: Client | null = null;
let botConfig = {
  token: '',
  isConnected: false,
  status: {
    connected: false,
    ready: false,
    latency: 0,
    uptime: 0,
    guilds: 0,
    users: 0,
    error: null as string | null
  }
};

// Real Discord data cache
let realServerData = {
  memberCount: 0,
  onlineCount: 0,
  activeChannels: 0,
  channels: [] as any[],
  roles: [] as any[]
};

// WebSocket clients for real-time updates
const wsClients = new Set<WebSocket>();

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // Initialize bot config from database
  try {
    const savedToken = await storage.getBotSetting('bot_token');
    if (savedToken) {
      botConfig.token = savedToken;
      console.log('Loaded saved bot token from database');
    }
  } catch (error) {
    console.error('Error loading bot token from database:', error);
  }

  // WebSocket server setup
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });

  wss.on('connection', (ws) => {
    console.log('WebSocket client connected');
    wsClients.add(ws);

    ws.on('close', () => {
      wsClients.delete(ws);
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      wsClients.delete(ws);
    });
  });

  // Broadcast function for real-time updates
  function broadcast(data: any) {
    const message = JSON.stringify(data);
    wsClients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    });
  }

  // Real Discord bot connection function
  async function connectDiscordBot(token: string) {
    try {
      // Disconnect existing client if any
      if (discordClient) {
        discordClient.destroy();
        discordClient = null;
      }

      // Create new Discord client
      discordClient = new Client({
        intents: [
          GatewayIntentBits.Guilds,
          GatewayIntentBits.GuildMembers,
          GatewayIntentBits.GuildPresences,
          GatewayIntentBits.GuildMessages
        ]
      });

      // Set up event handlers
      discordClient.once(Events.ClientReady, async (client) => {
        console.log(`Discord bot ready as ${client.user.tag}`);
        
        // Update bot status with real data
        const guilds = client.guilds.cache;
        const totalMembers = guilds.reduce((acc, guild) => acc + guild.memberCount, 0);
        
        botConfig.status = {
          connected: true,
          ready: true,
          latency: client.ws.ping,
          uptime: Date.now(),
          guilds: guilds.size,
          users: totalMembers,
          error: null
        };
        
        // Fetch real server data from first guild
        const firstGuild = guilds.first();
        if (firstGuild) {
          try {
            // Fetch all members
            await firstGuild.members.fetch();
            
            // Get real channel data
            const channels = firstGuild.channels.cache
              .filter(channel => channel.type === 0) // Text channels
              .map(channel => ({
                id: channel.id,
                name: channel.name,
                type: channel.type,
                category: channel.parent?.name || '未分类'
              }));

            // Get real role data
            const roles = firstGuild.roles.cache
              .filter(role => !role.managed && role.name !== '@everyone')
              .map(role => ({
                id: role.id,
                name: role.name,
                color: role.hexColor,
                memberCount: role.members.size
              }));

            // Update real server data
            realServerData = {
              memberCount: firstGuild.memberCount,
              onlineCount: firstGuild.presences.cache.filter(p => p.status !== 'offline').size,
              activeChannels: channels.length,
              channels: channels,
              roles: roles
            };

            // Broadcast real data
            broadcast({
              type: 'serverData',
              data: realServerData
            });

            console.log(`Fetched real data: ${firstGuild.memberCount} members, ${channels.length} channels, ${roles.length} roles`);
            
          } catch (fetchError) {
            console.error('Error fetching guild data:', fetchError);
          }
        }

        // Broadcast bot status
        broadcast({
          type: 'botStatus',
          data: botConfig.status
        });
      });

      discordClient.on('error', (error) => {
        console.error('Discord client error:', error);
        botConfig.status.error = error.message;
        broadcast({
          type: 'botStatus',
          data: botConfig.status
        });
      });

      // Login with token
      await discordClient.login(token);
      botConfig.token = token;
      botConfig.isConnected = true;
      
    } catch (error: any) {
      console.error('Failed to connect Discord bot:', error);
      botConfig.status = {
        connected: false,
        ready: false,
        latency: 0,
        uptime: 0,
        guilds: 0,
        users: 0,
        error: error.message
      };
      
      broadcast({
        type: 'botStatus',
        data: botConfig.status
      });
    }
  }

  // API Routes

  // Settings endpoints
  app.get('/api/settings', async (req, res) => {
    try {
      const settings = {
        bot_token: await storage.getBotSetting('bot_token') || '',
        server_name: await storage.getBotSetting('server_name') || 'Discord服务器',
        welcome_message: await storage.getBotSetting('welcome_message') || '欢迎加入我们的服务器！',
        auto_role: await storage.getBotSetting('auto_role') || '',
        log_channel: await storage.getBotSetting('log_channel') || ''
      };
      
      // Hide token for security
      if (settings.bot_token) {
        settings.bot_token = '****' + settings.bot_token.slice(-10);
      }
      
      res.json({
        success: true,
        data: settings
      });
    } catch (error) {
      console.error('Error fetching settings:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch settings'
      });
    }
  });

  // Save settings
  app.post('/api/settings', async (req, res) => {
    try {
      const { server_name, welcome_message, auto_role, log_channel } = req.body;
      
      if (server_name) await storage.setBotSetting('server_name', server_name);
      if (welcome_message) await storage.setBotSetting('welcome_message', welcome_message);
      if (auto_role) await storage.setBotSetting('auto_role', auto_role);
      if (log_channel) await storage.setBotSetting('log_channel', log_channel);
      
      res.json({
        success: true,
        message: 'Settings saved successfully'
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to save settings'
      });
    }
  });

  // Test Discord token endpoint
  app.post('/api/bot/test-token', async (req, res) => {
    const { token } = req.body;
    
    if (!token) {
      return res.json({
        success: false,
        message: 'Token is required'
      });
    }

    try {
      // Create temporary Discord client for testing
      const testClient = new Client({
        intents: [GatewayIntentBits.Guilds]
      });
      
      let testResult = {
        success: false,
        message: '',
        data: null as any
      };
      
      try {
        // Set timeout for test
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('连接超时')), 10000);
        });
        
        const loginPromise = new Promise((resolve, reject) => {
          testClient.once(Events.ClientReady, (client) => {
            const guilds = client.guilds.cache;
            resolve({
              success: true,
              message: 'Token验证成功',
              data: {
                bot_name: client.user.tag,
                bot_id: client.user.id,
                guilds: guilds.size,
                users: guilds.reduce((acc, guild) => acc + guild.memberCount, 0),
                avatar: client.user.displayAvatarURL()
              }
            });
          });
          
          testClient.on('error', reject);
          testClient.login(token).catch(reject);
        });
        
        testResult = await Promise.race([loginPromise, timeoutPromise]) as any;
        
      } catch (error: any) {
        testResult = {
          success: false,
          message: error.message.includes('TOKEN_INVALID') ? '无效的机器人令牌' : 
                   error.message.includes('timeout') ? '连接超时，请检查网络' : 
                   '连接失败: ' + error.message,
          data: null
        };
      } finally {
        testClient.destroy();
      }
      
      res.json(testResult);
      
    } catch (error: any) {
      console.error('Error testing token:', error);
      res.status(500).json({
        success: false,
        message: '服务器错误，请稍后重试'
      });
    }
  });

  // Save and apply Discord token
  app.put('/api/settings/bot_token', async (req, res) => {
    try {
      const { token } = req.body;
      
      if (!token) {
        return res.json({
          success: false,
          message: 'Token is required'
        });
      }
      
      // Save token to database
      await storage.setBotSetting('bot_token', token);
      
      // Connect to Discord with the new token
      try {
        await connectDiscordBot(token);
        
        res.json({
          success: true,
          message: 'Token保存成功，正在连接Discord...'
        });
        
        // Log the successful connection
        await storage.createSystemLog({
          level: 'info',
          user: 'system',
          module: 'settings',
          action: 'token_updated',
          details: 'Discord bot token updated and connection initiated'
        });
        
      } catch (error: any) {
        // Even if connection fails, token is saved for retry
        res.json({
          success: true,
          message: 'Token已保存，连接将在后台重试'
        });
        
        await storage.createSystemLog({
          level: 'error',
          user: 'system',
          module: 'settings',
          action: 'connection_failed',
          details: `Discord bot connection failed after token update: ${error.message}`
        });
      }
      
    } catch (error: any) {
      console.error('Error saving token:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to save token'
      });
    }
  });

  // Start bot endpoint
  app.post('/api/bot/start', async (req, res) => {
    try {
      const savedToken = await storage.getBotSetting('bot_token');
      
      if (!savedToken) {
        return res.json({
          success: false,
          message: 'No token configured'
        });
      }
      
      // Connect to Discord with real token
      await connectDiscordBot(savedToken);
      
      res.json({
        success: true,
        message: 'Bot connection initiated',
        data: {
          name: 'Discord 管理机器人',
          connecting: true
        }
      });
    } catch (error: any) {
      console.error('Error starting bot:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to start bot'
      });
    }
  });

  // Get bot status
  app.get('/api/bot/status', async (req, res) => {
    res.json({
      success: true,
      data: botConfig.status
    });
  });

  // Sync data endpoint - forces refresh of Discord data
  app.post('/api/sync/data', async (req, res) => {
    try {
      if (!discordClient || !botConfig.isConnected) {
        return res.json({
          success: false,
          message: '机器人未连接'
        });
      }

      // Refresh Discord data
      const guilds = discordClient.guilds.cache;
      const firstGuild = guilds.first();
      
      if (firstGuild) {
        await firstGuild.members.fetch();
        
        const channels = firstGuild.channels.cache
          .filter(channel => channel.type === 0)
          .map(channel => ({
            id: channel.id,
            name: channel.name,
            type: channel.type,
            category: channel.parent?.name || '未分类'
          }));

        const roles = firstGuild.roles.cache
          .filter(role => !role.managed && role.name !== '@everyone')
          .map(role => ({
            id: role.id,
            name: role.name,
            color: role.hexColor,
            memberCount: role.members.size
          }));

        realServerData = {
          memberCount: firstGuild.memberCount,
          onlineCount: firstGuild.presences.cache.filter(p => p.status !== 'offline').size,
          activeChannels: channels.length,
          channels: channels,
          roles: roles
        };

        broadcast({
          type: 'serverData',
          data: realServerData
        });

        res.json({
          success: true,
          message: '数据同步完成',
          data: realServerData
        });
      } else {
        res.json({
          success: false,
          message: '没有找到服务器'
        });
      }
    } catch (error: any) {
      console.error('Error syncing data:', error);
      res.status(500).json({
        success: false,
        message: '同步失败: ' + error.message
      });
    }
  });

  // Initialize with saved token
  setTimeout(async () => {
    if (botConfig.token) {
      await connectDiscordBot(botConfig.token);
    }
  }, 3000);

  // Simulate real-time updates
  setInterval(() => {
    if (wsClients.size > 0 && botConfig.isConnected) {
      // Update bot status with real latency
      if (discordClient) {
        botConfig.status.latency = discordClient.ws.ping;
        broadcast({
          type: 'botStatus',
          data: botConfig.status
        });
      }

      // Simulate activities
      const activities = [
        { action: '用户完成签到', time: '刚刚', icon: 'Calendar', color: 'yellow' },
        { action: '新用户加入服务器', time: '刚刚', icon: 'UserPlus', color: 'black' },
        { action: '自动化规则触发', time: '刚刚', icon: 'Workflow', color: 'gray' },
        { action: '积分奖励发放', time: '刚刚', icon: 'Coins', color: 'green' }
      ];

      if (Math.random() < 0.3) {
        const activity = activities[Math.floor(Math.random() * activities.length)];
        broadcast({
          type: 'activity',
          data: activity
        });
      }
    }
  }, 10000);

  return httpServer;
}