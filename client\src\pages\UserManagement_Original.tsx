import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import * as icons from 'lucide-react';

const UserManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterRole, setFilterRole] = useState('all');
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [showSyncAlert, setShowSyncAlert] = useState(false);

  const queryClient = useQueryClient();

  // Fetch only Discord users from API
  const { data: usersResponse, isLoading: usersLoading, refetch } = useQuery({
    queryKey: ['/api/users'],
    queryFn: async () => {
      const response = await fetch('/api/users');
      if (!response.ok) throw new Error('Failed to fetch users');
      return response.json();
    }
  });

  // Discord user sync mutation
  const syncUsersMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/users/sync', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      if (!response.ok) throw new Error('Failed to sync users');
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        setShowSyncAlert(true);
        setTimeout(() => setShowSyncAlert(false), 3000);
        queryClient.invalidateQueries({ queryKey: ['/api/users'] });
      }
    }
  });

  // Auto-sync on component mount
  useEffect(() => {
    const autoSync = async () => {
      try {
        await syncUsersMutation.mutateAsync();
      } catch (error) {
        console.log('Auto-sync completed');
      }
    };
    
    const timer = setTimeout(autoSync, 500);
    return () => clearTimeout(timer);
  }, []);

  // Filter to show only Discord users (users with discord_id)
  const discordUsers = (usersResponse?.data || []).filter((user: any) => user.discordId);

  const filteredUsers = discordUsers.filter((user: any) => {
    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || user.status === filterStatus;
    const matchesRole = filterRole === 'all' || (user.roles && user.roles.includes(filterRole));
    return matchesSearch && matchesStatus && matchesRole;
  });

  const handleSelectUser = (userId: number) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map((user: any) => user.id));
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <div className="w-3 h-3 bg-green-500 rounded-full"></div>;
      case 'idle': return <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>;
      case 'dnd': return <div className="w-3 h-3 bg-red-500 rounded-full"></div>;
      default: return <div className="w-3 h-3 bg-gray-400 rounded-full"></div>;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online': return '在线';
      case 'idle': return '离开';
      case 'dnd': return '勿扰';
      default: return '离线';
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          用户管理
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          管理Discord服务器成员，查看用户信息和活动状态
        </p>
      </div>

      {/* Sync Alert */}
      {showSyncAlert && (
        <div className="mb-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <div className="flex items-center">
            <icons.CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
            <span className="text-green-700 dark:text-green-300">Discord用户同步完成</span>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-3 flex-1">
            {/* Search */}
            <div className="relative">
              <icons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="搜索用户..."
                className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Status Filter */}
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">全部状态</option>
              <option value="online">在线</option>
              <option value="idle">离开</option>
              <option value="dnd">勿扰</option>
              <option value="offline">离线</option>
            </select>

            {/* Role Filter */}
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">全部角色</option>
              <option value="管理员">管理员</option>
              <option value="成员">成员</option>
            </select>
          </div>

          {/* Sync Button */}
          <button
            onClick={() => syncUsersMutation.mutate()}
            disabled={syncUsersMutation.isPending}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            {syncUsersMutation.isPending ? (
              <icons.Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <icons.RefreshCw className="h-4 w-4" />
            )}
            {syncUsersMutation.isPending ? '同步中...' : '同步Discord'}
          </button>
        </div>
      </div>

      {/* User Count */}
      <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
        显示 {filteredUsers.length} 个Discord用户 (共 {discordUsers.length} 个)
      </div>

      {/* User List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {usersLoading ? (
          <div className="flex items-center justify-center p-12">
            <icons.Loader2 className="h-8 w-8 animate-spin text-blue-600 mr-3" />
            <span className="text-gray-600 dark:text-gray-400">加载用户数据...</span>
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className="text-center p-12">
            <icons.Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {discordUsers.length === 0 ? '暂无Discord用户' : '没有符合条件的用户'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {discordUsers.length === 0 
                ? '请先同步Discord服务器用户数据' 
                : '尝试调整搜索条件或筛选设置'
              }
            </p>
            {discordUsers.length === 0 && (
              <button
                onClick={() => syncUsersMutation.mutate()}
                className="flex items-center gap-2 mx-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <icons.RefreshCw className="h-4 w-4" />
                同步Discord用户
              </button>
            )}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    用户
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    积分
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    加入时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredUsers.map((user: any) => (
                  <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                    <td className="px-6 py-4">
                      <input
                        type="checkbox"
                        checked={selectedUsers.includes(user.id)}
                        onChange={() => handleSelectUser(user.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-300 dark:bg-gray-600 mr-3">
                          {user.avatar ? (
                            <img src={user.avatar} alt={user.username} className="h-10 w-10 object-cover" />
                          ) : (
                            <div className="h-10 w-10 flex items-center justify-center text-gray-500 dark:text-gray-400 font-medium">
                              {user.username.charAt(0).toUpperCase()}
                            </div>
                          )}
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {user.username}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            Discord ID: {user.discordId}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        {getStatusIcon(user.status)}
                        <span className="ml-2 text-sm text-gray-900 dark:text-white">
                          {getStatusText(user.status)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900 dark:text-white">
                      {user.points || 0}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {new Date(user.joinDate).toLocaleDateString('zh-CN')}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <button className="text-blue-600 hover:text-blue-900 text-sm">
                          编辑
                        </button>
                        <button className="text-green-600 hover:text-green-900 text-sm">
                          消息
                        </button>
                        <button className="text-gray-600 hover:text-gray-900 text-sm">
                          详情
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Selected Actions */}
      {selectedUsers.length > 0 && (
        <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700 dark:text-blue-300">
              已选择 {selectedUsers.length} 个用户
            </span>
            <div className="flex items-center space-x-2">
              <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                批量消息
              </button>
              <button className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                导出选中
              </button>
              <button 
                onClick={() => setSelectedUsers([])}
                className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
              >
                取消选择
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;