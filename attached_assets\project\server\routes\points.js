import express from 'express';
import { db } from '../database/init.js';
import { addPoints, deductPoints } from '../utils/points.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Get points history
router.get('/history', async (req, res) => {
  try {
    const { page = 1, limit = 50, userId = '', type = '' } = req.query;
    const offset = (page - 1) * limit;

    let query = `
      SELECT ph.*, u.username 
      FROM points_history ph
      LEFT JOIN users u ON ph.user_id = u.id
      WHERE 1=1
    `;
    let params = [];

    if (userId) {
      query += ' AND ph.user_id = ?';
      params.push(userId);
    }

    if (type) {
      query += ' AND ph.type = ?';
      params.push(type);
    }

    query += ' ORDER BY ph.created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    const history = await db.allAsync(query, params);

    res.json({
      success: true,
      data: history
    });
  } catch (error) {
    logger.error('Error getting points history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get points history'
    });
  }
});

// Add points to user
router.post('/add', async (req, res) => {
  try {
    const { userId, amount, reason } = req.body;

    if (!userId || !amount || !reason) {
      return res.status(400).json({
        success: false,
        message: 'User ID, amount, and reason are required'
      });
    }

    const success = await addPoints(userId, parseInt(amount), reason, 'admin');

    if (success) {
      res.json({
        success: true,
        message: 'Points added successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to add points'
      });
    }
  } catch (error) {
    logger.error('Error adding points:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add points'
    });
  }
});

// Deduct points from user
router.post('/deduct', async (req, res) => {
  try {
    const { userId, amount, reason } = req.body;

    if (!userId || !amount || !reason) {
      return res.status(400).json({
        success: false,
        message: 'User ID, amount, and reason are required'
      });
    }

    const success = await deductPoints(userId, parseInt(amount), reason);

    if (success) {
      res.json({
        success: true,
        message: 'Points deducted successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to deduct points'
      });
    }
  } catch (error) {
    logger.error('Error deducting points:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

export default router;