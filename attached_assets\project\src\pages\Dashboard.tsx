import React, { useState } from 'react';
import StatCard from '../components/StatCard';
import * as icons from 'lucide-react';
import { useData } from '../contexts/DataContext';

interface DashboardProps {
  onTabChange?: (tabId: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onTabChange }) => {
  const [isSyncModalOpen, setIsSyncModalOpen] = useState(false);
  const [isPointsModalOpen, setIsPointsModalOpen] = useState(false);
  const [isLotteryModalOpen, setIsLotteryModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isRestartModalOpen, setIsRestartModalOpen] = useState(false);
  const [isRestartingBot, setIsRestartingBot] = useState(false);
  const [restartComplete, setRestartComplete] = useState(false);
  const [syncProgress, setSyncProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);

  // 使用统一的数据上下文
  const { botStatus, serverData, stats, refreshData, isConnected } = useData();

  const handleSyncUsers = () => {
    setIsSyncModalOpen(true);
    setIsLoading(true);
    setSyncProgress(0);
    
    const interval = setInterval(() => {
      setSyncProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          setOperationResult({ type: 'success', message: '用户数据同步成功！已同步 1,234 个用户' });
          setTimeout(() => setOperationResult(null), 3000);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const handleDistributePoints = () => {
    setIsPointsModalOpen(true);
  };

  const handleCreateLottery = () => {
    setIsLotteryModalOpen(true);
  };

  const handleSystemSettings = () => {
    setIsSettingsModalOpen(true);
  };

  const handleRestartBot = () => {
    setIsRestartModalOpen(true);
  };

  const confirmRestartBot = () => {
    setIsRestartingBot(true);
    setRestartComplete(false);
    
    // 模拟重启过程
    setTimeout(() => {
      setIsRestartingBot(false);
      setRestartComplete(true);
    }, 3000);
  };

  const handleFormSubmit = (type: string, formData?: any) => {
    // 模拟表单提交
    setTimeout(() => {
      setOperationResult({
        type: 'success',
        message: type === 'points' ? '积分发放成功！' : 
                type === 'lottery' ? '抽奖活动创建成功！' : 
                type === 'settings' ? '设置保存成功！' : '操作完成！'
      });
      
      // 3秒后关闭结果提示
      setTimeout(() => {
        setOperationResult(null);
        if (type === 'points') setIsPointsModalOpen(false);
        if (type === 'lottery') setIsLotteryModalOpen(false);
        if (type === 'settings') setIsSettingsModalOpen(false);
      }, 2000);
    }, 1000);
  };

  const handleViewAllActivities = () => {
    if (onTabChange) {
      onTabChange('logs');
    }
  };

  // Bot 状态相关函数
  const getBotStatusText = () => {
    if (!isConnected) return 'WebSocket 断开';
    if (botStatus.error) return 'Bot 错误';
    if (botStatus.ready) return 'Bot 在线';
    if (botStatus.connected) return 'Bot 连接中';
    return 'Bot 离线';
  };

  const getBotStatusColor = () => {
    if (!isConnected) return 'text-gray-600 dark:text-gray-400';
    if (botStatus.error) return 'text-red-600 dark:text-red-400';
    if (botStatus.ready) return 'text-green-600 dark:text-green-400';
    if (botStatus.connected) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">控制台总览</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">实时监控 Bot 运行状态和服务器数据</p>
        </div>
        <div className="flex items-center space-x-3">
          <div className={`px-4 py-2 rounded-lg font-medium text-sm shadow-lg ${
            isConnected 
              ? 'bg-yellow-400 text-black shadow-yellow-400/25' 
              : 'bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400'
          }`}>
            {isConnected ? '实时数据' : '连接断开'}
          </div>
        </div>
      </div>

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧统计卡片区域 */}
        <div className="col-span-12 lg:col-span-8 space-y-6">
          {/* 主要统计数据 - 使用实时数据 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="服务器人数"
              value={serverData.memberCount.toLocaleString()}
              icon="Users"
              color="yellow"
              trend={{ value: 12, isPositive: true }}
              subtitle="本月新增 148 人"
            />
            <StatCard
              title="活跃用户"
              value={serverData.onlineCount.toLocaleString()}
              icon="UserCheck"
              color="black"
              trend={{ value: 8, isPositive: true }}
              subtitle={`活跃率 ${serverData.memberCount > 0 ? ((serverData.onlineCount / serverData.memberCount) * 100).toFixed(1) : 0}%`}
            />
            <StatCard
              title="用户总数"
              value={stats.userCount.toLocaleString()}
              icon="MessageCircle"
              color="gray"
              trend={{ value: 23, isPositive: true }}
              subtitle={`最近活跃 ${stats.recentActiveUsers || 0} 人`}
            />
            <StatCard
              title="今日签到"
              value={stats.todayCheckins.toLocaleString()}
              icon="Calendar"
              color="green"
              trend={{ value: 5, isPositive: false }}
              subtitle={`签到率 ${stats.userCount > 0 ? ((stats.todayCheckins / stats.userCount) * 100).toFixed(1) : 0}%`}
            />
          </div>

          {/* 活跃消息图表 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">过去 7 天消息活跃度</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">实时数据更新</p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full shadow-lg shadow-yellow-400/50"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">消息数量</span>
                </div>
                <div className={`px-3 py-1 rounded-lg text-xs font-medium ${
                  isConnected 
                    ? 'bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-300' 
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                }`}>
                  {isConnected ? '实时' : '离线'}
                </div>
              </div>
            </div>
            
            <div className="h-64 flex items-end justify-between space-x-3">
              {[
                { day: '周一', value: 2100, percentage: 65 },
                { day: '周二', value: 2450, percentage: 78 },
                { day: '周三', value: 1890, percentage: 56 },
                { day: '周四', value: 3200, percentage: 100 },
                { day: '周五', value: 2750, percentage: 89 },
                { day: '周六', value: 3100, percentage: 93 },
                { day: '周日', value: 2847, percentage: 85 }
              ].map((item, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div className="w-full bg-gray-100 dark:bg-gray-700 rounded-t-lg relative overflow-hidden" style={{ height: '200px' }}>
                    <div 
                      className="absolute bottom-0 w-full bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t-lg transition-all duration-1000 ease-out shadow-lg"
                      style={{ height: `${item.percentage}%` }}
                    />
                    <div className="absolute top-2 left-1/2 transform -translate-x-1/2 text-xs font-bold text-gray-700 dark:text-gray-300">
                      {item.value}
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 text-center mt-2 font-medium">
                    {item.day}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 最近活动 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">最近活动</h3>
              <button 
                onClick={handleViewAllActivities}
                className="text-yellow-600 hover:text-yellow-700 text-sm font-medium transition-colors"
              >
                查看全部
              </button>
            </div>
            <div className="space-y-4">
              {[
                { action: '用户 @张三 完成签到', time: '2分钟前', icon: 'Calendar', color: 'yellow' },
                { action: '积分奖励已发放给 @李四', time: '5分钟前', icon: 'Star', color: 'green' },
                { action: '新用户 @王五 加入服务器', time: '10分钟前', icon: 'UserPlus', color: 'black' },
                { action: '自动化规则已触发', time: '15分钟前', icon: 'Workflow', color: 'gray' },
              ].map((activity, index) => {
                const Icon = icons[activity.icon as keyof typeof icons] as React.ComponentType<any>;
                const colorClasses = {
                  yellow: 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400',
                  green: 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400',
                  black: 'bg-gray-100 dark:bg-gray-900/20 text-gray-700 dark:text-gray-400',
                  gray: 'bg-gray-100 dark:bg-gray-900/20 text-gray-700 dark:text-gray-400'
                };
                return (
                  <div key={index} className="flex items-center space-x-4 p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${colorClasses[activity.color as keyof typeof colorClasses]}`}>
                      <Icon className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-900 dark:text-white font-medium">{activity.action}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{activity.time}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* 右侧信息面板 */}
        <div className="col-span-12 lg:col-span-4 space-y-6">
          {/* Bot 状态面板 - 使用实时数据 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Activity className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">Bot 状态</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">实时监控</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">连接状态</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    botStatus.ready ? 'bg-green-400 animate-pulse' : 
                    botStatus.connected ? 'bg-yellow-400' : 
                    'bg-red-400'
                  }`}></div>
                  <span className={`text-sm font-bold ${getBotStatusColor()}`}>
                    {getBotStatusText()}
                  </span>
                </div>
              </div>
              
              {botStatus.ready && (
                <>
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">延迟</span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">{botStatus.latency}ms</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">运行时间</span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">
                      {botStatus.uptime ? Math.floor(botStatus.uptime / (1000 * 60 * 60)) + '小时' : '未知'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">服务器数</span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">{botStatus.guilds}</span>
                  </div>
                </>
              )}
              
              <button 
                onClick={handleRestartBot}
                className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 px-4 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-bold shadow-lg shadow-yellow-500/25"
              >
                重启 Bot
              </button>
            </div>
          </div>

          {/* 今日排行榜 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl flex items-center justify-center shadow-lg shadow-gray-900/25">
                <icons.Trophy className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">今日排行</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">活跃度排名</p>
              </div>
            </div>
            
            <div className="space-y-3">
              {[
                { user: '张三', points: 1250, rank: 1, change: '+2' },
                { user: '李四', points: 890, rank: 2, change: '0' },
                { user: '王五', points: 750, rank: 3, change: '-1' },
                { user: '赵六', points: 600, rank: 4, change: '+1' },
              ].map((item) => (
                <div key={item.user} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm ${
                      item.rank === 1 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25' : 
                      item.rank === 2 ? 'bg-gradient-to-br from-gray-400 to-gray-500' : 
                      item.rank === 3 ? 'bg-gradient-to-br from-orange-400 to-orange-500' : 'bg-gradient-to-br from-gray-600 to-gray-700'
                    }`}>
                      {item.rank}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{item.user}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-900 dark:text-white">{item.points}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">积分</div>
                  </div>
                  <div className={`text-xs font-bold px-2 py-1 rounded ${
                    item.change.startsWith('+') ? 'text-green-600 bg-green-100 dark:bg-green-900/20' :
                    item.change.startsWith('-') ? 'text-red-600 bg-red-100 dark:bg-red-900/20' :
                    'text-gray-600 bg-gray-100 dark:bg-gray-900/20'
                  }`}>
                    {item.change}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 快速操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快速操作</h3>
            <div className="grid grid-cols-2 gap-3">
              <button 
                onClick={handleSyncUsers}
                className="flex flex-col items-center p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.UserPlus className="w-6 h-6 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600 mb-2" />
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">同步用户</span>
              </button>
              <button 
                onClick={handleDistributePoints}
                className="flex flex-col items-center p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Star className="w-6 h-6 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600 mb-2" />
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">发放积分</span>
              </button>
              <button 
                onClick={handleCreateLottery}
                className="flex flex-col items-center p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Gift className="w-6 h-6 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600 mb-2" />
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">创建抽奖</span>
              </button>
              <button 
                onClick={handleSystemSettings}
                className="flex flex-col items-center p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Settings className="w-6 h-6 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600 mb-2" />
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">系统设置</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 同步用户模态框 */}
      {isSyncModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">同步用户数据</h3>
              {!isLoading && (
                <button 
                  onClick={() => setIsSyncModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <icons.X className="w-5 h-5" />
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  {isLoading ? (
                    <icons.RefreshCw className="w-8 h-8 text-black animate-spin" />
                  ) : (
                    <icons.CheckCircle className="w-8 h-8 text-black" />
                  )}
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {isLoading ? '正在同步用户数据...' : '同步完成！'}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {isLoading ? '请稍候，正在从Discord服务器获取最新用户信息' : '成功同步了 1,234 个用户的数据'}
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">同步进度</span>
                  <span className="font-medium text-gray-900 dark:text-white">{syncProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${syncProgress}%` }}
                  />
                </div>
              </div>
              
              {!isLoading && (
                <button
                  onClick={() => setIsSyncModalOpen(false)}
                  className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-medium"
                >
                  完成
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 发放积分模态框 */}
      {isPointsModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">批量发放积分</h3>
              <button 
                onClick={() => setIsPointsModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('积分') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">发放成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">已成功向所有用户发放积分</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('points'); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">发放对象</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>所有用户</option>
                    <option>活跃用户</option>
                    <option>新用户</option>
                    <option>指定头衔用户</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">积分数量</label>
                  <input
                    type="number"
                    placeholder="100"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">发放原因</label>
                  <textarea
                    placeholder="节日福利发放..."
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsPointsModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium"
                  >
                    发放积分
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 创建抽奖模态框 */}
      {isLotteryModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">快速创建抽奖</h3>
              <button 
                onClick={() => setIsLotteryModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('抽奖') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">创建成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">抽奖活动已成功创建并启动</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('lottery'); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">抽奖标题</label>
                  <input
                    type="text"
                    placeholder="新年福利抽奖"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">奖品</label>
                  <input
                    type="text"
                    placeholder="Discord Nitro × 3"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">参与条件</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>免费参与</option>
                    <option>消耗 50 积分</option>
                    <option>消耗 100 积分</option>
                    <option>指定头衔用户</option>
                  </select>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsLotteryModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium"
                  >
                    创建抽奖
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 系统设置模态框 */}
      {isSettingsModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">快速设置</h3>
              <button 
                onClick={() => setIsSettingsModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('设置') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">保存成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">系统设置已更新</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('settings'); }}>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">自动签到提醒</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">每日提醒用户签到</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">新用户欢迎</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">自动欢迎新加入用户</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">积分通知</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">积分变动时发送通知</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" />
                      <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                    </label>
                  </div>
                  
                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-medium"
                  >
                    保存设置
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 重启Bot确认模态框 */}
      {isRestartModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                {isRestartingBot ? '正在重启...' : restartComplete ? '重启完成' : '确认重启'}
              </h3>
              {!isRestartingBot && (
                <button 
                  onClick={() => {
                    setIsRestartModalOpen(false);
                    setRestartComplete(false);
                  }}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <icons.X className="w-5 h-5" />
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                  isRestartingBot ? 'bg-gradient-to-br from-orange-400 to-orange-500' :
                  restartComplete ? 'bg-gradient-to-br from-green-400 to-green-500' :
                  'bg-gradient-to-br from-orange-400 to-orange-500'
                }`}>
                  {isRestartingBot ? (
                    <icons.RefreshCw className="w-8 h-8 text-white animate-spin" />
                  ) : restartComplete ? (
                    <icons.CheckCircle className="w-8 h-8 text-white" />
                  ) : (
                    <icons.AlertTriangle className="w-8 h-8 text-white" />
                  )}
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {isRestartingBot ? '正在重启 Discord Bot' : 
                   restartComplete ? 'Bot 重启成功！' : 
                   '重启 Discord Bot'}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {isRestartingBot ? 'Bot正在重启中，请稍候...' :
                   restartComplete ? 'Bot已成功重启并重新连接到Discord服务器' :
                   '重启过程中Bot将暂时离线，预计需要30秒。确定要继续吗？'}
                </p>
              </div>
              
              {!isRestartingBot && !restartComplete && (
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    onClick={() => setIsRestartModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={confirmRestartBot}
                    className="px-6 py-3 bg-gradient-to-r from-orange-400 to-orange-500 text-white rounded-xl hover:from-orange-500 hover:to-orange-600 transition-all duration-200 shadow-lg shadow-orange-500/25 font-medium"
                  >
                    确认重启
                  </button>
                </div>
              )}
              
              {restartComplete && (
                <button
                  onClick={() => {
                    setIsRestartModalOpen(false);
                    setRestartComplete(false);
                  }}
                  className="w-full bg-gradient-to-r from-green-400 to-green-500 text-white py-3 rounded-xl hover:from-green-500 hover:to-green-600 transition-all duration-200 font-medium"
                >
                  完成
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;