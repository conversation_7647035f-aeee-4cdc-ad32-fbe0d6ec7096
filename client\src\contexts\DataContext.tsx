import React, { createContext, useContext, useState, useEffect } from 'react';
import { useProductionWebSocket } from '../hooks/useProductionWebSocket';

interface BotStatus {
  connected: boolean;
  ready: boolean;
  latency: number;
  uptime: number;
  guilds: number;
  users: number;
  error: string | null;
}

interface Channel {
  id: string;
  name: string;
  type: number;
  category: string;
}

interface Role {
  id: string;
  name: string;
  color: string;
  memberCount: number;
}

interface ServerData {
  memberCount: number;
  onlineCount: number;
  activeChannels: number;
  channels: Channel[];
  roles: Role[];
}

interface Stats {
  userCount: number;
  todayCheckins: number;
  recentActiveUsers: number;
  totalPoints?: number;
  timestamp: string;
}

interface DataContextType {
  botStatus: BotStatus;
  serverData: ServerData;
  stats: Stats;
  isConnected: boolean;
  refreshData: () => void;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [botStatus, setBotStatus] = useState<BotStatus>({
    connected: false,
    ready: false,
    latency: 0,
    uptime: 0,
    guilds: 0,
    users: 0,
    error: null
  });

  const [serverData, setServerData] = useState<ServerData>({
    memberCount: 0,
    onlineCount: 0,
    activeChannels: 0,
    channels: [],
    roles: []
  });

  const [stats, setStats] = useState<Stats>({
    userCount: 0,
    todayCheckins: 0,
    recentActiveUsers: 0,
    timestamp: new Date().toISOString()
  });

  // 使用生产环境就绪的 WebSocket 连接
  const { isConnected, lastMessage, isProduction } = useProductionWebSocket();

  // Handle WebSocket messages
  useEffect(() => {
    if (!lastMessage) return;

    console.log('📥 Processing WebSocket message:', lastMessage.type, lastMessage.data);

    try {
      switch (lastMessage.type) {
        case 'botStatus':
          if (lastMessage.data) {
            setBotStatus(prev => ({ ...prev, ...lastMessage.data }));
          }
          break;
        
        case 'serverData':
          if (lastMessage.data) {
            setServerData(prev => ({ ...prev, ...lastMessage.data }));
          }
          break;
        
        case 'stats':
          if (lastMessage.data) {
            setStats(prev => ({ ...prev, ...lastMessage.data }));
          }
          break;
        
        case 'activity':
          // Handle activity updates - could be used for real-time activity feed
          console.log('📊 New activity:', lastMessage.data);
          break;
          
        default:
          // 忽略未知消息类型
          break;
      }
    } catch (error) {
      console.error('Error processing WebSocket message:', error);
    }
  }, [lastMessage]);

  const refreshData = () => {
    // In production mode, trigger a manual data refresh
    if (isProduction) {
      console.log('🔄 Manual data refresh triggered in production mode');
      // Force refresh by clearing last message timestamp
      setStats({ ...stats, timestamp: new Date().toISOString() });
    } else {
      // Development mode with WebSocket
      console.log('🔄 Manual data refresh triggered in development mode');
    }
  };

  return (
    <DataContext.Provider value={{
      botStatus,
      serverData,
      stats,
      isConnected,
      refreshData
    }}>
      {children}
    </DataContext.Provider>
  );
};

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};