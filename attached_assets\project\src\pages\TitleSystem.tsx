import React, { useState } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';

const TitleSystem: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isBatchModalOpen, setIsBatchModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isUsersModalOpen, setIsUsersModalOpen] = useState(false);
  const [selectedTitle, setSelectedTitle] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const [exportProgress, setExportProgress] = useState(0);
  const { showNotification } = useNotification();

  const titles = [
    { id: '1', name: '新手', minPoints: 0, maxPoints: 99, color: '#9CA3AF', users: 45, notification: '欢迎来到我们的社区！', benefits: ['基础权限'], level: 1 },
    { id: '2', name: '初级成员', minPoints: 100, maxPoints: 499, color: '#10B981', users: 128, notification: '恭喜您成为初级成员！', benefits: ['基础权限', '发送图片'], level: 2 },
    { id: '3', name: '活跃成员', minPoints: 500, maxPoints: 999, color: '#3B82F6', users: 89, notification: '您的活跃得到了认可！', benefits: ['基础权限', '发送图片', '语音聊天'], level: 3 },
    { id: '4', name: '高级成员', minPoints: 1000, maxPoints: 2499, color: '#8B5CF6', users: 56, notification: '恭喜晋升为高级成员！', benefits: ['基础权限', '发送图片', '语音聊天', '创建频道'], level: 4 },
    { id: '5', name: '核心成员', minPoints: 2500, maxPoints: 4999, color: '#F59E0B', users: 23, notification: '您已成为社区核心成员！', benefits: ['基础权限', '发送图片', '语音聊天', '创建频道', '管理消息'], level: 5 },
    { id: '6', name: '传奇成员', minPoints: 5000, maxPoints: 999999, color: '#D6F36F', users: 12, notification: '传奇之路，由您开启！', benefits: ['所有权限'], level: 6 },
  ];

  const titleUsers = [
    { id: '1', username: '张三', avatar: '', joinDate: '2024-01-15', points: 5250, title: '传奇成员', status: 'online' },
    { id: '2', username: '李四', avatar: '', joinDate: '2024-02-03', points: 3200, title: '核心成员', status: 'idle' },
    { id: '3', username: '王五', avatar: '', joinDate: '2024-02-20', points: 2340, title: '核心成员', status: 'offline' },
    { id: '4', username: '赵六', avatar: '', joinDate: '2024-01-28', points: 1567, title: '高级成员', status: 'online' },
    { id: '5', username: '钱七', avatar: '', joinDate: '2024-03-01', points: 890, title: '活跃成员', status: 'idle' },
    { id: '6', username: '孙八', avatar: '', joinDate: '2024-02-15', points: 450, title: '初级成员', status: 'online' },
    { id: '7', username: '周九', avatar: '', joinDate: '2024-03-05', points: 50, title: '新手', status: 'offline' },
  ];

  const handleEditTitle = (title: any) => {
    setSelectedTitle(title);
    setIsEditModalOpen(true);
  };

  const handleViewTitleUsers = (title: any) => {
    setSelectedTitle(title);
    setIsUsersModalOpen(true);
  };

  const handleFormSubmit = (type: string) => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setOperationResult({
        type: 'success',
        message: type === 'create' ? '头衔创建成功！' : 
                type === 'edit' ? '头衔更新成功！' : 
                type === 'batch' ? '批量晋升完成！' : 
                type === 'settings' ? '头衔设置已保存！' : '操作完成！'
      });
      
      setTimeout(() => {
        setOperationResult(null);
        if (type === 'create') setIsModalOpen(false);
        if (type === 'edit') setIsEditModalOpen(false);
        if (type === 'batch') setIsBatchModalOpen(false);
        if (type === 'settings') setIsSettingsModalOpen(false);
      }, 2000);
    }, 1500);
  };

  const handleExport = () => {
    setIsExportModalOpen(true);
    setExportProgress(0);
    setIsLoading(true);
    
    const interval = setInterval(() => {
      setExportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          return 100;
        }
        return prev + 20;
      });
    }, 300);
  };

  const handleDeleteTitle = (title: any) => {
    if (confirm(`确定要删除头衔 "${title.name}" 吗？`)) {
      showNotification(`头衔 "${title.name}" 已成功删除！`);
    }
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">头衔系统</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">管理用户头衔等级和晋升条件</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setIsModalOpen(true)}
            className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-yellow-500/25 font-medium"
          >
            <icons.Plus className="w-4 h-4" />
            <span>新增头衔</span>
          </button>
        </div>
      </div>

      {/* 操作结果提示 */}
      {operationResult && (
        <div className={`p-4 rounded-xl border ${
          operationResult.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {operationResult.type === 'success' ? (
              <icons.CheckCircle className="w-5 h-5" />
            ) : (
              <icons.AlertCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{operationResult.message}</span>
          </div>
        </div>
      )}

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧统计区域 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 头衔统计卡片 */}
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Crown className="w-6 h-6 text-black font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">头衔总数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{titles.length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" style={{ width: '100%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">系统完整度 100%</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                  <icons.TrendingUp className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">本周晋升</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">28</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" style={{ width: '70%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">比上周增长 15%</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl flex items-center justify-center shadow-lg shadow-gray-900/25">
                  <icons.Award className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">平均积分</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">1,247</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-gray-800 to-gray-900 h-2 rounded-full" style={{ width: '62%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">服务器平均水平</p>
            </div>
          </div>

          {/* 头衔分布图 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">头衔分布</h3>
            <div className="grid grid-cols-2 gap-4">
              {titles.map((title) => (
                <div key={title.id} className="text-center">
                  <div 
                    className="w-16 h-16 rounded-2xl mx-auto mb-2 flex items-center justify-center text-white font-bold text-lg shadow-lg"
                    style={{ backgroundColor: title.color, color: title.color === '#D6F36F' ? '#000' : '#fff' }}
                  >
                    {title.users}
                  </div>
                  <div className="text-xs font-medium text-gray-900 dark:text-white">{title.name}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{((title.users / titles.reduce((sum, t) => sum + t.users, 0)) * 100).toFixed(1)}%</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 中间主要内容区域 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          {/* 头衔列表 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">头衔列表</h3>
              <div className="space-y-6">
                {titles.map((title, index) => (
                  <div key={title.id} className="border border-gray-200 dark:border-gray-600 rounded-xl p-6 hover:border-yellow-400 dark:hover:border-yellow-500 transition-colors">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-sm bg-gray-600 dark:bg-gray-500">
                            {title.level}
                          </div>
                          <div
                            className="px-4 py-2 rounded-xl font-bold text-sm shadow-lg"
                            style={{ 
                              backgroundColor: title.color, 
                              color: title.color === '#D6F36F' ? '#000' : '#fff' 
                            }}
                          >
                            {title.name}
                          </div>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {title.minPoints} - {title.maxPoints === 999999 ? '∞' : title.maxPoints} 积分
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => handleViewTitleUsers(title)}
                          className="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors flex items-center space-x-1"
                        >
                          <icons.Users className="w-4 h-4" />
                          <span>{title.users} 位用户</span>
                        </button>
                        <div className="flex items-center space-x-2">
                          <button 
                            onClick={() => handleEditTitle(title)}
                            className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300 transition-colors p-2 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
                            title="编辑头衔"
                          >
                            <icons.Edit className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => {
                              showNotification('头衔复制成功！');
                            }}
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20" 
                            title="复制头衔"
                          >
                            <icons.Copy className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => handleDeleteTitle(title)}
                            className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20" 
                            title="删除头衔"
                          >
                            <icons.Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        <strong>晋升通知：</strong>{title.notification}
                      </div>
                      
                      <div>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">权限福利：</span>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {title.benefits.map((benefit, benefitIndex) => (
                            <span key={benefitIndex} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400">
                              {benefit}
                            </span>
                          ))}
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                        <div className="flex items-center justify-between text-sm mb-2">
                          <span className="text-gray-600 dark:text-gray-400">积分进度示例</span>
                          <span className="text-gray-900 dark:text-white font-medium">
                            {Math.min(title.minPoints + 200, title.maxPoints)} / {title.maxPoints === 999999 ? '∞' : title.maxPoints}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-3">
                          <div 
                            className="h-3 rounded-full shadow-sm"
                            style={{ 
                              backgroundColor: title.color,
                              width: title.maxPoints === 999999 ? '60%' : `${Math.min(((title.minPoints + 200) / title.maxPoints) * 100, 100)}%`
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧信息面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 晋升记录 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.TrendingUp className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">最近晋升</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">实时更新</p>
              </div>
            </div>
            
            <div className="space-y-4">
              {[
                { user: '张三', from: '活跃成员', to: '高级成员', time: '2分钟前', points: 1000 },
                { user: '李四', from: '初级成员', to: '活跃成员', time: '1小时前', points: 500 },
                { user: '王五', from: '新手', to: '初级成员', time: '3小时前', points: 100 },
                { user: '赵六', from: '高级成员', to: '核心成员', time: '昨天', points: 2500 },
              ].map((record, index) => (
                <div key={index} className="p-3 border border-gray-200 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-gray-600 to-gray-700 rounded-lg flex items-center justify-center shadow-lg">
                      <span className="text-white font-bold text-xs">
                        {record.user.charAt(0)}
                      </span>
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{record.user}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{record.time}</div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 ml-11">
                    {record.from} → {record.to}
                  </div>
                  <div className="text-xs text-yellow-600 dark:text-yellow-400 ml-11 font-medium">
                    达到 {record.points} 积分
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 头衔进度统计 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">晋升趋势</h3>
            <div className="h-32 flex items-end justify-between space-x-2">
              {[15, 22, 18, 28, 25, 32, 28].map((value, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div 
                    className="w-full bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t transition-all duration-500"
                    style={{ height: `${(value / 35) * 100}%` }}
                  />
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    {['一', '二', '三', '四', '五', '六', '日'][index]}
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">168</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">本周总晋升次数</div>
            </div>
          </div>

          {/* 快速操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快速操作</h3>
            <div className="space-y-3">
              <button 
                onClick={() => setIsBatchModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Zap className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">批量晋升</span>
              </button>
              <button 
                onClick={handleExport}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Download className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">导出数据</span>
              </button>
              <button 
                onClick={() => setIsSettingsModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Settings className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">头衔设置</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 查看头衔用户模态框 */}
      {isUsersModalOpen && selectedTitle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                <span 
                  className="inline-block px-3 py-1 rounded-lg mr-2 text-sm font-bold"
                  style={{ 
                    backgroundColor: selectedTitle.color, 
                    color: selectedTitle.color === '#D6F36F' ? '#000' : '#fff' 
                  }}
                >
                  {selectedTitle.name}
                </span>
                用户列表
              </h3>
              <button 
                onClick={() => setIsUsersModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              {titleUsers
                .filter(user => user.title === selectedTitle.name)
                .map(user => (
                  <div key={user.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <div className="w-12 h-12 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center shadow-lg">
                          <span className="text-white font-bold text-sm">
                            {user.username.charAt(0)}
                          </span>
                        </div>
                        <div className={`absolute -bottom-1 -right-1 w-4 h-4 ${
                          user.status === 'online' ? 'bg-green-400' :
                          user.status === 'idle' ? 'bg-yellow-400' : 'bg-gray-400'
                        } rounded-full border-2 border-white dark:border-gray-800`}></div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white">{user.username}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">加入于 {user.joinDate}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-yellow-600 dark:text-yellow-400">{user.points}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">积分</div>
                    </div>
                  </div>
                ))}
            </div>
            
            <div className="flex justify-end space-x-3 pt-6 mt-4 border-t border-gray-200 dark:border-gray-600">
              <button
                onClick={() => setIsUsersModalOpen(false)}
                className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 新增头衔模态框 */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">新增头衔</h3>
              <button 
                onClick={() => setIsModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('创建') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">创建成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">新头衔已成功创建</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('create'); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔名称</label>
                  <input
                    type="text"
                    placeholder="请输入头衔名称"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最低积分</label>
                    <input
                      type="number"
                      placeholder="0"
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最高积分</label>
                    <input
                      type="number"
                      placeholder="999"
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔颜色</label>
                  <div className="flex items-center space-x-3">
                    <input
                      type="color"
                      className="w-12 h-12 border border-gray-300 dark:border-gray-600 rounded-xl"
                      defaultValue="#D6F36F"
                    />
                    <input
                      type="text"
                      placeholder="#D6F36F"
                      className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">晋升通知</label>
                  <textarea
                    placeholder="恭喜您晋升为新头衔！"
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '创建中...' : '创建头衔'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 编辑头衔模态框 */}
      {isEditModalOpen && selectedTitle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">编辑头衔 - {selectedTitle.name}</h3>
              <button 
                onClick={() => setIsEditModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('更新') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">更新成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">头衔信息已更新</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('edit'); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔名称</label>
                  <input
                    type="text"
                    defaultValue={selectedTitle.name}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最低积分</label>
                    <input
                      type="number"
                      defaultValue={selectedTitle.minPoints}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最高积分</label>
                    <input
                      type="number"
                      defaultValue={selectedTitle.maxPoints === 999999 ? '' : selectedTitle.maxPoints}
                      placeholder="留空表示无上限"
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔颜色</label>
                  <div className="flex items-center space-x-3">
                    <input
                      type="color"
                      className="w-12 h-12 border border-gray-300 dark:border-gray-600 rounded-xl"
                      defaultValue={selectedTitle.color}
                    />
                    <input
                      type="text"
                      defaultValue={selectedTitle.color}
                      className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">晋升通知</label>
                  <textarea
                    defaultValue={selectedTitle.notification}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsEditModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '保存中...' : '保存更改'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 批量晋升模态框 */}
      {isBatchModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">批量晋升</h3>
              <button 
                onClick={() => setIsBatchModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('批量') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">批量晋升完成！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">已成功为 23 位用户进行头衔晋升</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('batch'); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">晋升条件</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>根据积分自动晋升</option>
                    <option>指定用户晋升</option>
                    <option>批量降级</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">目标头衔</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>自动匹配</option>
                    {titles.map(title => (
                      <option key={title.id} value={title.id}>{title.name}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">晋升范围</label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500" defaultChecked />
                      <span className="text-sm text-gray-700 dark:text-gray-300">所有符合条件的用户</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">仅活跃用户</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">发送晋升通知</span>
                    </label>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsBatchModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '处理中...' : '开始晋升'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 导出数据模态框 */}
      {isExportModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">导出头衔数据</h3>
              {!isLoading && (
                <button 
                  onClick={() => setIsExportModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <icons.X className="w-5 h-5" />
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  {isLoading ? (
                    <icons.Download className="w-8 h-8 text-black animate-pulse" />
                  ) : (
                    <icons.CheckCircle className="w-8 h-8 text-black" />
                  )}
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {isLoading ? '正在导出数据...' : '导出完成！'}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {isLoading ? '请稍候，正在生成头衔数据文件' : '头衔数据已成功导出为Excel文件'}
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">导出进度</span>
                  <span className="font-medium text-gray-900 dark:text-white">{exportProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${exportProgress}%` }}
                  />
                </div>
              </div>
              
              {!isLoading && (
                <button
                  onClick={() => {
                    setIsExportModalOpen(false);
                    showNotification('头衔数据导出成功！');
                  }}
                  className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-medium"
                >
                  完成
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 头衔设置模态框 */}
      {isSettingsModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">头衔系统设置</h3>
              <button 
                onClick={() => setIsSettingsModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('设置') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">设置已保存！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">头衔系统配置已更新</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('settings'); }}>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">自动晋升</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">根据积分自动晋升头衔</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">晋升通知</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">头衔变化时发送通知</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white">头衔显示</div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">在用户名旁显示头衔</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                    </label>
                  </div>
                  
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-medium disabled:opacity-50 flex items-center justify-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '保存中...' : '保存设置'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TitleSystem;