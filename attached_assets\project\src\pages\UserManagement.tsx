import React, { useState } from 'react';
import * as icons from 'lucide-react';

const UserManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);
  const [isKickModalOpen, setIsKickModalOpen] = useState(false);
  const [isBatchInviteModalOpen, setIsBatchInviteModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isPermissionModalOpen, setIsPermissionModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const [syncProgress, setSyncProgress] = useState(0);
  const [exportProgress, setExportProgress] = useState(0);

  const users = [
    { id: '1', username: '张三', avatar: '', joinDate: '2024-01-15', points: 1250, title: '活跃成员', status: 'online', roles: ['成员', '活跃用户'], lastActive: '刚刚', messages: 2847, voiceTime: '12h 30m' },
    { id: '2', username: '李四', avatar: '', joinDate: '2024-02-03', points: 890, title: '初级成员', status: 'idle', roles: ['成员'], lastActive: '5分钟前', messages: 1456, voiceTime: '8h 15m' },
    { id: '3', username: '王五', avatar: '', joinDate: '2024-02-20', points: 2340, title: '高级成员', status: 'offline', roles: ['成员', '高级用户'], lastActive: '2小时前', messages: 3892, voiceTime: '25h 45m' },
    { id: '4', username: '赵六', avatar: '', joinDate: '2024-01-28', points: 567, title: '新手', status: 'online', roles: ['成员'], lastActive: '刚刚', messages: 892, voiceTime: '3h 20m' },
    { id: '5', username: '钱七', avatar: '', joinDate: '2024-03-01', points: 1890, title: '活跃成员', status: 'idle', roles: ['成员', '活跃用户'], lastActive: '15分钟前', messages: 2156, voiceTime: '18h 10m' },
    { id: '6', username: '孙八', avatar: '', joinDate: '2024-02-15', points: 3450, title: '核心成员', status: 'online', roles: ['成员', '核心用户', '管理员'], lastActive: '刚刚', messages: 5234, voiceTime: '45h 30m' },
  ];

  const filteredUsers = users.filter(user => 
    user.username.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-400';
      case 'idle': return 'bg-yellow-400';
      case 'offline': return 'bg-gray-400';
      default: return 'bg-gray-400';
    }
  };

  const getTitleColor = (title: string) => {
    switch (title) {
      case '核心成员': return 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-black';
      case '高级成员': return 'bg-gradient-to-r from-purple-500 to-purple-600 text-white';
      case '活跃成员': return 'bg-gradient-to-r from-blue-500 to-blue-600 text-white';
      case '初级成员': return 'bg-gradient-to-r from-green-500 to-green-600 text-white';
      default: return 'bg-gradient-to-r from-gray-500 to-gray-600 text-white';
    }
  };

  const handleEditUser = (user: any) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  };

  const handleSendMessage = (user: any) => {
    setSelectedUser(user);
    setIsMessageModalOpen(true);
  };

  const handleKickUser = (user: any) => {
    setSelectedUser(user);
    setIsKickModalOpen(true);
  };

  const handleSyncUsers = () => {
    setIsLoading(true);
    setSyncProgress(0);
    
    const interval = setInterval(() => {
      setSyncProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          setOperationResult({ type: 'success', message: '用户数据同步成功！已同步 1,234 个用户' });
          setTimeout(() => setOperationResult(null), 3000);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const handleFormSubmit = (type: string) => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setOperationResult({
        type: 'success',
        message: type === 'edit' ? '用户信息更新成功！' : 
                type === 'message' ? '私信发送成功！' : 
                type === 'kick' ? '用户已被踢出服务器！' : 
                type === 'batch' ? '批量邀请已发送！' : 
                type === 'permission' ? '权限设置已更新！' : '操作完成！'
      });
      
      setTimeout(() => {
        setOperationResult(null);
        if (type === 'edit') setIsEditModalOpen(false);
        if (type === 'message') setIsMessageModalOpen(false);
        if (type === 'kick') setIsKickModalOpen(false);
        if (type === 'batch') setIsBatchInviteModalOpen(false);
        if (type === 'permission') setIsPermissionModalOpen(false);
      }, 2000);
    }, 1500);
  };

  const handleExport = () => {
    setIsExportModalOpen(true);
    setExportProgress(0);
    setIsLoading(true);
    
    const interval = setInterval(() => {
      setExportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          return 100;
        }
        return prev + 15;
      });
    }, 300);
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">用户管理</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">管理服务器成员信息和状态</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={handleSyncUsers}
            className="bg-gray-800 hover:bg-gray-900 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-gray-900/25"
          >
            <icons.RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span>{isLoading ? '同步中...' : '同步用户'}</span>
          </button>
        </div>
      </div>

      {/* 操作结果提示 */}
      {operationResult && (
        <div className={`p-4 rounded-xl border ${
          operationResult.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {operationResult.type === 'success' ? (
              <icons.CheckCircle className="w-5 h-5" />
            ) : (
              <icons.AlertCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{operationResult.message}</span>
          </div>
        </div>
      )}

      {/* 同步进度条 */}
      {isLoading && syncProgress > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-sm mb-2">
            <span className="text-gray-600 dark:text-gray-400">同步进度</span>
            <span className="font-medium text-gray-900 dark:text-white">{syncProgress}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${syncProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧统计区域 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 用户统计卡片 */}
          <div className="space-y-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Users className="w-6 h-6 text-black font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">总用户数</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{users.length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" style={{ width: '85%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">服务器容量 85%</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                  <icons.UserCheck className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">在线用户</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{users.filter(u => u.status === 'online').length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" style={{ width: '60%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">活跃度 60%</p>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl flex items-center justify-center shadow-lg shadow-gray-900/25">
                  <icons.Crown className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">高级成员</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{users.filter(u => u.title.includes('高级') || u.title.includes('核心')).length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-gray-800 to-gray-900 h-2 rounded-full" style={{ width: '35%' }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">VIP 比例 35%</p>
            </div>
          </div>

          {/* 用户分布图 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">头衔分布</h3>
            <div className="space-y-3">
              {[
                { title: '核心成员', count: 1, percentage: 15 },
                { title: '高级成员', count: 1, percentage: 15 },
                { title: '活跃成员', count: 2, percentage: 35 },
                { title: '初级成员', count: 1, percentage: 15 },
                { title: '新手', count: 1, percentage: 20 }
              ].map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">{item.title}</span>
                    <span className="font-medium text-gray-900 dark:text-white">{item.count} 人</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${item.percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 中间主要内容区域 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          {/* 搜索和筛选 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <icons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="搜索用户名..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex space-x-2">
                <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有状态</option>
                  <option>在线</option>
                  <option>离开</option>
                  <option>离线</option>
                </select>
                <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有头衔</option>
                  <option>新手</option>
                  <option>初级成员</option>
                  <option>活跃成员</option>
                  <option>高级成员</option>
                  <option>核心成员</option>
                </select>
              </div>
            </div>
          </div>

          {/* 用户列表 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">用户列表</h3>
                <span className="text-sm text-gray-500 dark:text-gray-400">共 {filteredUsers.length} 位用户</span>
              </div>
              <div className="space-y-4">
                {filteredUsers.map((user) => (
                  <div key={user.id} className="border border-gray-200 dark:border-gray-600 rounded-xl p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="relative">
                          <div className="w-12 h-12 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center shadow-lg">
                            <span className="text-white font-bold text-sm">
                              {user.username.charAt(0)}
                            </span>
                          </div>
                          <div className={`absolute -bottom-1 -right-1 w-4 h-4 ${getStatusColor(user.status)} rounded-full border-2 border-white dark:border-gray-800`}></div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <div className="font-bold text-gray-900 dark:text-white">{user.username}</div>
                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-bold ${getTitleColor(user.title)} shadow-lg`}>
                              {user.title}
                            </span>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              user.status === 'online' ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400' :
                              user.status === 'idle' ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-400' :
                              'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400'
                            }`}>
                              {user.status === 'online' ? '在线' : user.status === 'idle' ? '离开' : '离线'}
                            </span>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-500 dark:text-gray-400 mb-2">
                            <span>积分: <span className="font-bold text-yellow-600 dark:text-yellow-400">{user.points}</span></span>
                            <span>消息: <span className="font-bold text-gray-900 dark:text-white">{user.messages}</span></span>
                            <span>语音: <span className="font-bold text-gray-900 dark:text-white">{user.voiceTime}</span></span>
                            <span>最后活跃: <span className="font-bold text-gray-900 dark:text-white">{user.lastActive}</span></span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {user.roles.map((role, index) => (
                              <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-900/20 text-gray-700 dark:text-gray-400">
                                {role}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button 
                          onClick={() => handleEditUser(user)}
                          className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300 transition-colors p-2 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
                          title="编辑用户"
                        >
                          <icons.Edit className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => handleSendMessage(user)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20" 
                          title="发送私信"
                        >
                          <icons.MessageCircle className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => handleKickUser(user)}
                          className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20" 
                          title="踢出用户"
                        >
                          <icons.UserX className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 右侧信息面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 活跃度排行 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.TrendingUp className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">活跃排行</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">本周数据</p>
              </div>
            </div>
            
            <div className="space-y-3">
              {users.slice(0, 5).map((user, index) => (
                <div key={user.id} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm ${
                      index === 0 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25' : 
                      index === 1 ? 'bg-gradient-to-br from-gray-400 to-gray-500' : 
                      index === 2 ? 'bg-gradient-to-br from-orange-400 to-orange-500' : 'bg-gradient-to-br from-gray-600 to-gray-700'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{user.username}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{user.messages} 消息</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-900 dark:text-white">{user.points}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">积分</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 用户增长趋势 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">7日增长</h3>
            <div className="h-32 flex items-end justify-between space-x-2">
              {[12, 8, 15, 23, 19, 28, 25].map((value, index) => (
                <div key={index} className="flex-1 flex flex-col items-center">
                  <div 
                    className="w-full bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t transition-all duration-500"
                    style={{ height: `${(value / 30) * 100}%` }}
                  />
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    {['一', '二', '三', '四', '五', '六', '日'][index]}
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">+130</div>
              <div className="text-xs text-gray-500 dark:text-gray-400">本周新增用户</div>
            </div>
          </div>

          {/* 快速操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快速操作</h3>
            <div className="space-y-3">
              <button 
                onClick={() => setIsBatchInviteModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.UserPlus className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">批量邀请</span>
              </button>
              <button 
                onClick={handleExport}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Download className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">导出用户</span>
              </button>
              <button 
                onClick={() => setIsPermissionModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Shield className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">权限管理</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 编辑用户模态框 */}
      {isEditModalOpen && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">编辑用户 - {selectedUser.username}</h3>
              <button 
                onClick={() => setIsEditModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('更新') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">更新成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">用户信息已成功更新</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('edit'); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">用户名</label>
                  <input
                    type="text"
                    defaultValue={selectedUser.username}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">积分</label>
                  <input
                    type="number"
                    defaultValue={selectedUser.points}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔</label>
                  <select 
                    defaultValue={selectedUser.title}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option>新手</option>
                    <option>初级成员</option>
                    <option>活跃成员</option>
                    <option>高级成员</option>
                    <option>核心成员</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">角色</label>
                  <div className="space-y-2">
                    {['成员', '活跃用户', '高级用户', '核心用户', '管理员', 'VIP'].map(role => (
                      <label key={role} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          defaultChecked={selectedUser.roles.includes(role)}
                          className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500"
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300">{role}</span>
                      </label>
                    ))}
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsEditModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '保存中...' : '保存更改'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 发送私信模态框 */}
      {isMessageModalOpen && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">发送私信给 {selectedUser.username}</h3>
              <button 
                onClick={() => setIsMessageModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('私信') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">发送成功！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">私信已成功发送给 {selectedUser.username}</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('message'); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">消息标题</label>
                  <input
                    type="text"
                    placeholder="请输入消息标题"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">消息内容</label>
                  <textarea
                    rows={5}
                    placeholder="请输入要发送的消息内容..."
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">消息类型</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>普通消息</option>
                    <option>重要通知</option>
                    <option>警告消息</option>
                    <option>系统消息</option>
                  </select>
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsMessageModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg shadow-blue-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '发送中...' : '发送私信'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 踢出用户确认模态框 */}
      {isKickModalOpen && selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">踢出用户确认</h3>
              <button 
                onClick={() => setIsKickModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('踢出') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">操作完成！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">{selectedUser.username} 已被踢出服务器</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <icons.AlertTriangle className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">确认踢出用户</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    您确定要踢出用户 <span className="font-bold">{selectedUser.username}</span> 吗？此操作不可撤销。
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">踢出原因</label>
                  <textarea
                    rows={3}
                    placeholder="请输入踢出原因（可选）"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    onClick={() => setIsKickModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={() => handleFormSubmit('kick')}
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-lg shadow-red-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '踢出中...' : '确认踢出'}</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 批量邀请模态框 */}
      {isBatchInviteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">批量邀请用户</h3>
              <button 
                onClick={() => setIsBatchInviteModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('邀请') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">邀请已发送！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">批量邀请链接已生成并发送</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('batch'); }} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">邀请数量</label>
                  <input
                    type="number"
                    placeholder="50"
                    min="1"
                    max="100"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">邀请链接有效期</label>
                  <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>30分钟</option>
                    <option>1小时</option>
                    <option>6小时</option>
                    <option>12小时</option>
                    <option>1天</option>
                    <option>7天</option>
                    <option>永不过期</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">邀请消息</label>
                  <textarea
                    rows={3}
                    placeholder="欢迎加入我们的Discord服务器！"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setIsBatchInviteModalOpen(false)}
                    className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '生成中...' : '生成邀请'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}

      {/* 导出用户模态框 */}
      {isExportModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">导出用户数据</h3>
              {!isLoading && (
                <button 
                  onClick={() => setIsExportModalOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <icons.X className="w-5 h-5" />
                </button>
              )}
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  {isLoading ? (
                    <icons.Download className="w-8 h-8 text-black animate-pulse" />
                  ) : (
                    <icons.CheckCircle className="w-8 h-8 text-black" />
                  )}
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {isLoading ? '正在导出数据...' : '导出完成！'}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {isLoading ? '请稍候，正在生成用户数据文件' : '用户数据已成功导出为Excel文件'}
                </p>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">导出进度</span>
                  <span className="font-medium text-gray-900 dark:text-white">{exportProgress}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${exportProgress}%` }}
                  />
                </div>
              </div>
              
              {!isLoading && (
                <button
                  onClick={() => setIsExportModalOpen(false)}
                  className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-medium"
                >
                  完成
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 权限管理模态框 */}
      {isPermissionModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">权限管理</h3>
              <button 
                onClick={() => setIsPermissionModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            {operationResult && operationResult.message.includes('权限') ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <icons.CheckCircle className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">设置已保存！</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">权限配置已更新</p>
              </div>
            ) : (
              <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit('permission'); }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">默认角色权限</label>
                    <div className="space-y-2">
                      {['发送消息', '发送图片', '使用表情', '语音聊天', '创建邀请'].map(permission => (
                        <label key={permission} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            defaultChecked={true}
                            className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500"
                          />
                          <span className="text-sm text-gray-700 dark:text-gray-300">{permission}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">管理员权限</label>
                    <div className="space-y-2">
                      {['管理消息', '踢出用户', '封禁用户', '管理频道', '管理角色'].map(permission => (
                        <label key={permission} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            defaultChecked={false}
                            className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500"
                          />
                          <span className="text-sm text-gray-700 dark:text-gray-300">{permission}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-medium disabled:opacity-50 flex items-center justify-center space-x-2"
                  >
                    {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                    <span>{isLoading ? '保存中...' : '保存权限'}</span>
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;