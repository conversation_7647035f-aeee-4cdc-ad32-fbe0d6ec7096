{"name": "discord-bot-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "vite", "dev:backend": "node server/index.js", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "node server/index.js"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.0", "lucide-react": "^0.263.1", "express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.13.0", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "winston": "^3.11.0", "sqlite3": "^5.1.6", "sqlite": "^5.1.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^8.2.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}