import React, { useState, useEffect } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';
import { useData } from '../contexts/DataContext';

const Settings: React.FC = () => {
  const [isTokenModalOpen, setIsTokenModalOpen] = useState(false);
  const [isBotTestModalOpen, setIsBotTestModalOpen] = useState(false);
  const [isBackupModalOpen, setIsBackupModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [botToken, setBotToken] = useState('');
  const [testResult, setTestResult] = useState<any>(null);
  const [settings, setSettings] = useState<any>({});
  const { showNotification } = useNotification();
  const { botStatus, serverData, isConnected, refreshData } = useData();

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/settings');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Server returned non-JSON response');
      }
      
      const data = await response.json();
      if (data.success) {
        setSettings(data.data);
        setBotToken(data.data.bot_token || '');
      } else {
        throw new Error(data.message || 'Failed to load settings');
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      showNotification('加载设置失败，请检查服务器连接', 'error');
    }
  };

  const handleTestToken = async () => {
    if (!botToken.trim()) {
      showNotification('请输入 Bot Token', 'error');
      return;
    }

    setIsLoading(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/bot/test-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: botToken }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Server returned non-JSON response');
      }

      const data = await response.json();
      setTestResult(data);

      if (data.success) {
        showNotification('Token 验证成功！', 'success');
      } else {
        showNotification('Token 验证失败！', 'error');
      }
    } catch (error) {
      console.error('Error testing token:', error);
      showNotification('测试失败，请检查服务器连接', 'error');
      setTestResult({
        success: false,
        message: '服务器连接失败'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveToken = async () => {
    if (!botToken.trim()) {
      showNotification('请输入 Bot Token', 'error');
      return;
    }

    setIsLoading(true);

    try {
      // Save token to settings
      const saveResponse = await fetch('/api/settings/bot_token', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ value: botToken }),
      });

      if (!saveResponse.ok) {
        throw new Error('Failed to save token');
      }

      // Start the bot
      const startResponse = await fetch('/api/bot/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: botToken }),
      });

      if (!startResponse.ok) {
        throw new Error(`HTTP error! status: ${startResponse.status}`);
      }

      const contentType = startResponse.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Server returned non-JSON response');
      }

      const data = await startResponse.json();

      if (data.success) {
        showNotification('Bot 启动成功！正在同步数据...', 'success');
        setIsTokenModalOpen(false);
        // Refresh data after a short delay
        setTimeout(() => {
          refreshData();
        }, 2000);
      } else {
        showNotification(`Bot 启动失败: ${data.message}`, 'error');
      }
    } catch (error) {
      console.error('Error saving token:', error);
      showNotification('保存失败，请检查服务器连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestartBot = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/bot/restart', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Server returned non-JSON response');
      }

      const data = await response.json();

      if (data.success) {
        showNotification('Bot 重启成功！', 'success');
        setTimeout(() => {
          refreshData();
        }, 3000);
      } else {
        showNotification(`Bot 重启失败: ${data.message}`, 'error');
      }
    } catch (error) {
      console.error('Error restarting bot:', error);
      showNotification('重启失败，请检查服务器连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStopBot = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/bot/stop', {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('Server returned non-JSON response');
      }

      const data = await response.json();

      if (data.success) {
        showNotification('Bot 已停止', 'success');
        refreshData();
      } else {
        showNotification(`Bot 停止失败: ${data.message}`, 'error');
      }
    } catch (error) {
      console.error('Error stopping bot:', error);
      showNotification('停止失败，请检查服务器连接', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const getBotStatusColor = () => {
    if (botStatus.error) return 'text-red-600 dark:text-red-400';
    if (botStatus.ready) return 'text-green-600 dark:text-green-400';
    if (botStatus.connected) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-gray-600 dark:text-gray-400';
  };

  const getBotStatusText = () => {
    if (botStatus.error) return `错误: ${botStatus.error}`;
    if (botStatus.ready) return '运行中';
    if (botStatus.connected) return '连接中';
    return '未连接';
  };

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white">设置中心</h2>
        <p className="text-gray-600 dark:text-gray-400 mt-1">配置系统参数和 Discord Bot 设置</p>
      </div>

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧系统状态 */}
        <div className="col-span-12 lg:col-span-4 space-y-6">
          {/* 系统状态卡片 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Activity className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">系统状态</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">实时监控</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">WebSocket 连接</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 ${isConnected ? 'bg-green-400' : 'bg-red-400'} rounded-full`}></div>
                  <span className={`text-sm font-bold ${isConnected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {isConnected ? '已连接' : '断开'}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">Discord Bot</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 ${botStatus.ready ? 'bg-green-400' : botStatus.connected ? 'bg-yellow-400' : 'bg-red-400'} rounded-full`}></div>
                  <span className={`text-sm font-bold ${getBotStatusColor()}`}>
                    {getBotStatusText()}
                  </span>
                </div>
              </div>
              
              {botStatus.ready && (
                <>
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">服务器数量</span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">{botStatus.guilds}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">成员总数</span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">{serverData.memberCount}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">在线用户</span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">{serverData.onlineCount}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <span className="text-sm text-gray-600 dark:text-gray-300 font-medium">延迟</span>
                    <span className="text-sm font-bold text-gray-900 dark:text-white">{botStatus.latency}ms</span>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* 快速操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Bot 控制</h3>
            <div className="space-y-3">
              <button 
                onClick={() => setIsTokenModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.Key className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">配置 Bot Token</span>
              </button>
              
              <button 
                onClick={() => setIsBotTestModalOpen(true)}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group"
              >
                <icons.TestTube className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">测试连接</span>
              </button>
              
              <button 
                onClick={handleRestartBot}
                disabled={isLoading}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-gray-200 dark:border-gray-600 hover:border-yellow-400 hover:bg-yellow-50 dark:hover:bg-yellow-900/10 transition-all duration-200 group disabled:opacity-50"
              >
                <icons.RefreshCw className={`w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-yellow-600 ${isLoading ? 'animate-spin' : ''}`} />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 group-hover:text-yellow-600">重启 Bot</span>
              </button>
              
              <button 
                onClick={handleStopBot}
                disabled={isLoading}
                className="w-full flex items-center space-x-3 p-3 rounded-xl border border-red-200 dark:border-red-600 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200 group disabled:opacity-50"
              >
                <icons.Square className="w-5 h-5" />
                <span className="text-sm font-medium">停止 Bot</span>
              </button>
            </div>
          </div>
        </div>

        {/* 中间设置区域 */}
        <div className="col-span-12 lg:col-span-8 space-y-6">
          {/* Bot 配置 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Discord Bot 配置</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bot 前缀</label>
                <input
                  type="text"
                  defaultValue={settings.bot_prefix || '!'}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">签到积分</label>
                <input
                  type="number"
                  defaultValue={settings.checkin_points || '10'}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">消息积分</label>
                <input
                  type="number"
                  defaultValue={settings.message_points || '1'}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">邀请积分</label>
                <input
                  type="number"
                  defaultValue={settings.invite_points || '50'}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
          </div>

          {/* 系统设置 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">系统设置</h3>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">自动备份</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">定期备份数据库</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked={settings.auto_backup === 'true'} />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">错误通知</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">发生错误时发送通知</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked={settings.error_notifications === 'true'} />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-xl">
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">调试模式</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">启用详细日志记录</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" className="sr-only peer" defaultChecked={settings.debug_mode === 'true'} />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-yellow-600"></div>
                </label>
              </div>
            </div>
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end">
            <button 
              onClick={() => showNotification('设置保存成功！')}
              className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-black px-6 py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium"
            >
              保存设置
            </button>
          </div>
        </div>
      </div>

      {/* Bot Token 配置模态框 */}
      {isTokenModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">配置 Discord Bot Token</h3>
              <button 
                onClick={() => setIsTokenModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Bot Token</label>
                <input
                  type="password"
                  value={botToken}
                  onChange={(e) => setBotToken(e.target.value)}
                  placeholder="请输入你的 Discord Bot Token"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  在 Discord Developer Portal 创建应用并获取 Bot Token
                </p>
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={handleTestToken}
                  disabled={isLoading}
                  className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
                >
                  {isLoading ? '测试中...' : '测试 Token'}
                </button>
                <button
                  onClick={handleSaveToken}
                  disabled={isLoading || !botToken.trim()}
                  className="flex-1 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50"
                >
                  {isLoading ? '保存中...' : '保存并启动'}
                </button>
              </div>
              
              {testResult && (
                <div className={`p-4 rounded-xl border ${
                  testResult.success 
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
                    : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
                }`}>
                  <div className="flex items-center space-x-2">
                    {testResult.success ? (
                      <icons.CheckCircle className="w-5 h-5" />
                    ) : (
                      <icons.AlertCircle className="w-5 h-5" />
                    )}
                    <span className="font-medium">
                      {testResult.success ? 'Token 验证成功！' : 'Token 验证失败'}
                    </span>
                  </div>
                  {testResult.success && testResult.data && (
                    <div className="mt-2 text-sm">
                      <p>Bot 名称: {testResult.data.username}</p>
                      <p>服务器数量: {testResult.data.guilds}</p>
                      <p>用户数量: {testResult.data.users}</p>
                    </div>
                  )}
                  {!testResult.success && testResult.message && (
                    <div className="mt-2 text-sm">
                      <p>{testResult.message}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Bot 测试模态框 */}
      {isBotTestModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">Bot 连接测试</h3>
              <button 
                onClick={() => setIsBotTestModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="text-center">
                <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                  botStatus.ready ? 'bg-gradient-to-br from-green-400 to-green-500' :
                  botStatus.connected ? 'bg-gradient-to-br from-yellow-400 to-yellow-500' :
                  'bg-gradient-to-br from-red-400 to-red-500'
                }`}>
                  {botStatus.ready ? (
                    <icons.CheckCircle className="w-8 h-8 text-white" />
                  ) : botStatus.connected ? (
                    <icons.Clock className="w-8 h-8 text-black" />
                  ) : (
                    <icons.AlertCircle className="w-8 h-8 text-white" />
                  )}
                </div>
                <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                  {getBotStatusText()}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {botStatus.ready ? 'Bot 运行正常，数据同步中' :
                   botStatus.connected ? 'Bot 正在连接中，请稍候' :
                   '请检查 Token 配置或网络连接'}
                </p>
              </div>
              
              {botStatus.ready && (
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">服务器数量:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{botStatus.guilds}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">成员总数:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{serverData.memberCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">在线用户:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{serverData.onlineCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">延迟:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{botStatus.latency}ms</span>
                  </div>
                </div>
              )}
              
              <button
                onClick={() => {
                  refreshData();
                  showNotification('数据已刷新！');
                }}
                className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black py-3 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 font-medium"
              >
                刷新数据
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Settings;