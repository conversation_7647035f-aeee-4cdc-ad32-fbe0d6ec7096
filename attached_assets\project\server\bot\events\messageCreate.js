import { logger } from '../../utils/logger.js';
import { db } from '../../database/init.js';
import { addPoints } from '../../utils/points.js';
import { getSetting } from '../../utils/settings.js';

export async function handleMessageCreate(message) {
  try {
    // Ignore bot messages
    if (message.author.bot) return;

    // Update user activity
    await db.runAsync(`
      UPDATE users 
      SET last_active = ?, message_count = message_count + 1
      WHERE id = ?
    `, [new Date().toISOString(), message.author.id]);

    // Add message points
    const messagePoints = parseInt(await getSetting('message_points')) || 1;
    await addPoints(message.author.id, messagePoints, '发送消息', 'earned');

    // Handle commands (if message starts with prefix)
    const prefix = await getSetting('bot_prefix') || '!';
    if (message.content.startsWith(prefix)) {
      const args = message.content.slice(prefix.length).trim().split(/ +/);
      const commandName = args.shift().toLowerCase();

      // Handle basic commands
      switch (commandName) {
        case '签到':
          await handleCheckin(message);
          break;
        case '积分':
          await handlePoints(message);
          break;
        case '排行榜':
          await handleLeaderboard(message);
          break;
        case '帮助':
          await handleHelp(message);
          break;
      }
    }
  } catch (error) {
    logger.error('Error handling message:', error);
  }
}

async function handleCheckin(message) {
  try {
    const userId = message.author.id;
    const today = new Date().toISOString().split('T')[0];

    // Check if already checked in today
    const existingCheckin = await db.getAsync(`
      SELECT * FROM checkin_records 
      WHERE user_id = ? AND checkin_date = ?
    `, [userId, today]);

    if (existingCheckin) {
      await message.reply('⏰ 您今天已经签到过了，明天再来吧！');
      return;
    }

    // Get user's current streak
    const user = await db.getAsync('SELECT checkin_streak FROM users WHERE id = ?', [userId]);
    const currentStreak = user?.checkin_streak || 0;
    const newStreak = currentStreak + 1;

    // Calculate points
    const basePoints = parseInt(await getSetting('checkin_points')) || 10;
    let bonusPoints = 0;
    
    if (newStreak >= 7) bonusPoints += 5;
    if (newStreak >= 30) bonusPoints += 15;

    const totalPoints = basePoints + bonusPoints;

    // Record checkin
    await db.runAsync(`
      INSERT INTO checkin_records (user_id, checkin_date, points_earned, streak_bonus)
      VALUES (?, ?, ?, ?)
    `, [userId, today, totalPoints, bonusPoints]);

    // Update user streak and points
    await db.runAsync(`
      UPDATE users 
      SET checkin_streak = ?, last_checkin = ?, points = points + ?
      WHERE id = ?
    `, [newStreak, today, totalPoints, userId]);

    // Send success message
    let replyMessage = `✅ ${message.author} 签到成功！获得 ${totalPoints} 积分，已连续签到 ${newStreak} 天！`;
    
    if (bonusPoints > 0) {
      replyMessage += `\n🎉 连续签到奖励：+${bonusPoints} 积分！`;
    }

    await message.reply(replyMessage);
  } catch (error) {
    logger.error('Error handling checkin:', error);
    await message.reply('❌ 签到失败，请稍后重试。');
  }
}

async function handlePoints(message) {
  try {
    const user = await db.getAsync(`
      SELECT points, title, checkin_streak FROM users WHERE id = ?
    `, [message.author.id]);

    if (!user) {
      await message.reply('❌ 未找到您的用户信息。');
      return;
    }

    await message.reply({
      embeds: [{
        title: '💰 您的积分信息',
        fields: [
          { name: '当前积分', value: user.points.toString(), inline: true },
          { name: '当前头衔', value: user.title, inline: true },
          { name: '连续签到', value: `${user.checkin_streak} 天`, inline: true }
        ],
        color: 0xD6F36F,
        thumbnail: {
          url: message.author.displayAvatarURL()
        }
      }]
    });
  } catch (error) {
    logger.error('Error handling points command:', error);
    await message.reply('❌ 查询积分失败，请稍后重试。');
  }
}

async function handleLeaderboard(message) {
  try {
    const topUsers = await db.allAsync(`
      SELECT username, points, title 
      FROM users 
      ORDER BY points DESC 
      LIMIT 10
    `);

    if (topUsers.length === 0) {
      await message.reply('📊 暂无排行榜数据。');
      return;
    }

    const leaderboardText = topUsers.map((user, index) => {
      const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
      return `${medal} **${user.username}** - ${user.points} 积分 (${user.title})`;
    }).join('\n');

    await message.reply({
      embeds: [{
        title: '🏆 积分排行榜',
        description: leaderboardText,
        color: 0xD6F36F
      }]
    });
  } catch (error) {
    logger.error('Error handling leaderboard command:', error);
    await message.reply('❌ 查询排行榜失败，请稍后重试。');
  }
}

async function handleHelp(message) {
  const prefix = await getSetting('bot_prefix') || '!';
  
  await message.reply({
    embeds: [{
      title: '🤖 Bot 帮助',
      description: '以下是可用的命令：',
      fields: [
        { name: `${prefix}签到`, value: '每日签到获取积分', inline: true },
        { name: `${prefix}积分`, value: '查看个人积分信息', inline: true },
        { name: `${prefix}排行榜`, value: '查看积分排行榜', inline: true },
        { name: `${prefix}帮助`, value: '显示此帮助信息', inline: true }
      ],
      color: 0xD6F36F,
      footer: {
        text: 'Discord Bot 管理系统'
      }
    }]
  });
}