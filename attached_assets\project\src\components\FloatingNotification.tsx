import React, { useEffect, useState } from 'react';
import * as icons from 'lucide-react';

interface FloatingNotificationProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  isVisible: boolean;
  onClose: () => void;
  duration?: number;
}

const FloatingNotification: React.FC<FloatingNotificationProps> = ({
  message,
  type = 'success',
  isVisible,
  onClose,
  duration = 3000
}) => {
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    if (isVisible) {
      setShouldRender(true);
      const timer = setTimeout(() => {
        onClose();
      }, duration);
      return () => clearTimeout(timer);
    } else {
      const timer = setTimeout(() => {
        setShouldRender(false);
      }, 300); // 等待动画完成
      return () => clearTimeout(timer);
    }
  }, [isVisible, onClose, duration]);

  if (!shouldRender) return null;

  const getTypeStyles = () => {
    switch (type) {
      case 'success':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400';
      case 'error':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400';
      case 'warning':
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-400';
      case 'info':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-400';
      default:
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <icons.CheckCircle className="w-5 h-5" />;
      case 'error':
        return <icons.AlertCircle className="w-5 h-5" />;
      case 'warning':
        return <icons.AlertTriangle className="w-5 h-5" />;
      case 'info':
        return <icons.Info className="w-5 h-5" />;
      default:
        return <icons.CheckCircle className="w-5 h-5" />;
    }
  };

  return (
    <div className="fixed top-4 left-4 right-4 z-50 flex justify-center pointer-events-none">
      <div 
        className={`
          ${getTypeStyles()} 
          p-4 rounded-xl shadow-lg max-w-md border backdrop-blur-sm pointer-events-auto
          transform transition-all duration-300 ease-out
          ${isVisible ? 'translate-y-0 opacity-100' : '-translate-y-4 opacity-0'}
        `}
      >
        <div className="flex items-center space-x-3">
          {getIcon()}
          <span className="font-medium flex-1">{message}</span>
          <button
            onClick={onClose}
            className="text-current opacity-70 hover:opacity-100 transition-opacity"
          >
            <icons.X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default FloatingNotification;