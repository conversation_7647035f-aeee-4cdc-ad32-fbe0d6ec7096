/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        yellow: {
          400: '#D6F36F',
          500: '#C8E85F',
          600: '#B8D84F'
        }
      },
      animation: {
        'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      boxShadow: {
        'yellow-glow': '0 0 20px rgba(214, 243, 111, 0.3)',
        'black-glow': '0 0 20px rgba(0, 0, 0, 0.3)',
      }
    },
  },
  plugins: [],
};