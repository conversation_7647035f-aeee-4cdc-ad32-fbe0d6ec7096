import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import * as icons from 'lucide-react';

const CheckinSystem: React.FC = () => {
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('records');
  const [refreshingChannels, setRefreshingChannels] = useState(false);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    basePoints: 10,
    streakEnabled: true,
    streakDays: 7,
    streakBonusPoints: 50,
    allowedChannels: [] as string[]
  });

  // WebSocket for real-time updates
  useEffect(() => {
    const handleWebSocketMessage = (event: MessageEvent) => {
      try {
        const message = JSON.parse(event.data);
        console.log('📨 WebSocket message:', message.type, message.data);
        
        if (message.type === 'checkinUpdate') {
          // Invalidate and refresh checkin-related queries
          queryClient.invalidateQueries({ queryKey: ['/api/checkins/records'] });
          queryClient.invalidateQueries({ queryKey: ['/api/checkins/stats'] });
          queryClient.invalidateQueries({ queryKey: ['/api/users'] });
          queryClient.invalidateQueries({ queryKey: ['/api/point-records'] });
          
          console.log('🔄 Auto-refreshed checkin data after Discord checkin');
        }
      } catch (error) {
        // Ignore non-JSON messages
      }
    };

    // Find existing WebSocket connection or create new one
    const connectWebSocket = () => {
      const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
      const wsUrl = `${protocol}//${window.location.host}/ws`;
      
      try {
        const ws = new WebSocket(wsUrl);
        ws.addEventListener('message', handleWebSocketMessage);
        
        ws.onopen = () => {
          console.log('📡 CheckinSystem WebSocket connected for real-time updates');
        };
        
        ws.onclose = () => {
          console.log('📡 CheckinSystem WebSocket disconnected');
          // Auto-reconnect after 3 seconds
          setTimeout(connectWebSocket, 3000);
        };
        
        return ws;
      } catch (error) {
        console.error('Failed to connect CheckinSystem WebSocket:', error);
        return null;
      }
    };

    const ws = connectWebSocket();
    
    return () => {
      if (ws) {
        ws.removeEventListener('message', handleWebSocketMessage);
        ws.close();
      }
    };
  }, [queryClient]);

  // Get checkin records
  const { data: checkinRecordsResponse, isLoading: recordsLoading } = useQuery({
    queryKey: ['/api/checkins/records'],
    queryFn: async () => {
      const response = await fetch('/api/checkins/records');
      if (!response.ok) throw new Error('Failed to fetch checkin records');
      return response.json();
    }
  });

  // Get checkin configs
  const { data: configResponse, isLoading: configLoading } = useQuery({
    queryKey: ['/api/checkins/config'],
    queryFn: async () => {
      const response = await fetch('/api/checkins/config');
      if (!response.ok) throw new Error('Failed to fetch checkin configs');
      return response.json();
    }
  });

  // Get Discord channels for selection
  const { data: serverStatsResponse, refetch: refetchChannels } = useQuery({
    queryKey: ['/api/discord/server-stats'],
    queryFn: async () => {
      const response = await fetch('/api/discord/server-stats');
      if (!response.ok) throw new Error('Failed to fetch server stats');
      return response.json();
    }
  });

  // Get checkin stats for leaderboard
  const { data: checkinStatsResponse } = useQuery({
    queryKey: ['/api/checkins/stats'],
    queryFn: async () => {
      const response = await fetch('/api/checkins/stats');
      if (!response.ok) throw new Error('Failed to fetch checkin stats');
      return response.json();
    }
  });

  // Get all users for leaderboard
  const { data: usersResponse } = useQuery({
    queryKey: ['/api/users'],
    queryFn: async () => {
      const response = await fetch('/api/users');
      if (!response.ok) throw new Error('Failed to fetch users');
      return response.json();
    }
  });

  const checkinRecords = checkinRecordsResponse?.data || [];
  const configs = configResponse?.data || [];
  const channels = serverStatsResponse?.data?.channels || [];
  const checkinStats = checkinStatsResponse?.data || {};
  const allUsers = usersResponse?.data || [];
  const leaderboardUsers = checkinStats.leaderboard || [];

  // Calculate dynamic progress percentages based on real data
  const todayCheckins = checkinStats.todayCheckins || 0;
  const totalUsers = allUsers.length || 1;
  const activeConfigs = configs.filter((config: any) => config.isActive).length;
  const totalConfigs = configs.length;
  
  // Calculate realistic progress percentages
  const checkinProgress = totalUsers > 0 ? Math.min((todayCheckins / totalUsers) * 100, 100) : 0;
  const userProgress = Math.min((totalUsers / 5) * 100, 100); // Scale to 5 users as 100%
  const configProgress = totalConfigs > 0 ? Math.min((activeConfigs / totalConfigs) * 100, 100) : 0;

  // Filter records based on search term
  const filteredRecords = checkinRecords.filter((record: any) => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      record.username?.toLowerCase().includes(searchLower) ||
      record.pointsEarned?.toString().includes(searchLower) ||
      record.consecutiveDays?.toString().includes(searchLower)
    );
  });

  // Create config mutation
  const createConfigMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch('/api/checkins/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error('Failed to create config');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/checkins/config'] });
      queryClient.invalidateQueries({ queryKey: ['/api/checkins/stats'] });
      setConfigDialogOpen(false);
      resetForm();
      setOperationResult({ type: 'success', message: '签到配置创建成功' });
    },
    onError: (error: any) => {
      setOperationResult({ type: 'error', message: `创建失败: ${error.message}` });
    }
  });

  // Update config mutation
  const updateConfigMutation = useMutation({
    mutationFn: async ({ id, ...data }: any) => {
      const response = await fetch(`/api/checkins/config/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error('Failed to update config');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/checkins/config'] });
      queryClient.invalidateQueries({ queryKey: ['/api/checkins/stats'] });
      setConfigDialogOpen(false);
      resetForm();
      setOperationResult({ type: 'success', message: '签到配置更新成功' });
    }
  });

  // Delete config mutation
  const deleteConfigMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/checkins/config/${id}`, {
        method: 'DELETE'
      });
      if (!response.ok) throw new Error('Failed to delete config');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/checkins/config'] });
      queryClient.invalidateQueries({ queryKey: ['/api/checkins/stats'] });
      setOperationResult({ type: 'success', message: '签到配置删除成功' });
    }
  });

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      basePoints: 10,
      streakEnabled: true,
      streakDays: 7,
      streakBonusPoints: 50,
      allowedChannels: []
    });
    setEditingConfig(null);
  };

  const openCreateDialog = () => {
    resetForm();
    setConfigDialogOpen(true);
  };

  const openEditDialog = (config: any) => {
    setEditingConfig(config);
    setFormData({
      name: config.name,
      description: config.description || '',
      basePoints: config.basePoints,
      streakEnabled: config.streakEnabled,
      streakDays: config.streakDays,
      streakBonusPoints: config.streakBonusPoints,
      allowedChannels: config.allowedChannels || []
    });
    setConfigDialogOpen(true);
  };

  const handleDeleteConfig = (configId: number, configName: string) => {
    if (window.confirm(`确定要删除配置"${configName}"吗？此操作不可撤销。`)) {
      deleteConfigMutation.mutate(configId);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingConfig) {
      updateConfigMutation.mutate({ id: editingConfig.id, ...formData });
    } else {
      createConfigMutation.mutate(formData);
    }
  };

  const handleChannelToggle = (channelId: string) => {
    setFormData(prev => ({
      ...prev,
      allowedChannels: prev.allowedChannels.includes(channelId)
        ? prev.allowedChannels.filter(id => id !== channelId)
        : [...prev.allowedChannels, channelId]
    }));
  };

  const handleRefreshChannels = async () => {
    setRefreshingChannels(true);
    try {
      await refetchChannels();
      setOperationResult({ type: 'success', message: 'Discord频道数据刷新成功' });
    } catch (error) {
      setOperationResult({ type: 'error', message: '刷新失败，请检查Discord连接' });
    } finally {
      setRefreshingChannels(false);
    }
  };

  const handleDeleteRecord = async (record: any) => {
    const totalPoints = record.pointsEarned + (record.bonusPoints || 0) + (record.randomBonus || 0);
    if (!confirm(`确定要删除 ${record.username} 在 ${new Date(record.checkinDate).toLocaleDateString()} 的签到记录吗？\n\n注意：这将会：\n• 删除签到记录\n• 扣除 ${totalPoints} 积分\n• 用户可以重新签到`)) {
      return;
    }

    try {
      const response = await fetch(`/api/checkins/records/${record.id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        const result = await response.json();
        setOperationResult({ type: 'success', message: '签到记录删除成功，积分已扣除，用户可以重新签到' });
        queryClient.invalidateQueries({ queryKey: ['/api/checkins/records'] });
        queryClient.invalidateQueries({ queryKey: ['/api/checkins/stats'] });
        queryClient.invalidateQueries({ queryKey: ['/api/users'] });
        queryClient.invalidateQueries({ queryKey: ['/api/point-records'] });
      } else {
        setOperationResult({ type: 'error', message: '删除签到记录失败' });
      }
    } catch (error) {
      setOperationResult({ type: 'error', message: '删除签到记录失败' });
    }

    setTimeout(() => setOperationResult(null), 3000);
  };

  // Clear operation result after 3 seconds
  useEffect(() => {
    if (operationResult) {
      const timer = setTimeout(() => setOperationResult(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [operationResult]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-2xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Calendar className="w-8 h-8 text-black font-bold" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">签到管理</h1>
                <p className="text-gray-500 dark:text-gray-400">管理用户签到记录和配置系统</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={openCreateDialog}
                className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-6 py-3 rounded-xl font-medium transition-all shadow-lg shadow-yellow-500/25 flex items-center space-x-2"
              >
                <icons.Plus className="h-5 w-5" />
                <span>新建签到</span>
              </button>
            </div>
          </div>
        </div>

        {/* Operation Result */}
        {operationResult && (
          <div className={`p-4 rounded-xl border mb-6 ${
            operationResult.type === 'success' 
              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
              : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
          }`}>
            <div className="flex items-center space-x-2">
              {operationResult.type === 'success' ? (
                <icons.CheckCircle className="w-5 h-5" />
              ) : (
                <icons.AlertCircle className="w-5 h-5" />
              )}
              <span className="font-medium">{operationResult.message}</span>
            </div>
          </div>
        )}

        {/* Main Grid Layout */}
        <div className="grid grid-cols-12 gap-6">
          {/* Left Sidebar - Statistics */}
          <div className="col-span-12 lg:col-span-3 space-y-6">
            {/* Today's Check-ins */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                  <icons.Calendar className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日签到</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{checkinStats.todayCheckins || 0}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full" style={{ width: `${checkinProgress}%` }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                {todayCheckins > 0 ? `签到率 ${checkinProgress.toFixed(1)}%` : '暂无签到记录'}
              </p>
            </div>

            {/* Total Users */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                  <icons.Users className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">活跃用户</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{allUsers.length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" style={{ width: `${userProgress}%` }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                {totalUsers > 0 ? `用户增长 ${userProgress.toFixed(1)}%` : '等待用户加入'}
              </p>
            </div>

            {/* Active Configs */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                  <icons.Settings className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">签到配置</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{configs.length}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-purple-400 to-purple-500 h-2 rounded-full" style={{ width: `${configProgress}%` }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                {totalConfigs > 0 ? `激活率 ${configProgress.toFixed(1)}%` : '暂无配置'}
              </p>
            </div>

            {/* Consecutive Check-ins */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Zap className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">最大连续</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{checkinStats.maxStreak || 0}天</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" style={{ width: `${Math.min((checkinStats.maxStreak || 0) * 10, 100)}%` }}></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                平均连续 {checkinStats.averageStreak || 0} 天
              </p>
            </div>

            {/* Top Check-in Users */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">签到排行</h3>
              <div className="space-y-3">
                {leaderboardUsers.slice(0, 5).map((user: any, index: number) => (
                  <div key={user.username} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm ${
                        index === 0 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25' : 
                        index === 1 ? 'bg-gradient-to-br from-gray-400 to-gray-500' : 
                        index === 2 ? 'bg-gradient-to-br from-orange-400 to-orange-500' : 'bg-gradient-to-br from-gray-600 to-gray-700'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white text-sm">{user.username}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">签到数: {user.totalCheckins}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">连续{user.consecutiveDays}天</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Center - Main Content */}
          <div className="col-span-12 lg:col-span-6 space-y-6">
            {/* Search and Filter */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <icons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="搜索用户或签到信息..."
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex space-x-2">
                  <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option>所有记录</option>
                    <option>今日签到</option>
                    <option>连续签到</option>
                  </select>
                  <button className="px-4 py-3 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                    <icons.Filter className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Check-in Records */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                      <icons.Calendar className="w-5 h-5 text-white font-bold" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white">签到记录</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">共 {filteredRecords.length} 条记录</p>
                    </div>
                  </div>
                  <button className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                    <icons.MoreHorizontal className="w-5 h-5" />
                  </button>
                </div>
              </div>
              
              <div className="max-h-96 overflow-y-auto">
                {recordsLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"></div>
                  </div>
                ) : filteredRecords.length === 0 ? (
                  <div className="text-center py-12">
                    <icons.Calendar className="mx-auto h-16 w-16 text-gray-300 dark:text-gray-600 mb-4" />
                    <p className="text-gray-500 dark:text-gray-400 text-lg font-medium">
                      {searchTerm ? '未找到匹配的签到记录' : '暂无签到记录'}
                    </p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
                      {searchTerm ? '请尝试其他搜索条件' : '用户签到后记录将显示在这里'}
                    </p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredRecords.map((record: any) => (
                      <div key={record.id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="relative">
                              {record.avatar ? (
                                <img 
                                  src={record.avatar} 
                                  alt={record.username || 'User'} 
                                  className="w-12 h-12 rounded-xl shadow-lg object-cover"
                                />
                              ) : (
                                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-xl flex items-center justify-center text-white font-bold shadow-lg shadow-green-500/25">
                                  {record.username?.charAt(0) || 'U'}
                                </div>
                              )}
                              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                                <icons.Check className="w-3 h-3 text-white" />
                              </div>
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-1">
                                <div className="font-bold text-gray-900 dark:text-white">{record.username}</div>
                                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-green-400 to-green-500 text-white shadow-lg shadow-green-500/25">
                                  +{record.pointsEarned} 积分
                                </span>
                              </div>
                              {record.consecutiveDays > 1 && (
                                <div className="flex items-center space-x-1 mb-1">
                                  <icons.Zap className="h-4 w-4 text-yellow-500" />
                                  <span className="text-sm text-yellow-600 dark:text-yellow-400 font-medium">连续 {record.consecutiveDays} 天</span>
                                </div>
                              )}
                              <div className="text-xs text-gray-500 dark:text-gray-500">
                                {new Date(record.checkinDate).toLocaleString('zh-CN')}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => handleDeleteRecord(record)}
                              className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                              title="删除签到记录"
                            >
                              <icons.Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Sidebar - Quick Actions */}
          <div className="col-span-12 lg:col-span-3 space-y-6">
            {/* Quick Actions */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快捷操作</h3>
              <div className="space-y-3">
                <button 
                  onClick={openCreateDialog}
                  className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-blue-500/25"
                >
                  <icons.Plus className="w-4 h-4" />
                  <span>新建配置</span>
                </button>
                <button 
                  onClick={handleRefreshChannels}
                  disabled={refreshingChannels}
                  className="w-full bg-gradient-to-r from-purple-500 to-purple-600 text-white px-4 py-3 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-purple-500/25 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <icons.RefreshCw className={`w-4 h-4 ${refreshingChannels ? 'animate-spin' : ''}`} />
                  <span>{refreshingChannels ? '刷新中...' : '刷新频道'}</span>
                </button>
                <button 
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-3 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-green-500/25"
                >
                  <icons.Download className="w-4 h-4" />
                  <span>导出记录</span>
                </button>

              </div>
            </div>

            {/* Active Configs */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Settings className="w-5 h-5 text-black font-bold" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">活跃配置</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">当前启用</p>
                </div>
              </div>
              
              <div className="space-y-3">
                {configs.slice(0, 3).map((config: any) => (
                  <div key={config.id} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm ${
                        config.isActive ? 'bg-gradient-to-br from-green-400 to-green-500' : 'bg-gradient-to-br from-gray-400 to-gray-500'
                      }`}>
                        <icons.Settings className="w-4 h-4" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white text-sm">{config.name}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">{config.basePoints} 积分</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => openEditDialog(config)}
                        className="text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                        title="编辑配置"
                      >
                        <icons.Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteConfig(config.id, config.name)}
                        disabled={deleteConfigMutation.isPending}
                        className="text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        title="删除配置"
                      >
                        <icons.Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
                {configs.length === 0 && (
                  <div className="text-center py-4">
                    <p className="text-gray-500 dark:text-gray-400 text-sm">暂无配置</p>
                  </div>
                )}
              </div>
            </div>

            {/* Consecutive Check-ins */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg shadow-orange-500/25">
                  <icons.Flame className="w-5 h-5 text-white font-bold" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">连续签到</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">活跃用户</p>
                </div>
              </div>
              
              <div className="space-y-3">
                {allUsers.slice(0, 3).map((user: any, index: number) => (
                  <div key={user.id} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                        {user.username?.charAt(0) || 'U'}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white text-sm">{user.username}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">连续 0 天</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <icons.Flame className="h-4 w-4 text-orange-500" />
                      <span className="text-sm font-bold text-orange-600 dark:text-orange-400">0</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Create/Edit Config Modal */}
        {configDialogOpen && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  {editingConfig ? '编辑签到配置' : '新建签到配置'}
                </h3>
                <button
                  onClick={() => setConfigDialogOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <icons.X className="h-5 w-5" />
                </button>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">配置名称</label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="例如：日常签到"
                      className="w-full bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl px-4 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-colors"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">单次签到积分</label>
                    <input
                      type="number"
                      min="1"
                      value={formData.basePoints}
                      onChange={(e) => setFormData(prev => ({ ...prev, basePoints: parseInt(e.target.value) || 10 }))}
                      className="w-full bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl px-4 py-3 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-colors"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">描述</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="简单描述这个签到配置的用途"
                    rows={3}
                    className="w-full bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl px-4 py-3 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-colors resize-none"
                  />
                </div>

                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">启用连续签到加成</label>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        用户连续签到达到指定天数时获得额外积分
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={formData.streakEnabled}
                        onChange={(e) => setFormData(prev => ({ ...prev, streakEnabled: e.target.checked }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-yellow-300 dark:peer-focus:ring-yellow-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-yellow-500"></div>
                    </label>
                  </div>

                  {formData.streakEnabled && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">连续天数</label>
                        <input
                          type="number"
                          min="2"
                          value={formData.streakDays}
                          onChange={(e) => setFormData(prev => ({ ...prev, streakDays: parseInt(e.target.value) || 7 }))}
                          className="w-full bg-white dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-colors"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">暴击积分</label>
                        <input
                          type="number"
                          min="1"
                          value={formData.streakBonusPoints}
                          onChange={(e) => setFormData(prev => ({ ...prev, streakBonusPoints: parseInt(e.target.value) || 50 }))}
                          className="w-full bg-white dark:bg-gray-600 border border-gray-200 dark:border-gray-500 rounded-lg px-3 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-colors"
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">作用频道</label>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                        选择允许签到的Discord频道 ({channels.length} 个可用频道)
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={handleRefreshChannels}
                      disabled={refreshingChannels}
                      className="bg-blue-100 dark:bg-blue-900/20 hover:bg-blue-200 dark:hover:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-colors border border-blue-200 dark:border-blue-800 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <icons.RefreshCw className={`h-4 w-4 ${refreshingChannels ? 'animate-spin' : ''}`} />
                      <span>{refreshingChannels ? '刷新中...' : '刷新频道'}</span>
                    </button>
                  </div>
                  <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600 rounded-xl p-4">
                    {channels.map((channel: any) => (
                      <div key={channel.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`channel-${channel.id}`}
                          checked={formData.allowedChannels.includes(channel.id)}
                          onChange={() => handleChannelToggle(channel.id)}
                          className="rounded border-gray-300 text-yellow-500 focus:ring-yellow-500"
                        />
                        <label htmlFor={`channel-${channel.id}`} className="text-sm flex items-center space-x-1 text-gray-700 dark:text-gray-300 cursor-pointer">
                          <icons.Hash className="h-3 w-3" />
                          <span className="truncate">{channel.name}</span>
                        </label>
                      </div>
                    ))}
                  </div>
                  {formData.allowedChannels.length === 0 && (
                    <p className="text-sm text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                      ⚠️ 至少选择一个频道才能创建签到配置
                    </p>
                  )}
                </div>

                <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                  <button
                    type="button"
                    onClick={() => setConfigDialogOpen(false)}
                    className="bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl font-medium transition-colors"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    disabled={createConfigMutation.isPending || updateConfigMutation.isPending || formData.allowedChannels.length === 0}
                    className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-6 py-3 rounded-xl font-medium transition-all shadow-lg shadow-yellow-500/25 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {editingConfig ? '更新配置' : '创建配置'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CheckinSystem;