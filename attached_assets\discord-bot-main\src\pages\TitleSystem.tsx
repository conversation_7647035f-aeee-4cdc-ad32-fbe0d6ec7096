import React, { useState } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';

const TitleSystem: React.FC = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedTitle, setSelectedTitle] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const { showNotification } = useNotification();

  const [titles, setTitles] = useState([
    {
      id: '1',
      name: '新手',
      description: '刚加入的新成员',
      minPoints: 0,
      maxPoints: 99,
      color: '#6B7280',
      icon: '🌱',
      isActive: true,
      userCount: 45
    },
    {
      id: '2',
      name: '初级成员',
      description: '开始活跃的成员',
      minPoints: 100,
      maxPoints: 499,
      color: '#10B981',
      icon: '🌿',
      isActive: true,
      userCount: 38
    },
    {
      id: '3',
      name: '活跃成员',
      description: '经常参与讨论的成员',
      minPoints: 500,
      maxPoints: 1499,
      color: '#3B82F6',
      icon: '⭐',
      isActive: true,
      userCount: 25
    },
    {
      id: '4',
      name: '高级成员',
      description: '社区的重要成员',
      minPoints: 1500,
      maxPoints: 4999,
      color: '#8B5CF6',
      icon: '💎',
      isActive: true,
      userCount: 19
    },
    {
      id: '5',
      name: '核心成员',
      description: '社区的核心力量',
      minPoints: 5000,
      maxPoints: null,
      color: '#F59E0B',
      icon: '👑',
      isActive: true,
      userCount: 8
    }
  ]);

  const handleCreateTitle = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setOperationResult({ type: 'success', message: '头衔创建成功！' });
      setIsCreateModalOpen(false);
      setTimeout(() => setOperationResult(null), 3000);
    }, 1500);
  };

  const handleEditTitle = (title: any) => {
    setSelectedTitle(title);
    setIsEditModalOpen(true);
  };

  const handleDeleteTitle = (title: any) => {
    setSelectedTitle(title);
    setIsDeleteModalOpen(true);
  };

  const handleToggleTitle = (titleId: string) => {
    setTitles(titles.map(title => 
      title.id === titleId 
        ? { ...title, isActive: !title.isActive }
        : title
    ));
    const title = titles.find(t => t.id === titleId);
    showNotification(`头衔 "${title?.name}" 已${title?.isActive ? '禁用' : '启用'}！`);
  };

  const handleFormSubmit = (type: string) => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setOperationResult({
        type: 'success',
        message: type === 'edit' ? '头衔更新成功！' : 
                type === 'delete' ? '头衔删除成功！' : '操作完成！'
      });
      
      setTimeout(() => {
        setOperationResult(null);
        if (type === 'edit') setIsEditModalOpen(false);
        if (type === 'delete') {
          setIsDeleteModalOpen(false);
          setTitles(titles.filter(title => title.id !== selectedTitle?.id));
        }
      }, 2000);
    }, 1500);
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">头衔系统</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">管理用户头衔和等级系统</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-yellow-500/25 font-medium"
          >
            <icons.Plus className="w-4 h-4" />
            <span>创建头衔</span>
          </button>
        </div>
      </div>

      {/* 操作结果提示 */}
      {operationResult && (
        <div className={`p-4 rounded-xl border ${
          operationResult.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {operationResult.type === 'success' ? (
              <icons.CheckCircle className="w-5 h-5" />
            ) : (
              <icons.AlertCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{operationResult.message}</span>
          </div>
        </div>
      )}

      {/* 头衔列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {titles.map((title) => (
          <div key={title.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all duration-200">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div 
                  className="w-12 h-12 rounded-xl flex items-center justify-center text-2xl"
                  style={{ backgroundColor: title.color }}
                >
                  {title.icon}
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">{title.name}</h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    title.isActive 
                      ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400' 
                      : 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400'
                  }`}>
                    {title.isActive ? '启用' : '禁用'}
                  </span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button 
                  onClick={() => handleToggleTitle(title.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    title.isActive 
                      ? 'text-orange-600 dark:text-orange-400 hover:text-orange-800 dark:hover:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20'
                      : 'text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20'
                  }`}
                  title={title.isActive ? '禁用头衔' : '启用头衔'}
                >
                  {title.isActive ? <icons.Pause className="w-4 h-4" /> : <icons.Play className="w-4 h-4" />}
                </button>
                <button 
                  onClick={() => handleEditTitle(title)}
                  className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 transition-colors p-2 rounded-lg hover:bg-yellow-50 dark:hover:bg-yellow-900/20" 
                  title="编辑头衔"
                >
                  <icons.Edit className="w-4 h-4" />
                </button>
                <button 
                  onClick={() => handleDeleteTitle(title)}
                  className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20" 
                  title="删除头衔"
                >
                  <icons.Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{title.description}</p>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">积分要求</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {title.minPoints} - {title.maxPoints || '∞'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-500 dark:text-gray-400">当前用户</span>
                <span className="font-medium text-gray-900 dark:text-white">{title.userCount} 人</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="h-2 rounded-full transition-all duration-500"
                  style={{ 
                    backgroundColor: title.color,
                    width: `${Math.min((title.userCount / 50) * 100, 100)}%`
                  }}
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 创建头衔模态框 */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">创建新头衔</h3>
              <button 
                onClick={() => setIsCreateModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={(e) => { e.preventDefault(); handleCreateTitle(); }} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔名称</label>
                <input
                  type="text"
                  placeholder="请输入头衔名称"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔描述</label>
                <textarea
                  placeholder="请描述这个头衔..."
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最低积分</label>
                  <input
                    type="number"
                    placeholder="0"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最高积分</label>
                  <input
                    type="number"
                    placeholder="999"
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔颜色</label>
                <input
                  type="color"
                  defaultValue="#D6F36F"
                  className="w-full h-12 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔图标</label>
                <input
                  type="text"
                  placeholder="🏆"
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsCreateModalOpen(false)}
                  className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                >
                  {isLoading && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                  <span>{isLoading ? '创建中...' : '创建头衔'}</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 编辑和删除模态框类似实现... */}
    </div>
  );
};

export default TitleSystem;