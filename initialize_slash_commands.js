const { storage } = require('./server/storage.js');

async function initializeSlashCommands() {
  try {
    console.log('Initializing default slash commands...');
    
    const defaultCommands = [
      {
        name: '/积分',
        description: '查看个人积分信息',
        category: 'points',
        isEnabled: true,
        parameters: null,
        response: '查看个人积分、排名和近期记录',
        cooldown: 3,
        permissions: 'everyone'
      },
      {
        name: '/签到',
        description: '每日签到获取积分',
        category: 'points',
        isEnabled: true,
        parameters: null,
        response: '每日签到获取积分奖励，连续签到获得额外奖励',
        cooldown: 86400, // 24 hours
        permissions: 'everyone'
      },
      {
        name: '/排行榜',
        description: '查看积分排行榜',
        category: 'points',
        isEnabled: true,
        parameters: null,
        response: '显示服务器积分排行榜前10名',
        cooldown: 5,
        permissions: 'everyone'
      },
      {
        name: '/帮助',
        description: '显示帮助信息',
        category: 'system',
        isEnabled: true,
        parameters: null,
        response: '显示所有可用指令和使用说明',
        cooldown: 0,
        permissions: 'everyone'
      }
    ];

    for (const command of defaultCommands) {
      try {
        await storage.createSlashCommand(command);
        console.log(`Created command: ${command.name}`);
      } catch (error) {
        if (error.message.includes('duplicate key')) {
          console.log(`Command ${command.name} already exists, skipping...`);
        } else {
          console.error(`Error creating command ${command.name}:`, error);
        }
      }
    }
    
    console.log('Slash commands initialization completed');
  } catch (error) {
    console.error('Error initializing slash commands:', error);
  }
}

initializeSlashCommands().then(() => process.exit(0));
