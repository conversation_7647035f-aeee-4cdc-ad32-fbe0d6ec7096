import { logger } from '../utils/logger.js';
import { getBotStatus, getClient } from '../bot/index.js';
import { db } from '../database/init.js';

let clients = new Set();

export function setupWebSocket(wss) {
  wss.on('connection', (ws) => {
    logger.info('New WebSocket client connected');
    clients.add(ws);

    // Send initial data
    sendInitialData(ws);

    // Handle client messages
    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message);
        handleClientMessage(ws, data);
      } catch (error) {
        logger.error('Error parsing WebSocket message:', error);
      }
    });

    // Handle client disconnect
    ws.on('close', () => {
      logger.info('WebSocket client disconnected');
      clients.delete(ws);
    });

    ws.on('error', (error) => {
      logger.error('WebSocket error:', error);
      clients.delete(ws);
    });
  });

  // Start real-time data broadcasting
  startDataBroadcasting();
}

async function sendInitialData(ws) {
  try {
    const botStatus = getBotStatus();
    const client = getClient();
    
    let serverData = {
      memberCount: 0,
      onlineCount: 0,
      channels: [],
      roles: []
    };

    if (client && client.isReady()) {
      const guild = client.guilds.cache.first();
      if (guild) {
        await guild.members.fetch();
        serverData = {
          memberCount: guild.memberCount,
          onlineCount: guild.members.cache.filter(member => 
            member.presence?.status === 'online' || 
            member.presence?.status === 'idle' || 
            member.presence?.status === 'dnd'
          ).size,
          channels: guild.channels.cache.map(channel => ({
            id: channel.id,
            name: channel.name,
            type: channel.type
          })),
          roles: guild.roles.cache.map(role => ({
            id: role.id,
            name: role.name,
            color: role.hexColor,
            memberCount: role.members.size
          }))
        };
      }
    }

    // Get database stats
    const userCount = await db.getAsync('SELECT COUNT(*) as count FROM users');
    const todayCheckins = await db.getAsync(`
      SELECT COUNT(*) as count FROM checkin_records 
      WHERE checkin_date = date('now')
    `);
    const totalPoints = await db.getAsync('SELECT SUM(points) as total FROM users');

    const initialData = {
      type: 'initial_data',
      data: {
        botStatus,
        serverData,
        stats: {
          userCount: userCount?.count || 0,
          todayCheckins: todayCheckins?.count || 0,
          totalPoints: totalPoints?.total || 0
        }
      }
    };

    ws.send(JSON.stringify(initialData));
  } catch (error) {
    logger.error('Error sending initial data:', error);
  }
}

function handleClientMessage(ws, data) {
  switch (data.type) {
    case 'ping':
      ws.send(JSON.stringify({ type: 'pong' }));
      break;
    case 'request_update':
      sendInitialData(ws);
      break;
    default:
      logger.warn('Unknown WebSocket message type:', data.type);
  }
}

function startDataBroadcasting() {
  // Broadcast updates every 30 seconds
  setInterval(async () => {
    if (clients.size === 0) return;

    try {
      const botStatus = getBotStatus();
      const client = getClient();
      
      let serverData = {
        memberCount: 0,
        onlineCount: 0,
        activeChannels: 0
      };

      if (client && client.isReady()) {
        const guild = client.guilds.cache.first();
        if (guild) {
          serverData = {
            memberCount: guild.memberCount,
            onlineCount: guild.members.cache.filter(member => 
              member.presence?.status === 'online' || 
              member.presence?.status === 'idle' || 
              member.presence?.status === 'dnd'
            ).size,
            activeChannels: guild.channels.cache.filter(channel => 
              channel.type === 0 // Text channels
            ).size
          };
        }
      }

      // Get real-time database stats
      const userCount = await db.getAsync('SELECT COUNT(*) as count FROM users');
      const todayCheckins = await db.getAsync(`
        SELECT COUNT(*) as count FROM checkin_records 
        WHERE checkin_date = date('now')
      `);
      const recentMessages = await db.getAsync(`
        SELECT COUNT(*) as count FROM users 
        WHERE last_active > datetime('now', '-1 hour')
      `);

      const updateData = {
        type: 'real_time_update',
        data: {
          botStatus,
          serverData,
          stats: {
            userCount: userCount?.count || 0,
            todayCheckins: todayCheckins?.count || 0,
            recentActiveUsers: recentMessages?.count || 0,
            timestamp: new Date().toISOString()
          }
        }
      };

      broadcastToAllClients(updateData);
    } catch (error) {
      logger.error('Error broadcasting data:', error);
    }
  }, 30000); // 30 seconds

  // Broadcast bot events immediately
  setInterval(async () => {
    if (clients.size === 0) return;

    try {
      const botStatus = getBotStatus();
      const eventData = {
        type: 'bot_status_update',
        data: {
          botStatus,
          timestamp: new Date().toISOString()
        }
      };

      broadcastToAllClients(eventData);
    } catch (error) {
      logger.error('Error broadcasting bot status:', error);
    }
  }, 5000); // 5 seconds for bot status
}

function broadcastToAllClients(data) {
  const message = JSON.stringify(data);
  clients.forEach(client => {
    if (client.readyState === 1) { // WebSocket.OPEN
      try {
        client.send(message);
      } catch (error) {
        logger.error('Error sending message to client:', error);
        clients.delete(client);
      }
    } else {
      clients.delete(client);
    }
  });
}

// Export function to broadcast custom events
export function broadcastEvent(eventType, data) {
  const message = {
    type: eventType,
    data,
    timestamp: new Date().toISOString()
  };
  broadcastToAllClients(message);
}