import { users, pointRecords, checkinRecords, checkinConfig, userConsecutiveCheckins, botSettings, automationRules, systemLogs, titles, lotteries, lotteryPrizes, lotteryParticipants, lotteryDrawRecords, slashCommands, slashCommandUsage, activityRecords, activityConfig, type User, type InsertUser, type PointRecord, type InsertPointRecord, type CheckinRecord, type InsertCheckinRecord, type CheckinConfig, type InsertCheckinConfig, type UserConsecutiveCheckin, type InsertUserConsecutiveCheckin, type BotSettings, type InsertBotSettings, type AutomationRule, type InsertAutomationRule, type SystemLog, type InsertSystemLog, type Title, type InsertTitle, type Lottery, type InsertLottery, type LotteryPrize, type InsertLotteryPrize, type LotteryParticipant, type InsertLotteryParticipant, type LotteryDrawRecord, type InsertLotteryDrawRecord, type SlashCommand, type InsertSlashCommand, type SlashCommandUsage, type InsertSlashCommandUsage, type ActivityRecord, type InsertActivityRecord, type ActivityConfig, type InsertActivityConfig } from "../shared/schema";
import { db } from "./db";
import { eq, desc, and, gte, lt, sql } from "drizzle-orm";

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByDiscordId(discordId: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: Partial<User>): Promise<User | undefined>;
  deleteUser(id: number): Promise<void>;
  getAllUsers(): Promise<User[]>;
  
  // Point record methods
  createPointRecord(record: InsertPointRecord): Promise<PointRecord>;
  getUserPointRecords(userId: number): Promise<PointRecord[]>;
  getPointRecords(): Promise<PointRecord[]>;
  deletePointRecord(recordId: number): Promise<void>;
  getTodayPointsDistribution(): Promise<number>;
  getTodayPointsDeduction(): Promise<number>;
  getTotalPointsPool(): Promise<number>;
  addPointsToUser(userId: number, amount: number, reason: string): Promise<void>;
  
  // Checkin methods
  createCheckinRecord(record: InsertCheckinRecord): Promise<CheckinRecord>;
  getUserCheckinRecords(userId: number): Promise<CheckinRecord[]>;
  deleteCheckinRecord(recordId: number): Promise<void>;
  getTodayCheckins(): Promise<number>;
  getUserConsecutiveDays(userId: number): Promise<number>;
  
  // User consecutive checkin methods
  getUserConsecutiveCheckin(userId: number): Promise<UserConsecutiveCheckin | undefined>;
  updateUserConsecutiveCheckin(userId: number, consecutiveDays: number, lastCheckinDate: string): Promise<void>;
  createUserConsecutiveCheckin(record: InsertUserConsecutiveCheckin): Promise<UserConsecutiveCheckin>;
  
  // Checkin config methods
  getAllCheckinConfigs(): Promise<CheckinConfig[]>;
  getActiveCheckinConfig(): Promise<CheckinConfig | undefined>;
  createCheckinConfig(config: InsertCheckinConfig): Promise<CheckinConfig>;
  updateCheckinConfig(id: number, updates: Partial<CheckinConfig>): Promise<CheckinConfig | undefined>;
  deleteCheckinConfig(id: number): Promise<void>;
  
  // Bot settings methods
  getBotSetting(key: string): Promise<string | null>;
  setBotSetting(key: string, value: string): Promise<void>;
  
  // Automation rules methods
  getAllAutomationRules(): Promise<AutomationRule[]>;
  createAutomationRule(rule: InsertAutomationRule): Promise<AutomationRule>;
  updateAutomationRule(id: number, updates: Partial<AutomationRule>): Promise<AutomationRule | undefined>;
  
  // System logs methods
  createSystemLog(log: InsertSystemLog): Promise<SystemLog>;
  getSystemLogs(limit?: number): Promise<SystemLog[]>;
  
  // Title methods
  getAllTitles(): Promise<Title[]>;
  createTitle(title: InsertTitle): Promise<Title>;
  updateTitle(id: number, updates: Partial<Title>): Promise<Title | undefined>;
  deleteTitle(id: number): Promise<void>;
  getTitleById(id: number): Promise<Title | undefined>;
  
  // Lottery methods
  getAllLotteries(): Promise<Lottery[]>;
  createLottery(lottery: InsertLottery): Promise<Lottery>;
  updateLottery(id: number, updates: Partial<Lottery>): Promise<Lottery | undefined>;
  deleteLottery(id: number): Promise<void>;
  getLotteryById(id: number): Promise<Lottery | undefined>;
  
  // Lottery prize methods
  getLotteryPrizes(lotteryId: number): Promise<LotteryPrize[]>;
  createLotteryPrize(prize: InsertLotteryPrize): Promise<LotteryPrize>;
  updateLotteryPrize(id: number, updates: Partial<LotteryPrize>): Promise<LotteryPrize | undefined>;
  deleteLotteryPrize(id: number): Promise<void>;
  
  // Lottery participant methods
  getLotteryParticipants(lotteryId: number): Promise<LotteryParticipant[]>;
  addLotteryParticipant(participant: InsertLotteryParticipant): Promise<LotteryParticipant>;
  removeLotteryParticipant(lotteryId: number, userId: number): Promise<void>;
  
  // Lottery draw record methods
  getLotteryDrawRecords(lotteryId?: number): Promise<LotteryDrawRecord[]>;
  createLotteryDrawRecord(record: InsertLotteryDrawRecord): Promise<LotteryDrawRecord>;
  
  // Slash command methods
  getAllSlashCommands(): Promise<SlashCommand[]>;
  getSlashCommandById(id: number): Promise<SlashCommand | undefined>;
  createSlashCommand(command: InsertSlashCommand): Promise<SlashCommand>;
  updateSlashCommand(id: number, updates: Partial<SlashCommand>): Promise<SlashCommand | undefined>;
  deleteSlashCommand(id: number): Promise<void>;
  getSlashCommandsByCategory(category: string): Promise<SlashCommand[]>;
  incrementSlashCommandUsage(commandId: number): Promise<void>;
  
  // Slash command usage methods
  createSlashCommandUsage(usage: InsertSlashCommandUsage): Promise<SlashCommandUsage>;
  getSlashCommandUsageStats(commandId?: number): Promise<any>;
  getTodaySlashCommandUsage(): Promise<number>;
  getSlashCommandUsageByUser(userId: number): Promise<SlashCommandUsage[]>;

  // Code redemption methods
  hasRedeemedCode(userId: number, code: string): Promise<boolean>;
  recordCodeRedemption(userId: number, code: string): Promise<void>;
  
}

export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async getUserByDiscordId(discordId: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.discordId, discordId));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    const [user] = await db.update(users).set(updates).where(eq(users.id, id)).returning();
    return user || undefined;
  }

  async deleteUser(id: number): Promise<void> {
    await db.delete(users).where(eq(users.id, id));
  }

  async getAllUsers(): Promise<User[]> {
    try {
      // Try the ORM first
      return await db.select().from(users).orderBy(desc(users.points));
    } catch (error) {
      console.log('ORM failed, using raw SQL:', error);
      // Fallback to raw SQL
      const result = await db.execute(sql`
        SELECT id, username, password, discord_id, avatar, join_date, points, titles, status, roles, last_active, messages, voice_time
        FROM users 
        ORDER BY points DESC NULLS LAST
      `);
      return result.rows.map((row: any) => ({
        id: row.id,
        username: row.username,
        password: row.password,
        discordId: row.discord_id,
        avatar: row.avatar,
        joinDate: row.join_date,
        points: row.points || 0,
        titles: row.titles || [],
        status: row.status || 'offline',
        roles: row.roles || [],
        lastActive: row.last_active,
        messages: row.messages || 0,
        voiceTime: row.voice_time || '0h 0m'
      }));
    }
  }

  // Point record methods
  async createPointRecord(record: InsertPointRecord): Promise<PointRecord> {
    // Get current user points first
    const currentUser = await this.getUser(record.userId);
    const oldPoints = currentUser?.points || 0;
    const newPoints = oldPoints + record.amount;
    
    // Create record with old and new points
    const [pointRecord] = await db.insert(pointRecords).values({
      ...record,
      oldPoints: oldPoints,
      newPoints: newPoints
    }).returning();
    
    // Update user's total points
    if (record.userId) {
      await this.updateUser(record.userId, { points: newPoints });
    }
    
    return pointRecord;
  }

  async getUserPointRecords(userId: number): Promise<PointRecord[]> {
    return await db.select().from(pointRecords).where(eq(pointRecords.userId, userId)).orderBy(desc(pointRecords.createdAt));
  }

  async getPointRecords(): Promise<PointRecord[]> {
    const records = await db.select().from(pointRecords).orderBy(desc(pointRecords.createdAt));
    
    // Join with user data to get username and avatar
    const recordsWithUserData = [];
    for (const record of records) {
      const user = await this.getUser(record.userId);
      recordsWithUserData.push({
        ...record,
        username: user?.username || 'Unknown User',
        avatar: user?.avatar || null,
        oldPoints: record.oldPoints || 0,
        newPoints: record.newPoints || 0
      });
    }
    
    return recordsWithUserData;
  }

  async deletePointRecord(recordId: number): Promise<void> {
    await db.delete(pointRecords).where(eq(pointRecords.id, recordId));
  }

  async getTodayPointsDistribution(): Promise<number> {
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);
    
    const records = await db.select().from(pointRecords)
      .where(
        and(
          gte(pointRecords.createdAt, todayStart),
          lt(pointRecords.createdAt, todayEnd),
          gte(pointRecords.amount, 0) // Only positive amounts
        )
      );
    
    return records.reduce((sum, record) => sum + record.amount, 0);
  }

  async getTodayPointsDeduction(): Promise<number> {
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);
    
    const records = await db.select().from(pointRecords)
      .where(
        and(
          gte(pointRecords.createdAt, todayStart),
          lt(pointRecords.createdAt, todayEnd),
          lt(pointRecords.amount, 0) // Only negative amounts
        )
      );
    
    return records.reduce((sum, record) => sum + record.amount, 0);
  }

  async getTotalPointsPool(): Promise<number> {
    const usersData = await db.select().from(users);
    return usersData.reduce((sum, user) => sum + (user.points || 0), 0);
  }

  async addPointsToUser(userId: number, amount: number, reason: string): Promise<void> {
    await this.createPointRecord({
      userId: userId,
      amount: amount,
      reason: reason,
      type: amount > 0 ? 'earned' : 'deducted'
    });
  }

  // Checkin methods
  async createCheckinRecord(record: InsertCheckinRecord): Promise<CheckinRecord> {
    const [checkinRecord] = await db.insert(checkinRecords).values(record).returning();
    return checkinRecord;
  }

  async getUserCheckinRecords(userId: number): Promise<CheckinRecord[]> {
    return await db.select().from(checkinRecords).where(eq(checkinRecords.userId, userId)).orderBy(desc(checkinRecords.checkinDate));
  }

  async deleteCheckinRecord(recordId: number): Promise<void> {
    await db.delete(checkinRecords).where(eq(checkinRecords.id, recordId));
  }

  async getTodayCheckins(): Promise<number> {
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);
    
    const records = await db.select().from(checkinRecords)
      .where(
        and(
          gte(checkinRecords.checkinDate, todayStart),
          lt(checkinRecords.checkinDate, todayEnd)
        )
      );
    return records.length;
  }

  async getUserConsecutiveDays(userId: number): Promise<number> {
    const records = await db.select()
      .from(checkinRecords)
      .where(eq(checkinRecords.userId, userId))
      .orderBy(desc(checkinRecords.checkinDate));
    
    if (records.length === 0) return 0;
    
    // Start with the most recent record's consecutive days
    const mostRecentRecord = records[0];
    let consecutiveDays = mostRecentRecord.consecutiveDays || 1;
    
    // If the most recent check-in was today, return its consecutive days
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const mostRecentDate = new Date(mostRecentRecord.checkinDate);
    const mostRecentDay = new Date(mostRecentDate.getFullYear(), mostRecentDate.getMonth(), mostRecentDate.getDate());
    
    if (mostRecentDay.getTime() === today.getTime()) {
      return consecutiveDays;
    }
    
    // If the most recent check-in was yesterday, add 1 for today
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    if (mostRecentDay.getTime() === yesterday.getTime()) {
      return consecutiveDays + 1;
    }
    
    // If the last check-in was more than 1 day ago, reset to 1
    return 1;
  }

  // Checkin config methods
  async getAllCheckinConfigs(): Promise<CheckinConfig[]> {
    return await db.select().from(checkinConfig).orderBy(desc(checkinConfig.createdAt));
  }

  async getActiveCheckinConfig(): Promise<CheckinConfig | undefined> {
    const [config] = await db.select().from(checkinConfig).where(eq(checkinConfig.isActive, true)).limit(1);
    return config || undefined;
  }

  async createCheckinConfig(config: InsertCheckinConfig): Promise<CheckinConfig> {
    const [newConfig] = await db.insert(checkinConfig).values(config).returning();
    return newConfig;
  }

  async updateCheckinConfig(id: number, updates: Partial<CheckinConfig>): Promise<CheckinConfig | undefined> {
    const [config] = await db.update(checkinConfig).set({...updates, updatedAt: new Date()}).where(eq(checkinConfig.id, id)).returning();
    return config || undefined;
  }

  async deleteCheckinConfig(id: number): Promise<void> {
    await db.delete(checkinConfig).where(eq(checkinConfig.id, id));
  }

  // Bot settings methods
  async getBotSetting(key: string): Promise<string | null> {
    const [setting] = await db.select().from(botSettings).where(eq(botSettings.key, key));
    return setting?.value || null;
  }

  async setBotSetting(key: string, value: string): Promise<void> {
    await db.insert(botSettings)
      .values({ key, value })
      .onConflictDoUpdate({
        target: botSettings.key,
        set: { value, updatedAt: new Date() }
      });
  }

  // Automation rules methods
  async getAllAutomationRules(): Promise<AutomationRule[]> {
    return await db.select().from(automationRules).orderBy(desc(automationRules.createdAt));
  }

  async createAutomationRule(rule: InsertAutomationRule): Promise<AutomationRule> {
    const [automationRule] = await db.insert(automationRules).values(rule).returning();
    return automationRule;
  }

  async updateAutomationRule(id: number, updates: Partial<AutomationRule>): Promise<AutomationRule | undefined> {
    const [rule] = await db.update(automationRules).set(updates).where(eq(automationRules.id, id)).returning();
    return rule || undefined;
  }

  // System logs methods
  async createSystemLog(log: InsertSystemLog): Promise<SystemLog> {
    const [systemLog] = await db.insert(systemLogs).values(log).returning();
    return systemLog;
  }

  async getSystemLogs(limit: number = 100): Promise<SystemLog[]> {
    return await db.select().from(systemLogs).orderBy(desc(systemLogs.time)).limit(limit);
  }

  // Title methods
  async getAllTitles(): Promise<Title[]> {
    const titleList = await db.select().from(titles).orderBy(titles.minPoints);
    return titleList;
  }

  async createTitle(title: InsertTitle): Promise<Title> {
    const [newTitle] = await db
      .insert(titles)
      .values(title)
      .returning();
    return newTitle;
  }

  async updateTitle(id: number, updates: Partial<Title>): Promise<Title | undefined> {
    const [updatedTitle] = await db
      .update(titles)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(titles.id, id))
      .returning();
    return updatedTitle || undefined;
  }

  async deleteTitle(id: number): Promise<void> {
    await db.delete(titles).where(eq(titles.id, id));
  }

  async getTitleById(id: number): Promise<Title | undefined> {
    const [title] = await db.select().from(titles).where(eq(titles.id, id));
    return title || undefined;
  }

  // Lottery methods
  async getAllLotteries(): Promise<Lottery[]> {
    return await db.select().from(lotteries).orderBy(desc(lotteries.createdAt));
  }

  async createLottery(insertLottery: InsertLottery): Promise<Lottery> {
    const [lottery] = await db
      .insert(lotteries)
      .values(insertLottery)
      .returning();
    return lottery;
  }

  async updateLottery(id: number, updates: Partial<Lottery>): Promise<Lottery | undefined> {
    const [lottery] = await db
      .update(lotteries)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(lotteries.id, id))
      .returning();
    return lottery || undefined;
  }

  async deleteLottery(id: number): Promise<void> {
    await db.delete(lotteries).where(eq(lotteries.id, id));
  }

  async getLotteryById(id: number): Promise<Lottery | undefined> {
    const [lottery] = await db.select().from(lotteries).where(eq(lotteries.id, id));
    return lottery || undefined;
  }

  // Lottery prize methods
  async getLotteryPrizes(lotteryId: number): Promise<LotteryPrize[]> {
    return await db.select().from(lotteryPrizes).where(eq(lotteryPrizes.lotteryId, lotteryId));
  }

  async createLotteryPrize(prize: InsertLotteryPrize): Promise<LotteryPrize> {
    const [lotteryPrize] = await db
      .insert(lotteryPrizes)
      .values(prize)
      .returning();
    return lotteryPrize;
  }

  async updateLotteryPrize(id: number, updates: Partial<LotteryPrize>): Promise<LotteryPrize | undefined> {
    const [prize] = await db
      .update(lotteryPrizes)
      .set(updates)
      .where(eq(lotteryPrizes.id, id))
      .returning();
    return prize || undefined;
  }

  async deleteLotteryPrize(id: number): Promise<void> {
    await db.delete(lotteryPrizes).where(eq(lotteryPrizes.id, id));
  }

  // Lottery participant methods
  async getLotteryParticipants(lotteryId: number): Promise<LotteryParticipant[]> {
    return await db.select().from(lotteryParticipants).where(eq(lotteryParticipants.lotteryId, lotteryId));
  }

  async addLotteryParticipant(participant: InsertLotteryParticipant): Promise<LotteryParticipant> {
    const [lotteryParticipant] = await db
      .insert(lotteryParticipants)
      .values(participant)
      .returning();
    return lotteryParticipant;
  }

  async removeLotteryParticipant(lotteryId: number, userId: number): Promise<void> {
    await db.delete(lotteryParticipants)
      .where(and(
        eq(lotteryParticipants.lotteryId, lotteryId),
        eq(lotteryParticipants.userId, userId)
      ));
  }

  // Lottery draw record methods
  async getLotteryDrawRecords(lotteryId?: number): Promise<LotteryDrawRecord[]> {
    if (lotteryId) {
      return await db.select().from(lotteryDrawRecords)
        .where(eq(lotteryDrawRecords.lotteryId, lotteryId))
        .orderBy(desc(lotteryDrawRecords.drawnAt));
    }
    return await db.select().from(lotteryDrawRecords).orderBy(desc(lotteryDrawRecords.drawnAt));
  }

  async createLotteryDrawRecord(record: InsertLotteryDrawRecord): Promise<LotteryDrawRecord> {
    const [drawRecord] = await db
      .insert(lotteryDrawRecords)
      .values(record)
      .returning();
    return drawRecord;
  }

  // Slash command methods
  async getAllSlashCommands(): Promise<SlashCommand[]> {
    return await db.select().from(slashCommands).orderBy(slashCommands.createdAt);
  }

  async getSlashCommandById(id: number): Promise<SlashCommand | undefined> {
    const [command] = await db.select().from(slashCommands).where(eq(slashCommands.id, id));
    return command || undefined;
  }

  async createSlashCommand(command: InsertSlashCommand): Promise<SlashCommand> {
    const [newCommand] = await db
      .insert(slashCommands)
      .values({
        ...command,
        updatedAt: new Date()
      })
      .returning();
    return newCommand;
  }

  async updateSlashCommand(id: number, updates: Partial<SlashCommand>): Promise<SlashCommand | undefined> {
    const [updatedCommand] = await db
      .update(slashCommands)
      .set({
        ...updates,
        updatedAt: new Date()
      })
      .where(eq(slashCommands.id, id))
      .returning();
    return updatedCommand || undefined;
  }

  async deleteSlashCommand(id: number): Promise<void> {
    await db.delete(slashCommands).where(eq(slashCommands.id, id));
  }

  async getSlashCommandsByCategory(category: string): Promise<SlashCommand[]> {
    return await db.select().from(slashCommands).where(eq(slashCommands.category, category));
  }

  async incrementSlashCommandUsage(commandId: number): Promise<void> {
    // First get current usage
    const [currentCommand] = await db.select().from(slashCommands).where(eq(slashCommands.id, commandId));
    if (currentCommand) {
      await db
        .update(slashCommands)
        .set({ 
          usage: (currentCommand.usage || 0) + 1,
          updatedAt: new Date()
        })
        .where(eq(slashCommands.id, commandId));
    }
  }

  // Slash command usage methods
  async createSlashCommandUsage(usage: InsertSlashCommandUsage): Promise<SlashCommandUsage> {
    const [newUsage] = await db
      .insert(slashCommandUsage)
      .values(usage)
      .returning();
    
    // Increment command usage count
    await this.incrementSlashCommandUsage(usage.commandId);
    
    return newUsage;
  }

  async getSlashCommandUsageStats(commandId?: number): Promise<any> {
    if (commandId) {
      return await db.select().from(slashCommandUsage).where(eq(slashCommandUsage.commandId, commandId));
    }
    return await db.select().from(slashCommandUsage);
  }

  async getTodaySlashCommandUsage(): Promise<number> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const usageRecords = await db
      .select()
      .from(slashCommandUsage)
      .where(gte(slashCommandUsage.usedAt, today));
    
    return usageRecords.length;
  }

  async getSlashCommandUsageByUser(userId: number): Promise<SlashCommandUsage[]> {
    return await db.select().from(slashCommandUsage).where(eq(slashCommandUsage.userId, userId));
  }

  // Code redemption methods (using point records for more reliable tracking)
  async hasRedeemedCode(userId: number, code: string): Promise<boolean> {
    try {
      // Check point records table for this specific code redemption
      const result = await db
        .select({ count: sql<number>`count(*)` })
        .from(pointRecords)
        .where(and(
          eq(pointRecords.userId, userId),
          eq(pointRecords.reason, `Code redemption: ${code}`)
        ));
      return (result[0]?.count || 0) > 0;
    } catch (error) {
      console.error('Error checking code redemption:', error);
      return false;
    }
  }

  async recordCodeRedemption(userId: number, code: string): Promise<void> {
    try {
      await db.insert(systemLogs).values({
        user: userId.toString(),
        module: 'code_redemption',
        action: `code_redemption_${code}`,
        level: 'info',
        details: `User redeemed code: ${code}`,
        time: new Date()
      });
    } catch (error) {
      console.error('Error recording code redemption:', error);
      throw error;
    }
  }

  // Activity system methods
  async getAllActivityRecords(): Promise<ActivityRecord[]> {
    return await db.select().from(activityRecords)
      .orderBy(activityRecords.createdAt)
      .limit(1000);
  }

  async getActivityRecordsByUser(userId: number): Promise<ActivityRecord[]> {
    return await db.select().from(activityRecords)
      .where(eq(activityRecords.userId, userId))
      .orderBy(activityRecords.createdAt);
  }

  async createActivityRecord(record: InsertActivityRecord): Promise<ActivityRecord> {
    const [newRecord] = await db
      .insert(activityRecords)
      .values(record)
      .returning();
    return newRecord;
  }

  async getActivityStats(): Promise<any> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const [totalStats] = await db
      .select({
        totalActivities: sql<number>`count(*)`,
        totalPoints: sql<number>`sum(${activityRecords.points})`
      })
      .from(activityRecords);

    const [todayStats] = await db
      .select({
        todayActivities: sql<number>`count(*)`
      })
      .from(activityRecords)
      .where(gte(activityRecords.createdAt, today));

    const [activeUsersStats] = await db
      .select({
        activeUsers: sql<number>`count(distinct ${activityRecords.userId})`
      })
      .from(activityRecords)
      .where(gte(activityRecords.createdAt, today));

    return {
      totalActivities: totalStats?.totalActivities || 0,
      totalPoints: totalStats?.totalPoints || 0,
      todayActivities: todayStats?.todayActivities || 0,
      activeUsers: activeUsersStats?.activeUsers || 0
    };
  }

  async getActivityConfig(): Promise<ActivityConfig[]> {
    return await db.select().from(activityConfig).orderBy(activityConfig.activityType);
  }

  async createActivityConfig(config: InsertActivityConfig): Promise<ActivityConfig> {
    const [newConfig] = await db
      .insert(activityConfig)
      .values(config)
      .returning();
    return newConfig;
  }

  async updateActivityConfig(id: number, config: Partial<InsertActivityConfig>): Promise<ActivityConfig> {
    const [updatedConfig] = await db
      .update(activityConfig)
      .set({ ...config, updatedAt: new Date() })
      .where(eq(activityConfig.id, id))
      .returning();
    return updatedConfig;
  }



  // User consecutive checkin methods
  async getUserConsecutiveCheckin(userId: number): Promise<UserConsecutiveCheckin | undefined> {
    const [record] = await db.select().from(userConsecutiveCheckins).where(eq(userConsecutiveCheckins.userId, userId));
    return record || undefined;
  }

  async updateUserConsecutiveCheckin(userId: number, consecutiveDays: number, lastCheckinDate: string): Promise<void> {
    await db.insert(userConsecutiveCheckins)
      .values({ 
        userId, 
        consecutiveDays, 
        lastCheckinDate,
        lastUpdated: new Date()
      })
      .onConflictDoUpdate({
        target: userConsecutiveCheckins.userId,
        set: { 
          consecutiveDays, 
          lastCheckinDate,
          lastUpdated: new Date()
        }
      });
  }

  async createUserConsecutiveCheckin(record: InsertUserConsecutiveCheckin): Promise<UserConsecutiveCheckin> {
    const [checkinRecord] = await db.insert(userConsecutiveCheckins).values(record).returning();
    return checkinRecord;
  }
}

export const storage = new DatabaseStorage();
