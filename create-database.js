import { Client } from 'pg';
import 'dotenv/config';

async function createDatabase() {
  // Connect to postgres database first to create our target database
  const client = new Client({
    host: 'localhost',
    port: 5432,
    user: 'postgres',
    password: 'root',
    database: 'postgres' // Connect to default postgres database
  });

  try {
    await client.connect();
    console.log('Connected to PostgreSQL');
    
    // Check if database exists
    const result = await client.query(
      "SELECT 1 FROM pg_database WHERE datname = 'dc_bot'"
    );
    
    if (result.rows.length === 0) {
      // Create database if it doesn't exist
      await client.query('CREATE DATABASE dc_bot');
      console.log('✅ Database "dc_bot" created successfully');
    } else {
      console.log('✅ Database "dc_bot" already exists');
    }
    
  } catch (error) {
    console.error('❌ Error creating database:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

createDatabase();
