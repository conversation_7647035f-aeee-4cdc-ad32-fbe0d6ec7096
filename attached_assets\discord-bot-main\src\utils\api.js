export function getApiBaseUrl() {
  if (typeof window !== 'undefined') {
    const { protocol, hostname, port } = window.location;
    
    // 在开发环境中
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return `${protocol}//${hostname}:3001`;
    }
    
    // 在生产环境中，使用相对路径
    return '';
  }
  return '';
}

export function getWebSocketUrl() {
  if (typeof window !== 'undefined') {
    const { protocol, hostname, port } = window.location;
    const wsProtocol = protocol === 'https:' ? 'wss' : 'ws';
    
    // 在开发环境中
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return `${wsProtocol}://${hostname}:3001`;
    }
    
    // 在生产环境中
    return `${wsProtocol}://${hostname}${port ? ':' + port : ''}`;
  }
  return '';
}