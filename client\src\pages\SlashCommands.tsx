import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import * as icons from 'lucide-react';

interface SlashCommand {
  id: number;
  name: string;
  description: string;
  category: string;
  usage: number;
  isEnabled: boolean;
  parameters?: string;
  response?: string;
  cooldown: number;
  permissions: string;
  createdAt: string;
  updatedAt: string;
}

interface CommandStats {
  totalCommands: number;
  enabledCommands: number;
  todayUsage: number;
  totalUsers: number;
}

const SlashCommands: React.FC = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCommand, setEditingCommand] = useState<SlashCommand | null>(null);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const queryClient = useQueryClient();

  // Fetch commands
  const { data: commandsData, isLoading: commandsLoading } = useQuery({
    queryKey: ['/api/slash-commands'],
    queryFn: async () => {
      const response = await fetch(`/api/slash-commands?t=${Date.now()}`);
      if (!response.ok) throw new Error('Failed to fetch commands');
      return response.json();
    },
    refetchInterval: 1000, // Refresh every 1 second to show new commands
    staleTime: 0, // Always consider data stale
    refetchOnMount: true
  });

  // Fetch stats
  const { data: statsData } = useQuery<{success: boolean, data: CommandStats}>({
    queryKey: ['/api/slash-commands/stats'],
    queryFn: async () => {
      const response = await fetch('/api/slash-commands/stats');
      if (!response.ok) throw new Error('Failed to fetch stats');
      return response.json();
    }
  });

  const commands: SlashCommand[] = commandsData?.data || [];
  
  // Debug logging
  console.log('Commands data:', commandsData);
  console.log('Commands array:', commands);
  console.log('Commands length:', commands.length);
  console.log('Loading state:', commandsLoading);
  
  // Force show commands if we have data
  const showCommands = commands.length > 0;
  const stats = statsData?.data;

  // Create command mutation
  const createMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch('/api/slash-commands', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error('Failed to create command');
      return response.json();
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['/api/slash-commands'] });
      queryClient.invalidateQueries({ queryKey: ['/api/slash-commands/stats'] });
      setShowCreateModal(false);
      setOperationResult({ type: 'success', message: result.message || '指令创建成功' });
      setTimeout(() => setOperationResult(null), 3000);
    },
    onError: (error: any) => {
      setOperationResult({ type: 'error', message: error.message || '指令创建失败' });
      setTimeout(() => setOperationResult(null), 3000);
    }
  });

  // Update command mutation
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number, data: any }) => {
      const response = await fetch(`/api/slash-commands/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error('Failed to update command');
      return response.json();
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['/api/slash-commands'] });
      queryClient.invalidateQueries({ queryKey: ['/api/slash-commands/stats'] });
      setEditingCommand(null);
      setOperationResult({ type: 'success', message: result.message || '指令更新成功' });
      setTimeout(() => setOperationResult(null), 3000);
    },
    onError: (error: any) => {
      setOperationResult({ type: 'error', message: error.message || '指令更新失败' });
      setTimeout(() => setOperationResult(null), 3000);
    }
  });

  // Delete command mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/slash-commands/${id}`, {
        method: 'DELETE'
      });
      if (!response.ok) throw new Error('Failed to delete command');
      return response.json();
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['/api/slash-commands'] });
      queryClient.invalidateQueries({ queryKey: ['/api/slash-commands/stats'] });
      setOperationResult({ type: 'success', message: result.message || '指令删除成功' });
      setTimeout(() => setOperationResult(null), 3000);
    },
    onError: (error: any) => {
      setOperationResult({ type: 'error', message: error.message || '指令删除失败' });
      setTimeout(() => setOperationResult(null), 3000);
    }
  });

  // Handle create submit
  const handleCreateSubmit = (data: any) => {
    createMutation.mutate(data);
  };

  // Handle edit submit
  const handleEditSubmit = (data: any) => {
    if (editingCommand) {
      updateMutation.mutate({ id: editingCommand.id, data });
    }
  };

  // Handle delete
  const handleDelete = (command: SlashCommand) => {
    if (window.confirm(`确定删除指令 "${command.name}" 吗？`)) {
      deleteMutation.mutate(command.id);
    }
  };

  // Category colors
  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'points': 'bg-gradient-to-br from-yellow-400 to-yellow-500',
      'entertainment': 'bg-gradient-to-br from-pink-400 to-pink-500',
      'system': 'bg-gradient-to-br from-blue-400 to-blue-500',
      'lottery': 'bg-gradient-to-br from-purple-400 to-purple-500',
      'management': 'bg-gradient-to-br from-green-400 to-green-500'
    };
    return colors[category] || 'bg-gradient-to-br from-gray-400 to-gray-500';
  };

  // Category icons
  const getCategoryIcon = (category: string) => {
    const iconMap: { [key: string]: any } = {
      'points': icons.Star,
      'entertainment': icons.Gamepad2,
      'system': icons.Settings,
      'lottery': icons.Gift,
      'management': icons.Shield
    };
    return iconMap[category] || icons.Hash;
  };

  // Get commands by category
  const getCommandsByCategory = (category: string) => {
    return commands.filter(cmd => cmd.category === category);
  };

  const categories = [
    { id: 'points', name: '积分系统', count: getCommandsByCategory('points').length },
    { id: 'entertainment', name: '娱乐功能', count: getCommandsByCategory('entertainment').length },
    { id: 'system', name: '系统指令', count: getCommandsByCategory('system').length },
    { id: 'lottery', name: '抽奖系统', count: getCommandsByCategory('lottery').length },
    { id: 'management', name: '管理功能', count: getCommandsByCategory('management').length }
  ];

  if (commandsLoading && !showCommands) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-yellow-400 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载指令中... ({commands.length} 个指令已加载)</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-black text-gray-900 dark:text-white">Slash 指令</h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2 font-medium">管理和配置 Discord Slash 指令</p>
          </div>
          <div className="flex space-x-3">
            <button 
              onClick={() => setShowCreateModal(true)}
              className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-yellow-500/25 font-medium"
            >
              <icons.Plus className="w-4 h-4" />
              <span className="font-medium">创建指令</span>
            </button>
          </div>
        </div>

        {/* Operation Result */}
        {operationResult && (
          <div className={`p-4 rounded-xl border mb-6 ${
            operationResult.type === 'success' 
              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
              : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
          }`}>
            <div className="flex items-center space-x-2">
              {operationResult.type === 'success' ? (
                <icons.CheckCircle className="w-5 h-5" />
              ) : (
                <icons.AlertCircle className="w-5 h-5" />
              )}
              <span className="font-medium">{operationResult.message}</span>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                <icons.Hash className="w-6 h-6 text-white font-bold" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">指令包数</p>
                <p className="text-2xl font-black text-gray-900 dark:text-white">{stats?.totalCommands || 0}</p>
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" 
                style={{ width: stats?.totalCommands ? '100%' : '0%' }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">系统完整度 {stats?.totalCommands ? '100%' : '0%'}</p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                <icons.Play className="w-6 h-6 text-white font-bold" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">启用指令</p>
                <p className="text-2xl font-black text-gray-900 dark:text-white">{stats?.enabledCommands || 0}</p>
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" 
                style={{ width: stats?.totalCommands ? `${((stats?.enabledCommands || 0) / stats.totalCommands) * 100}%` : '0%' }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">运行正常</p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                <icons.BarChart className="w-6 h-6 text-white font-bold" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">加载的指令</p>
                <p className="text-2xl font-black text-gray-900 dark:text-white">{commands.length}/5</p>
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full" 
                style={{ width: stats?.todayUsage ? `${Math.min((stats.todayUsage / 100) * 100, 100)}%` : '0%' }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">比昨日增长 {stats?.todayUsage || 0}%</p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-500 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25">
                <icons.Users className="w-6 h-6 text-white font-bold" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">使用人数</p>
                <p className="text-2xl font-black text-gray-900 dark:text-white">{stats?.totalUsers || 0}</p>
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-purple-400 to-purple-500 h-2 rounded-full" 
                style={{ width: stats?.totalUsers ? `${Math.min((stats.totalUsers / 50) * 100, 100)}%` : '0%' }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">活跃用户</p>
          </div>
        </div>

        {/* Command Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => {
            const categoryCommands = getCommandsByCategory(category.id);
            const IconComponent = getCategoryIcon(category.id);
            
            return (
              <div key={category.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div className={`p-6 ${getCategoryColor(category.id)}`}>
                  <div className="flex items-center justify-between text-white">
                    <div className="flex items-center space-x-3">
                      <IconComponent className="w-8 h-8" />
                      <div>
                        <h3 className="text-xl font-black">{category.name}</h3>
                        <p className="text-white/80 font-medium">{category.count} 个指令</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  {categoryCommands.length > 0 ? (
                    <div className="space-y-3">
                      {categoryCommands.map((command) => (
                        <div key={command.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <span className="font-black text-gray-900 dark:text-white">/{command.name}</span>
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                command.isEnabled 
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' 
                                  : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                              }`}>
                                {command.isEnabled ? '启用' : '禁用'}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 font-medium">{command.description}</p>
                            <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">使用次数: {command.usage}</p>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <button
                              onClick={() => setEditingCommand(command)}
                              className="p-2 text-gray-400 hover:text-yellow-500 hover:bg-yellow-50 dark:hover:bg-yellow-900/20 rounded-lg transition-colors"
                              title="编辑指令"
                            >
                              <icons.Edit className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(command)}
                              className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                              title="删除指令"
                            >
                              <icons.Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-gray-500 dark:text-gray-400 font-medium">该分类下暂无指令</p>
                      <button
                        onClick={() => setShowCreateModal(true)}
                        className="mt-2 text-yellow-500 hover:text-yellow-600 font-medium"
                      >
                        创建第一个指令
                      </button>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Create Modal */}
      {showCreateModal && (
        <CreateCommandModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateSubmit}
          isLoading={createMutation.isPending}
        />
      )}

      {/* Edit Modal */}
      {editingCommand && (
        <EditCommandModal
          command={editingCommand}
          onClose={() => setEditingCommand(null)}
          onSubmit={handleEditSubmit}
          isLoading={updateMutation.isPending}
        />
      )}
    </div>
  );
};

// Template blocks for quick insertion
const TEMPLATE_BLOCKS = {
  '1': {
    name: 'Points System Details',
    content: `**💰 Points System**
You currently have {user_points} points, ranked #{user_rank}!

**📊 Statistics:**
• Today's points earned: {today_points}
• Overall ranking: {user_rank}/{total_users}
• Recent point records:
{recent_point_records}`
  },
  '2': {
    name: 'Check-in System',
    content: `**📅 Check-in Successful!**
Congratulations {user_username}, check-in complete!

**🎁 Reward Details:**
• Points earned: +{checkin_points}
• Consecutive days: {consecutive_days} days
• Monthly check-ins: {monthly_checkins} times

{checkin_bonus_message}`
  },
  '3': {
    name: 'Leaderboard System',
    content: `**🏆 Points Leaderboard TOP 10**

{leaderboard_top10}

**📈 Your Ranking Info:**
• Current rank: #{user_rank}
• Current points: {user_points}
• Points needed for next rank: {points_to_next_rank}`
  },
  '4': {
    name: 'Help Information',
    content: `**🤖 Bot Command Help**

**💰 Points Related:**
• \`/points\` - View personal point information
• \`/leaderboard\` - View points leaderboard

**📅 Check-in Related:**
• \`/checkin\` - Daily check-in to earn points

**🏆 Title Related:**
• \`/title\` - View personal title information

**❓ Need Help?**
Contact administrators for more support!`
  },
  '5': {
    name: 'Title System',
    content: `**👑 Title Information**

**🎭 Your Current Title:**
{user_title} {user_title_color}

**📝 Title Description:**
{title_description}

**⭐ Requirements:**
Requires {title_min_points} points or above

**📊 Progress Info:**
• Current points: {user_points}
• Title rank: {title_rank}
• Next title: {next_title_name} (requires {next_title_points} points)`
  }
};

// Render Discord-style preview
const renderDiscordPreview = (content: string) => {
  if (!content) return '';
  
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code style="background:#2f3136;padding:2px 4px;border-radius:3px;">$1</code>')
    .replace(/<@(\w+)>/g, '<span style="color:#5865f2;background:#5865f2;border-radius:3px;padding:0 2px;">@用户</span>')
    .replace(/<#(\w+)>/g, '<span style="color:#5865f2;">#频道</span>')
    .replace(/\n/g, '<br>');
};

// Create Command Modal Component
const CreateCommandModal: React.FC<{
  onClose: () => void;
  onSubmit: (data: any) => void;
  isLoading: boolean;
}> = ({ onClose, onSubmit, isLoading }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'system',
    response: '',
    cooldown: 0,
    permissions: 'everyone',
    isEnabled: true
  });

  const [showPreview, setShowPreview] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const insertTemplate = (blockNumber: string) => {
    const template = TEMPLATE_BLOCKS[blockNumber as keyof typeof TEMPLATE_BLOCKS];
    if (template) {
      setFormData({...formData, response: formData.response + template.content});
    }
  };

  const insertMarkdown = (type: string) => {
    const textarea = document.querySelector('textarea[name="response"]') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = formData.response.substring(start, end);
    let newText = formData.response;

    switch (type) {
      case 'bold':
        newText = formData.response.substring(0, start) + `**${selectedText || '粗体文本'}**` + formData.response.substring(end);
        break;
      case 'italic':
        newText = formData.response.substring(0, start) + `*${selectedText || '斜体文本'}*` + formData.response.substring(end);
        break;
      case 'code':
        newText = formData.response.substring(0, start) + `\`${selectedText || '代码'}\`` + formData.response.substring(end);
        break;
      case 'mention':
        newText = formData.response.substring(0, start) + `<@{user_id}>` + formData.response.substring(end);
        break;
      case 'channel':
        newText = formData.response.substring(0, start) + `<#{channel_id}>` + formData.response.substring(end);
        break;
      case 'timestamp':
        newText = formData.response.substring(0, start) + `{timestamp}` + formData.response.substring(end);
        break;
    }

    setFormData({...formData, response: newText});
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-black text-gray-900 dark:text-white">创建 Slash 指令</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg transition-colors"
            >
              <icons.X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left side - Basic info */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">指令名称</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                    placeholder="例如: 签到"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">指令描述</label>
                  <input
                    type="text"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                    placeholder="简要描述指令功能"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                  >
                    <option value="points">积分系统</option>
                    <option value="entertainment">娱乐功能</option>
                    <option value="system">系统指令</option>
                    <option value="lottery">抽奖系统</option>
                    <option value="management">管理功能</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">冷却时间 (秒)</label>
                  <input
                    type="number"
                    value={formData.cooldown}
                    onChange={(e) => setFormData({...formData, cooldown: parseInt(e.target.value) || 0})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">权限要求</label>
                  <select
                    value={formData.permissions}
                    onChange={(e) => setFormData({...formData, permissions: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                  >
                    <option value="everyone">所有人</option>
                    <option value="moderator">版主及以上</option>
                    <option value="admin">管理员</option>
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isEnabled"
                    checked={formData.isEnabled}
                    onChange={(e) => setFormData({...formData, isEnabled: e.target.checked})}
                    className="rounded border-gray-300 text-yellow-500 focus:ring-yellow-500"
                  />
                  <label htmlFor="isEnabled" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    启用指令
                  </label>
                </div>
              </div>

              {/* Right side - Response content */}
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">响应内容</label>
                    <div className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => setShowPreview(!showPreview)}
                        className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded"
                      >
                        {showPreview ? '编辑' : '预览'}
                      </button>
                    </div>
                  </div>

                  {/* Formatting toolbar */}
                  <div className="flex flex-wrap gap-1 p-2 bg-gray-50 dark:bg-gray-700 rounded-t-lg border border-gray-300 dark:border-gray-600 border-b-0">
                    <button type="button" onClick={() => insertMarkdown('bold')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="粗体">
                      <strong>B</strong>
                    </button>
                    <button type="button" onClick={() => insertMarkdown('italic')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="斜体">
                      <em>I</em>
                    </button>
                    <button type="button" onClick={() => insertMarkdown('code')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="代码">
                      {'</>'}
                    </button>
                    <button type="button" onClick={() => insertMarkdown('mention')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="提及用户">
                      @
                    </button>
                    <button type="button" onClick={() => insertMarkdown('channel')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="频道">
                      #
                    </button>
                    <button type="button" onClick={() => insertMarkdown('timestamp')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="时间戳">
                      T
                    </button>
                  </div>

                  {showPreview ? (
                    <div 
                      className="w-full h-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-b-lg dark:bg-gray-700 dark:text-white overflow-auto bg-gray-50 dark:bg-gray-800"
                      dangerouslySetInnerHTML={{ __html: renderDiscordPreview(formData.response) }}
                    />
                  ) : (
                    <div className="relative">
                      <textarea
                        name="response"
                        value={formData.response}
                        onChange={(e) => {
                          const value = e.target.value;
                          // Auto-detect template numbers in the text
                          const processedValue = value.replace(/(?:^|\s)([1-5])(?:\s|$)/g, (match, num) => {
                            const template = TEMPLATE_BLOCKS[num as keyof typeof TEMPLATE_BLOCKS];
                            return template ? ` ${template.content} ` : match;
                          });
                          setFormData({...formData, response: processedValue});
                        }}
                        
                        className="w-full h-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-b-lg dark:bg-gray-700 dark:text-white resize-none"
                        placeholder="输入指令响应内容...输入数字1-5快速插入模板，或输入{显示变量提示"
                      />
                      
                      {/* Variable suggestions tooltip */}
                      <div className="absolute top-2 right-2 z-10">
                        <div className="bg-black/80 text-white text-xs px-2 py-1 rounded opacity-75">
                          输入数字显示模板
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Template buttons */}
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">快速模板</p>
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(TEMPLATE_BLOCKS).map(([key, template]) => (
                      <button
                        key={key}
                        type="button"
                        onClick={() => insertTemplate(key)}
                        className="text-left p-2 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      >
                        <div className="flex items-center space-x-2">
                          <span className="w-6 h-6 bg-yellow-400 text-black rounded-full flex items-center justify-center text-xs font-bold">
                            {key}
                          </span>
                          <span className="text-sm font-medium">{template.name}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    点击模板插入预设内容，或手动输入数字代码（如: 1、2、3、4、5）
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-6 py-2 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black rounded-lg disabled:opacity-50 transition-all duration-200 font-medium"
              >
                {isLoading ? '创建中...' : '创建指令'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

// Edit Command Modal Component
const EditCommandModal: React.FC<{
  command: SlashCommand;
  onClose: () => void;
  onSubmit: (data: any) => void;
  isLoading: boolean;
}> = ({ command, onClose, onSubmit, isLoading }) => {
  const [formData, setFormData] = useState({
    name: command.name,
    description: command.description,
    category: command.category,
    response: command.response || '',
    cooldown: command.cooldown,
    permissions: command.permissions,
    isEnabled: command.isEnabled
  });

  const [showPreview, setShowPreview] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const insertTemplate = (blockNumber: string) => {
    const template = TEMPLATE_BLOCKS[blockNumber as keyof typeof TEMPLATE_BLOCKS];
    if (template) {
      setFormData({...formData, response: formData.response + template.content});
    }
  };

  const insertMarkdown = (type: string) => {
    const textarea = document.querySelector('textarea[name="edit-response"]') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = formData.response.substring(start, end);
    let newText = formData.response;

    switch (type) {
      case 'bold':
        newText = formData.response.substring(0, start) + `**${selectedText || '粗体文本'}**` + formData.response.substring(end);
        break;
      case 'italic':
        newText = formData.response.substring(0, start) + `*${selectedText || '斜体文本'}*` + formData.response.substring(end);
        break;
      case 'code':
        newText = formData.response.substring(0, start) + `\`${selectedText || '代码'}\`` + formData.response.substring(end);
        break;
      case 'mention':
        newText = formData.response.substring(0, start) + `<@{user_id}>` + formData.response.substring(end);
        break;
      case 'channel':
        newText = formData.response.substring(0, start) + `<#{channel_id}>` + formData.response.substring(end);
        break;
      case 'timestamp':
        newText = formData.response.substring(0, start) + `{timestamp}` + formData.response.substring(end);
        break;
    }

    setFormData({...formData, response: newText});
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-black text-gray-900 dark:text-white">编辑指令: /{command.name}</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg transition-colors"
            >
              <icons.X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Left side - Basic info */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">指令名称</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                    placeholder="例如: 签到"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">指令描述</label>
                  <input
                    type="text"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                    placeholder="简要描述指令功能"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData({...formData, category: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                  >
                    <option value="points">积分系统</option>
                    <option value="entertainment">娱乐功能</option>
                    <option value="system">系统指令</option>
                    <option value="lottery">抽奖系统</option>
                    <option value="management">管理功能</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">冷却时间 (秒)</label>
                  <input
                    type="number"
                    value={formData.cooldown}
                    onChange={(e) => setFormData({...formData, cooldown: parseInt(e.target.value) || 0})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">权限要求</label>
                  <select
                    value={formData.permissions}
                    onChange={(e) => setFormData({...formData, permissions: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"
                  >
                    <option value="everyone">所有人</option>
                    <option value="moderator">版主及以上</option>
                    <option value="admin">管理员</option>
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isEnabled-edit"
                    checked={formData.isEnabled}
                    onChange={(e) => setFormData({...formData, isEnabled: e.target.checked})}
                    className="rounded border-gray-300 text-yellow-500 focus:ring-yellow-500"
                  />
                  <label htmlFor="isEnabled-edit" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    启用指令
                  </label>
                </div>
              </div>

              {/* Right side - Response content */}
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">响应内容</label>
                    <div className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => setShowPreview(!showPreview)}
                        className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded"
                      >
                        {showPreview ? '编辑' : '预览'}
                      </button>
                    </div>
                  </div>

                  {/* Formatting toolbar */}
                  <div className="flex flex-wrap gap-1 p-2 bg-gray-50 dark:bg-gray-700 rounded-t-lg border border-gray-300 dark:border-gray-600 border-b-0">
                    <button type="button" onClick={() => insertMarkdown('bold')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="粗体">
                      <strong>B</strong>
                    </button>
                    <button type="button" onClick={() => insertMarkdown('italic')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="斜体">
                      <em>I</em>
                    </button>
                    <button type="button" onClick={() => insertMarkdown('code')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="代码">
                      {'</>'}
                    </button>
                    <button type="button" onClick={() => insertMarkdown('mention')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="提及用户">
                      @
                    </button>
                    <button type="button" onClick={() => insertMarkdown('channel')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="频道">
                      #
                    </button>
                    <button type="button" onClick={() => insertMarkdown('timestamp')} className="px-2 py-1 text-xs bg-white dark:bg-gray-600 rounded hover:bg-gray-100 dark:hover:bg-gray-500" title="时间戳">
                      T
                    </button>
                  </div>

                  {showPreview ? (
                    <div 
                      className="w-full h-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-b-lg dark:bg-gray-700 dark:text-white overflow-auto bg-gray-50 dark:bg-gray-800"
                      dangerouslySetInnerHTML={{ __html: renderDiscordPreview(formData.response) }}
                    />
                  ) : (
                    <div className="relative">
                      <textarea
                        name="edit-response"
                        value={formData.response}
                        onChange={(e) => {
                          const value = e.target.value;
                          // Auto-detect template numbers in the text
                          const processedValue = value.replace(/(?:^|\s)([1-5])(?:\s|$)/g, (match, num) => {
                            const template = TEMPLATE_BLOCKS[num as keyof typeof TEMPLATE_BLOCKS];
                            return template ? ` ${template.content} ` : match;
                          });
                          setFormData({...formData, response: processedValue});
                        }}
                        
                        className="w-full h-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-b-lg dark:bg-gray-700 dark:text-white resize-none"
                        placeholder="输入指令响应内容...输入数字1-5快速插入模板，或输入{显示变量提示"
                      />
                      
                      {/* Variable suggestions tooltip */}
                      <div className="absolute top-2 right-2 z-10">
                        <div className="bg-black/80 text-white text-xs px-2 py-1 rounded opacity-75">
                          输入数字显示模板
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Template buttons */}
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">快速模板</p>
                  <div className="grid grid-cols-1 gap-2">
                    {Object.entries(TEMPLATE_BLOCKS).map(([key, template]) => (
                      <button
                        key={key}
                        type="button"
                        onClick={() => insertTemplate(key)}
                        className="text-left p-2 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                      >
                        <div className="flex items-center space-x-2">
                          <span className="w-6 h-6 bg-yellow-400 text-black rounded-full flex items-center justify-center text-xs font-bold">
                            {key}
                          </span>
                          <span className="text-sm font-medium">{template.name}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    点击模板插入预设内容，或手动输入数字代码（如: 1、2、3、4、5）
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-6 py-2 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black rounded-lg disabled:opacity-50 transition-all duration-200 font-medium"
              >
                {isLoading ? '更新中...' : '更新指令'}
              </button>
            </div>
          </form>
        </div>
      </div>

    </div>
  );
};

export default SlashCommands;