import React, { useState } from 'react';
import * as icons from 'lucide-react';
import { useNotification } from '../contexts/NotificationContext';

const TitleSystem: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isUsersModalOpen, setIsUsersModalOpen] = useState(false);
  const [selectedTitle, setSelectedTitle] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const { showNotification } = useNotification();

  // 获取头衔数据 - 直接fetch调用，避免React Query缓存问题
  const [titles, setTitles] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);

  // 直接获取数据
  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/titles');
      const data = await response.json();
      if (data.success) {
        setTitles(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching titles:', error);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users');
      const data = await response.json();
      if (data.success) {
        setUsers(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  // 初始化数据
  React.useEffect(() => {
    fetchTitles();
    fetchUsers();
  }, []);

  // 强制刷新数据
  const refreshData = () => {
    fetchTitles();
    fetchUsers();
  };

  // 同步头衔到Discord
  const syncTitlesToDiscord = async () => {
    try {
      const response = await fetch('/api/titles/sync-discord', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (response.ok) {
        const result = await response.json();
        showNotification(result.message || '头衔同步成功！', 'success');
      } else {
        showNotification('同步头衔失败！', 'error');
      }
    } catch (error) {
      console.error('Sync titles error:', error);
      showNotification('同步头衔失败！', 'error');
    }
  };

  // 获取用户的Discord角色作为头衔（不再基于积分）
  const getUserDiscordTitles = (user: any) => {
    if (user.titles && Array.isArray(user.titles) && user.titles.length > 0) {
      return user.titles;
    }
    if (user.roles && Array.isArray(user.roles) && user.roles.length > 0) {
      return user.roles;
    }
    return ['无头衔'];
  };

  // 获取某个头衔的用户列表（基于Discord角色）
  const getUsersByTitle = (titleName: string) => {
    return users.filter((user: any) => {
      const userTitles = getUserDiscordTitles(user);
      return userTitles.includes(titleName);
    });
  };

  // 处理后的头衔数据，包含用户数量（按用户数量排序，不再按积分）
  const processedTitles = titles.map((title: any) => ({
    ...title,
    users: getUsersByTitle(title.name).length
  })).sort((a: any, b: any) => b.users - a.users); // 按用户数量从高到低排序

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    minPoints: 0,
    maxPoints: '',
    color: '#D6F36F',
    icon: '',
    isActive: true
  });

  const [editFormData, setEditFormData] = useState({
    name: '',
    description: '',
    minPoints: 0,
    maxPoints: '',
    color: '#D6F36F',
    icon: '',
    isActive: true
  });

  const handleCreateTitle = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/titles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          maxPoints: formData.maxPoints ? parseInt(formData.maxPoints) : null
        })
      });

      if (response.ok) {
        setOperationResult({ type: 'success', message: '头衔创建成功！' });
        setIsModalOpen(false);
        setFormData({
          name: '',
          description: '',
          minPoints: 0,
          maxPoints: '',
          color: '#D6F36F',
          icon: '',
          isActive: true
        });
        // 立即刷新数据
        refreshData();
      } else {
        setOperationResult({ type: 'error', message: '创建头衔失败！' });
      }
    } catch (error) {
      setOperationResult({ type: 'error', message: '创建头衔失败！' });
    }

    setIsLoading(false);
    setTimeout(() => setOperationResult(null), 3000);
  };

  const handleEditTitle = (title: any) => {
    setSelectedTitle(title);
    setEditFormData({
      name: title.name,
      description: title.description,
      minPoints: title.minPoints,
      maxPoints: title.maxPoints ? String(title.maxPoints) : '',
      color: title.color,
      icon: title.icon,
      isActive: title.isActive
    });
    setIsEditModalOpen(true);
  };

  const handleUpdateTitle = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedTitle) return;

    setIsLoading(true);

    try {
      const response = await fetch(`/api/titles/${selectedTitle.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...editFormData,
          maxPoints: editFormData.maxPoints ? parseInt(editFormData.maxPoints) : null
        })
      });

      if (response.ok) {
        setOperationResult({ type: 'success', message: '头衔更新成功！' });
        setIsEditModalOpen(false);
        refreshData();
      } else {
        setOperationResult({ type: 'error', message: '更新头衔失败！' });
      }
    } catch (error) {
      setOperationResult({ type: 'error', message: '更新头衔失败！' });
    }

    setIsLoading(false);
    setTimeout(() => setOperationResult(null), 3000);
  };

  const handleDeleteTitle = async (title: any) => {
    if (!confirm(`确定要删除头衔 "${title.name}" 吗？`)) return;

    try {
      const response = await fetch(`/api/titles/${title.id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        showNotification(`头衔 "${title.name}" 已成功删除！`, 'success');
        refreshData();
      } else {
        showNotification('删除头衔失败！', 'error');
      }
    } catch (error) {
      showNotification('删除头衔失败！', 'error');
    }
  };

  const handleViewTitleUsers = (title: any) => {
    setSelectedTitle(title);
    setIsUsersModalOpen(true);
  };

  const handleSyncFromDiscord = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/titles/sync-from-discord', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      
      const result = await response.json();
      if (result.success) {
        setOperationResult({ type: 'success', message: result.message });
        refreshData();
      } else {
        setOperationResult({ type: 'error', message: result.message || '同步失败' });
      }
    } catch (error) {
      setOperationResult({ type: 'error', message: '从Discord同步角色失败' });
    }
    setIsLoading(false);
    setTimeout(() => setOperationResult(null), 3000);
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">头衔系统</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">管理用户头衔等级和晋升条件</p>
          <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
            共 {processedTitles.length} 个头衔 | {users.length} 个用户
          </p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={handleSyncFromDiscord}
            disabled={isLoading}
            className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-purple-500/25 font-medium disabled:opacity-50"
          >
            <icons.Download className="w-4 h-4" />
            <span>{isLoading ? '同步中...' : '从Discord同步'}</span>
          </button>
          <button 
            onClick={refreshData}
            className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-gray-500/25 font-medium"
          >
            <icons.RefreshCw className="w-4 h-4" />
            <span>刷新</span>
          </button>
          <button 
            onClick={syncTitlesToDiscord}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-blue-500/25 font-medium"
          >
            <icons.Globe className="w-4 h-4" />
            <span>同步Discord</span>
          </button>
          <button 
            onClick={() => setIsModalOpen(true)}
            className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-yellow-500/25 font-medium"
          >
            <icons.Plus className="w-4 h-4" />
            <span>创建头衔</span>
          </button>
        </div>
      </div>

      {/* 操作结果提示 */}
      {operationResult && (
        <div className={`p-4 rounded-xl border ${
          operationResult.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {operationResult.type === 'success' ? (
              <icons.CheckCircle className="w-5 h-5" />
            ) : (
              <icons.AlertCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{operationResult.message}</span>
          </div>
        </div>
      )}

      {/* 头衔网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {processedTitles.map((title: any) => (
          <div 
            key={title.id} 
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md dark:hover:shadow-gray-900/50 transition-all duration-200"
          >
            {/* 头衔头部 */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div 
                  className="w-12 h-12 rounded-xl flex items-center justify-center text-2xl shadow-lg"
                  style={{ backgroundColor: title.color }}
                >
                  {title.icon || '🏆'}
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">{title.name}</h3>
                  <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                    <icons.Users className="w-4 h-4" />
                    <span>{title.users} 用户</span>
                  </div>
                </div>
              </div>
              
              {/* 操作按钮 */}
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => handleViewTitleUsers(title)}
                  className="p-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                  title="查看用户"
                >
                  <icons.Eye className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleEditTitle(title)}
                  className="p-2 text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                  title="编辑头衔"
                >
                  <icons.Edit2 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDeleteTitle(title)}
                  className="p-2 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                  title="删除头衔"
                >
                  <icons.Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* 头衔描述 */}
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">{title.description}</p>

            {/* 积分要求 */}
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-3 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">积分要求:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {title.minPoints} - {title.maxPoints || '∞'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">状态:</span>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  title.isActive 
                    ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400' 
                    : 'bg-gray-100 dark:bg-gray-900/20 text-gray-800 dark:text-gray-400'
                }`}>
                  {title.isActive ? '启用' : '禁用'}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 创建头衔模态框 */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">创建新头衔</h3>
              <button 
                onClick={() => setIsModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleCreateTitle} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔名称</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">描述</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最低积分</label>
                  <input
                    type="number"
                    value={formData.minPoints}
                    onChange={(e) => setFormData({...formData, minPoints: parseInt(e.target.value) || 0})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最高积分</label>
                  <input
                    type="number"
                    value={formData.maxPoints}
                    onChange={(e) => setFormData({...formData, maxPoints: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="留空表示无上限"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">颜色</label>
                  <input
                    type="color"
                    value={formData.color}
                    onChange={(e) => setFormData({...formData, color: e.target.value})}
                    className="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">图标</label>
                  <input
                    type="text"
                    value={formData.icon}
                    onChange={(e) => setFormData({...formData, icon: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="🏆"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-lg shadow-yellow-500/25 disabled:opacity-50"
                >
                  {isLoading ? '创建中...' : '创建头衔'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 编辑头衔模态框 */}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">编辑头衔</h3>
              <button 
                onClick={() => setIsEditModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleUpdateTitle} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">头衔名称</label>
                <input
                  type="text"
                  value={editFormData.name}
                  onChange={(e) => setEditFormData({...editFormData, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">描述</label>
                <textarea
                  value={editFormData.description}
                  onChange={(e) => setEditFormData({...editFormData, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最低积分</label>
                  <input
                    type="number"
                    value={editFormData.minPoints}
                    onChange={(e) => setEditFormData({...editFormData, minPoints: parseInt(e.target.value) || 0})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">最高积分</label>
                  <input
                    type="number"
                    value={editFormData.maxPoints}
                    onChange={(e) => setEditFormData({...editFormData, maxPoints: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="留空表示无上限"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">颜色</label>
                  <input
                    type="color"
                    value={editFormData.color}
                    onChange={(e) => setEditFormData({...editFormData, color: e.target.value})}
                    className="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">图标</label>
                  <input
                    type="text"
                    value={editFormData.icon}
                    onChange={(e) => setEditFormData({...editFormData, icon: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="🏆"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsEditModalOpen(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-lg shadow-green-500/25 disabled:opacity-50"
                >
                  {isLoading ? '更新中...' : '更新头衔'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 查看用户模态框 */}
      {isUsersModalOpen && selectedTitle && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-2xl shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                {selectedTitle.name} - 用户列表
              </h3>
              <button 
                onClick={() => setIsUsersModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-3 max-h-96 overflow-y-auto">
              {getUsersByTitle(selectedTitle.name).map((user: any) => (
                <div key={user.id} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                  <img 
                    src={user.avatar || `https://api.dicebear.com/7.x/initials/svg?seed=${user.username}`}
                    alt={user.username}
                    className="w-10 h-10 rounded-full"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 dark:text-white">{user.username}</h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{user.points || 0} 积分</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {new Date(user.joinDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
              {getUsersByTitle(selectedTitle.name).length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  暂无用户拥有此头衔
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TitleSystem;