import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import * as icons from 'lucide-react';

const ActivitySystem: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedActivityType, setSelectedActivityType] = useState('all');
  const [selectedDateRange, setSelectedDateRange] = useState('all');
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);

  const queryClient = useQueryClient();

  // Fetch activity records
  const { data: activityRecordsResponse, isLoading: recordsLoading } = useQuery({
    queryKey: ['/api/activity-records'],
    queryFn: async () => {
      const response = await fetch('/api/activity-records');
      if (!response.ok) throw new Error('Failed to fetch activity records');
      return response.json();
    }
  });

  // Fetch activity statistics
  const { data: activityStatsResponse } = useQuery({
    queryKey: ['/api/activity/stats'],
    queryFn: async () => {
      const response = await fetch('/api/activity/stats');
      if (!response.ok) throw new Error('Failed to fetch activity stats');
      return response.json();
    }
  });

  // Fetch Discord users
  const { data: discordUsersResponse } = useQuery({
    queryKey: ['/api/discord-users'],
    queryFn: async () => {
      const response = await fetch('/api/discord-users');
      if (!response.ok) throw new Error('Failed to fetch Discord users');
      return response.json();
    }
  });

  const activityRecords = activityRecordsResponse?.data || [];
  const activityStats = activityStatsResponse?.data || {
    totalActivities: 0,
    totalPoints: 0,
    todayActivities: 0,
    activeUsers: 0
  };
  const discordUsers = discordUsersResponse?.data || [];

  // Filter records
  const filteredRecords = activityRecords.filter((record: any) => {
    const matchesSearch = record.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.user?.username?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedActivityType === 'all' || record.activityType === selectedActivityType;
    
    let matchesDate = true;
    if (selectedDateRange !== 'all') {
      const recordDate = new Date(record.createdAt);
      const now = new Date();
      if (selectedDateRange === '今天') {
        matchesDate = recordDate.toDateString() === now.toDateString();
      } else if (selectedDateRange === '本周') {
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        matchesDate = recordDate >= weekAgo;
      } else if (selectedDateRange === '本月') {
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        matchesDate = recordDate >= monthAgo;
      }
    }
    
    return matchesSearch && matchesType && matchesDate;
  });

  // Activity type mapping
  const getActivityTypeLabel = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'message': '发送消息',
      'voice': '语音活动',
      'reaction': '添加反应',
      'command': '使用命令',
      'checkin': '每日签到',
      'code_redemption': '兑换码使用'
    };
    return typeMap[type] || type;
  };

  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case 'message': return icons.MessageSquare;
      case 'voice': return icons.Mic;
      case 'reaction': return icons.Heart;
      case 'command': return icons.Zap;
      case 'checkin': return icons.CheckCircle;
      case 'code_redemption': return icons.Gift;
      default: return icons.Bell;
    }
  };

  // Calculate activity distribution from records
  const getActivityDistribution = () => {
    const userActivityMap = new Map();
    
    activityRecords.forEach((record: any) => {
      const username = record.user?.username || `用户${record.userId}`;
      if (!userActivityMap.has(username)) {
        userActivityMap.set(username, {
          username,
          avatar: record.user?.avatar,
          totalPoints: 0,
          activityCount: 0
        });
      }
      const userData = userActivityMap.get(username);
      userData.totalPoints += record.points;
      userData.activityCount += 1;
    });

    return Array.from(userActivityMap.values())
      .sort((a, b) => b.totalPoints - a.totalPoints)
      .slice(0, 5);
  };

  const activityDistribution = getActivityDistribution();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">活跃度系统</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">管理和跟踪用户活跃度记录</p>
          </div>
          <div className="flex items-center space-x-3">
            <button 
              onClick={() => {
                queryClient.invalidateQueries({ queryKey: ['/api/activity-records'] });
                queryClient.invalidateQueries({ queryKey: ['/api/activity/stats'] });
                queryClient.invalidateQueries({ queryKey: ['/api/discord-users'] });
                setOperationResult({ type: 'success', message: '数据同步成功！' });
                setTimeout(() => setOperationResult(null), 2000);
              }}
              className="bg-gray-800 hover:bg-gray-900 text-white px-4 py-2 rounded-xl transition-colors flex items-center space-x-2 shadow-lg shadow-gray-900/25"
            >
              <icons.RefreshCw className="w-4 h-4" />
              <span>同步数据</span>
            </button>
          </div>
        </div>

        {/* 操作结果提示 */}
        {operationResult && (
          <div className={`p-4 rounded-xl border mb-6 ${
            operationResult.type === 'success' 
              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
              : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
          }`}>
            <div className="flex items-center space-x-2">
              {operationResult.type === 'success' ? (
                <icons.CheckCircle className="w-5 h-5" />
              ) : (
                <icons.AlertCircle className="w-5 h-5" />
              )}
              <span className="font-medium">{operationResult.message}</span>
            </div>
          </div>
        )}

        {/* 网格布局 */}
        <div className="grid grid-cols-12 gap-6">
          {/* 左侧统计区域 */}
          <div className="col-span-12 lg:col-span-3 space-y-6">
            {/* 总活跃度统计 */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                  <icons.Activity className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">总活跃度</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{activityStats.totalActivities}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" 
                  style={{ width: activityStats.totalActivities > 0 ? `${Math.min((activityStats.totalActivities / 100) * 100, 100)}%` : '0%' }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">活跃度记录总数</p>
            </div>

            {/* 今日活跃统计 */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                  <icons.Calendar className="w-6 h-6 text-white font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日活跃</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{activityStats.todayActivities}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-blue-400 to-blue-500 h-2 rounded-full" 
                  style={{ width: activityStats.todayActivities > 0 ? `${Math.min((activityStats.todayActivities / 50) * 100, 100)}%` : '0%' }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">今日活跃记录数</p>
            </div>

            {/* 活跃积分池 */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Award className="w-6 h-6 text-black font-bold" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">活跃积分</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{activityStats.totalPoints}</p>
                </div>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" 
                  style={{ width: activityStats.totalPoints > 0 ? `${Math.min((activityStats.totalPoints / 100) * 100, 100)}%` : '0%' }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">活跃获得积分总数</p>
            </div>

            {/* 活跃度分布 */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">活跃度分布</h3>
              <div className="space-y-3">
                {activityDistribution.map((user: any, index: number) => (
                  <div key={user.username} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm ${
                        index === 0 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25' : 
                        index === 1 ? 'bg-gradient-to-br from-gray-400 to-gray-500' : 
                        index === 2 ? 'bg-gradient-to-br from-orange-400 to-orange-500' : 'bg-gradient-to-br from-gray-600 to-gray-700'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white text-sm">{user.username}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">{user.totalPoints} 积分</div>
                      </div>
                    </div>
                    {user.avatar && (
                      <img src={user.avatar} alt={user.username} className="w-8 h-8 rounded-lg object-cover" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 中间主要内容区域 */}
          <div className="col-span-12 lg:col-span-6 space-y-6">
            {/* 搜索和筛选 */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <icons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="搜索用户或活动描述..."
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex space-x-2">
                  <select 
                    className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    value={selectedActivityType}
                    onChange={(e) => setSelectedActivityType(e.target.value)}
                  >
                    <option value="all">所有活动</option>
                    <option value="message">发送消息</option>
                    <option value="voice">语音活动</option>
                    <option value="reaction">添加反应</option>
                    <option value="command">使用命令</option>
                    <option value="checkin">每日签到</option>
                    <option value="code_redemption">兑换码使用</option>
                  </select>
                  <select 
                    className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    value={selectedDateRange}
                    onChange={(e) => setSelectedDateRange(e.target.value)}
                  >
                    <option value="all">全部</option>
                    <option value="今天">今天</option>
                    <option value="本周">本周</option>
                    <option value="本月">本月</option>
                  </select>
                </div>
              </div>
            </div>

            {/* 活跃度记录列表 */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">活跃度记录</h3>
                  <span className="text-sm text-gray-500 dark:text-gray-400">共 {filteredRecords.length} 条记录</span>
                </div>
                {recordsLoading ? (
                  <div className="flex items-center justify-center p-8">
                    <icons.Loader2 className="h-8 w-8 animate-spin text-yellow-600 mr-3" />
                    <span className="text-gray-600 dark:text-gray-400">加载活跃度记录...</span>
                  </div>
                ) : filteredRecords.length === 0 ? (
                  <div className="text-center p-8">
                    <icons.Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无活跃度记录</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">还没有任何活跃度记录</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredRecords.map((record: any) => {
                      const ActivityIcon = getActivityTypeIcon(record.activityType);
                      return (
                        <div key={record.id} className="border border-gray-200 dark:border-gray-600 rounded-xl p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className="relative">
                                {record.user?.avatar ? (
                                  <img src={record.user.avatar} alt={record.user.username} className="w-12 h-12 rounded-xl object-cover" />
                                ) : (
                                  <div className="w-12 h-12 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center shadow-lg">
                                    <span className="text-white font-bold text-sm">
                                      {record.user?.username?.charAt(0) || 'U'}
                                    </span>
                                  </div>
                                )}
                                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center shadow-lg">
                                  <ActivityIcon className="w-3 h-3 text-white" />
                                </div>
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center space-x-3 mb-1">
                                  <div className="font-bold text-gray-900 dark:text-white">{record.user?.username || `用户${record.userId}`}</div>
                                  <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25">
                                    +{record.points} 积分
                                  </span>
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                  <span className="font-medium">活动：</span>{record.description}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                                  <span className="font-medium">类型：</span>{getActivityTypeLabel(record.activityType)}
                                  {record.channelName && <span className="ml-2">频道：#{record.channelName}</span>}
                                </div>
                                <div className="text-xs text-gray-500 dark:text-gray-500">
                                  {record.createdAt ? new Date(record.createdAt).toLocaleString('zh-CN') : 'N/A'}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 右侧操作面板 */}
          <div className="col-span-12 lg:col-span-3 space-y-6">
            {/* 快捷操作 */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快捷操作</h3>
              <div className="space-y-3">
                <button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-blue-500/25">
                  <icons.FileText className="w-4 h-4" />
                  <span>活跃度模板</span>
                </button>
                <button className="w-full bg-gradient-to-r from-purple-500 to-purple-600 text-white px-4 py-3 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-purple-500/25">
                  <icons.Users className="w-4 h-4" />
                  <span>批量管理</span>
                </button>
                <button className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-3 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-green-500/25">
                  <icons.Download className="w-4 h-4" />
                  <span>导出记录</span>
                </button>
                <button className="w-full bg-gradient-to-r from-gray-500 to-gray-600 text-white px-4 py-3 rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-gray-500/25">
                  <icons.Settings className="w-4 h-4" />
                  <span>活跃度设置</span>
                </button>
              </div>
            </div>

            {/* 活跃度排行榜 */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                  <icons.Trophy className="w-5 h-5 text-black font-bold" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">活跃度排行</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">实时排名</p>
                </div>
              </div>
              
              <div className="space-y-3">
                {activityDistribution.map((user: any, index: number) => (
                  <div key={user.username} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm ${
                        index === 0 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25' : 
                        index === 1 ? 'bg-gradient-to-br from-gray-400 to-gray-500' : 
                        index === 2 ? 'bg-gradient-to-br from-orange-400 to-orange-500' : 'bg-gradient-to-br from-gray-600 to-gray-700'
                      }`}>
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900 dark:text-white text-sm">{user.username}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">{user.activityCount} 次活跃</div>
                      </div>
                    </div>
                    {user.avatar && (
                      <img src={user.avatar} alt={user.username} className="w-8 h-8 rounded-lg object-cover" />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivitySystem;