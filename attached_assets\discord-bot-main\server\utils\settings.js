import { getDb } from '../database/init.js';
import { logger } from './logger.js';

export async function getSetting(key) {
  try {
    const db = getDb();
    const result = await db.get('SELECT value FROM settings WHERE key = ?', [key]);
    return result?.value || null;
  } catch (error) {
    logger.error(`Error getting setting ${key}:`, error);
    return null;
  }
}

export async function setSetting(key, value) {
  try {
    const db = getDb();
    await db.run(`
      INSERT OR REPLACE INTO settings (key, value, updated_at)
      VALUES (?, ?, ?)
    `, [key, value, new Date().toISOString()]);
    
    logger.info(`Setting updated: ${key}`);
    return true;
  } catch (error) {
    logger.error(`Error setting ${key}:`, error);
    return false;
  }
}

export async function getAllSettings() {
  try {
    const db = getDb();
    const settings = await db.all('SELECT * FROM settings');
    return settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {});
  } catch (error) {
    logger.error('Error getting all settings:', error);
    return {};
  }
}