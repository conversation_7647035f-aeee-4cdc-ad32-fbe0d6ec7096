import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';

const app = express();
const PORT = process.env.PORT || 3001;

// 基础中间件
app.use(cors({
  origin: true,
  credentials: true
}));

app.use(express.json());

// 简单的路由
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

app.get('/api/settings', (req, res) => {
  res.json({
    success: true,
    data: {
      bot_token: '',
      bot_prefix: '!',
      checkin_points: '10',
      message_points: '1',
      invite_points: '50',
      auto_backup: 'true',
      error_notifications: 'true',
      debug_mode: 'false'
    }
  });
});

app.post('/api/bot/test-token', (req, res) => {
  const { token } = req.body;
  
  if (!token) {
    return res.status(400).json({
      success: false,
      message: 'Token is required'
    });
  }

  // 模拟测试结果
  res.json({
    success: true,
    message: 'Token is valid',
    data: {
      id: '123456789',
      username: 'TestBot',
      discriminator: '0001',
      avatar: null,
      guilds: 1,
      users: 100
    }
  });
});

app.post('/api/bot/start', (req, res) => {
  res.json({
    success: true,
    message: 'Bot started successfully'
  });
});

app.post('/api/bot/stop', (req, res) => {
  res.json({
    success: true,
    message: 'Bot stopped successfully'
  });
});

app.post('/api/bot/restart', (req, res) => {
  res.json({
    success: true,
    message: 'Bot restarted successfully'
  });
});

app.get('/api/bot/status', (req, res) => {
  res.json({
    success: true,
    data: {
      connected: true,
      ready: true,
      latency: 45,
      uptime: 3600000,
      guilds: 1,
      users: 100,
      error: null
    }
  });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

const httpServer = createServer(app);

// WebSocket 服务器
const wss = new WebSocketServer({ 
  server: httpServer,
  path: '/'
});

let clients = new Set();

wss.on('connection', (ws, req) => {
  console.log('WebSocket client connected');
  clients.add(ws);

  // 发送初始数据
  const initialData = {
    type: 'initial_data',
    data: {
      botStatus: {
        connected: true,
        ready: true,
        latency: 45,
        uptime: 3600000,
        guilds: 1,
        users: 100,
        error: null
      },
      serverData: {
        memberCount: 100,
        onlineCount: 45,
        activeChannels: 5,
        channels: [
          { id: '1', name: '一般', type: 0, category: '文字频道' },
          { id: '2', name: '闲聊', type: 0, category: '文字频道' },
          { id: '3', name: '公告', type: 0, category: '文字频道' }
        ],
        roles: [
          { id: '1', name: '成员', color: '#99AAB5', memberCount: 100 },
          { id: '2', name: '管理员', color: '#F04747', memberCount: 5 }
        ]
      },
      stats: {
        userCount: 100,
        todayCheckins: 45,
        recentActiveUsers: 23,
        totalPoints: 15000,
        timestamp: new Date().toISOString()
      }
    }
  };

  ws.send(JSON.stringify(initialData));

  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      
      switch (data.type) {
        case 'ping':
          ws.send(JSON.stringify({ 
            type: 'pong', 
            timestamp: new Date().toISOString() 
          }));
          break;
        case 'request_update':
          ws.send(JSON.stringify(initialData));
          break;
      }
    } catch (error) {
      console.error('WebSocket message error:', error);
    }
  });

  ws.on('close', () => {
    console.log('WebSocket client disconnected');
    clients.delete(ws);
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
    clients.delete(ws);
  });
});

// 定期广播更新
setInterval(() => {
  if (clients.size > 0) {
    const updateData = {
      type: 'real_time_update',
      data: {
        botStatus: {
          connected: true,
          ready: true,
          latency: Math.floor(Math.random() * 100) + 20,
          uptime: Date.now(),
          guilds: 1,
          users: 100,
          error: null
        },
        serverData: {
          memberCount: 100,
          onlineCount: Math.floor(Math.random() * 50) + 30,
          activeChannels: 5
        },
        stats: {
          userCount: 100,
          todayCheckins: Math.floor(Math.random() * 20) + 40,
          recentActiveUsers: Math.floor(Math.random() * 30) + 15,
          totalPoints: 15000 + Math.floor(Math.random() * 1000),
          timestamp: new Date().toISOString()
        }
      }
    };

    const message = JSON.stringify(updateData);
    clients.forEach(client => {
      if (client.readyState === client.OPEN) {
        try {
          client.send(message);
        } catch (error) {
          console.error('Error sending to client:', error);
          clients.delete(client);
        }
      }
    });
  }
}, 10000); // 每10秒更新一次

// 启动服务器
httpServer.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Simple server running at http://0.0.0.0:${PORT}`);
  console.log(`✅ Health check: http://localhost:${PORT}/health`);
  console.log(`✅ WebSocket server ready`);
});

httpServer.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use`);
    process.exit(1);
  } else {
    console.error('❌ Server error:', error);
  }
});

process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  httpServer.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  httpServer.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});