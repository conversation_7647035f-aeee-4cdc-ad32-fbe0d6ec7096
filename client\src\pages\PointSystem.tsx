import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import * as icons from 'lucide-react';

const PointSystem: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [isBatchModalOpen, setIsBatchModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [operationResult, setOperationResult] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const [exportProgress, setExportProgress] = useState(0);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [pointAmount, setPointAmount] = useState<number>(0);
  const [pointReason, setPointReason] = useState('');
  const [isDeductMode, setIsDeductMode] = useState(false);
  const [userPointRecords, setUserPointRecords] = useState<any[]>([]);
  const [userSearchTerm, setUserSearchTerm] = useState('');
  const [showUserDropdown, setShowUserDropdown] = useState(false);

  const queryClient = useQueryClient();

  // Fetch point records
  const { data: pointRecordsResponse, isLoading: recordsLoading } = useQuery({
    queryKey: ['/api/point-records'],
    queryFn: async () => {
      const response = await fetch('/api/point-records');
      if (!response.ok) throw new Error('Failed to fetch point records');
      return response.json();
    }
  });

  // Fetch point statistics
  const { data: pointStatsResponse } = useQuery({
    queryKey: ['/api/points/stats'],
    queryFn: async () => {
      const response = await fetch('/api/points/stats');
      if (!response.ok) throw new Error('Failed to fetch point stats');
      return response.json();
    }
  });

  // Fetch Discord users for points distribution
  const { data: discordUsersResponse } = useQuery({
    queryKey: ['/api/discord-users'],
    queryFn: async () => {
      const response = await fetch('/api/discord-users');
      if (!response.ok) throw new Error('Failed to fetch Discord users');
      return response.json();
    }
  });

  // Add points mutation
  const addPointsMutation = useMutation({
    mutationFn: async (data: { userId: number; amount: number; reason: string }) => {
      const response = await fetch('/api/points/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });
      if (!response.ok) throw new Error('Failed to add points');
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        setOperationResult({ type: 'success', message: data.message });
        setIsModalOpen(false);
        setSelectedUserId(null);
        setPointAmount(0);
        setPointReason('');
        setIsDeductMode(false);
        // Refresh data
        queryClient.invalidateQueries({ queryKey: ['/api/point-records'] });
        queryClient.invalidateQueries({ queryKey: ['/api/points/stats'] });
        queryClient.invalidateQueries({ queryKey: ['/api/discord-users'] });
        setTimeout(() => setOperationResult(null), 3000);
      }
    },
    onError: (error) => {
      setOperationResult({ type: 'error', message: '积分操作失败' });
      setTimeout(() => setOperationResult(null), 3000);
    }
  });

  // Deduct points mutation
  const deductPointsMutation = useMutation({
    mutationFn: async (data: { userId: number; amount: number; reason: string }) => {
      const response = await fetch('/api/points/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: data.userId,
          amount: -Math.abs(data.amount), // Ensure negative amount
          reason: data.reason
        })
      });
      if (!response.ok) throw new Error('Failed to deduct points');
      return response.json();
    },
    onSuccess: (data) => {
      if (data.success) {
        setOperationResult({ type: 'success', message: data.message });
        setIsModalOpen(false);
        setSelectedUserId(null);
        setPointAmount(0);
        setPointReason('');
        setIsDeductMode(false);
        // Refresh data
        queryClient.invalidateQueries({ queryKey: ['/api/point-records'] });
        queryClient.invalidateQueries({ queryKey: ['/api/points/stats'] });
        queryClient.invalidateQueries({ queryKey: ['/api/discord-users'] });
        setTimeout(() => setOperationResult(null), 3000);
      }
    },
    onError: (error) => {
      setOperationResult({ type: 'error', message: '积分扣除失败' });
      setTimeout(() => setOperationResult(null), 3000);
    }
  });

  const pointRecords = pointRecordsResponse?.data || [];
  const pointStats = pointStatsResponse?.data || {
    todayDistribution: 0,
    todayDeduction: 0,
    totalPool: 0,
    pointsDistribution: []
  };
  const discordUsers = discordUsersResponse?.data || [];

  const filteredRecords = pointRecords.filter((record: any) => 
    record.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    record.reason?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Fetch user point records when user is selected
  const fetchUserPointRecords = async (userId: number) => {
    try {
      const response = await fetch(`/api/users/${userId}/point-records`);
      if (response.ok) {
        const data = await response.json();
        setUserPointRecords(data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch user point records:', error);
    }
  };

  const handleUserSelect = (userId: number | null) => {
    setSelectedUserId(userId);
    if (userId) {
      fetchUserPointRecords(userId);
    } else {
      setUserPointRecords([]);
    }
  };

  // Filter users based on search term
  const filteredUsers = discordUsers.filter((user: any) => 
    user.username.toLowerCase().includes(userSearchTerm.toLowerCase()) ||
    user.id.toString().includes(userSearchTerm)
  );

  const selectedUser = discordUsers.find((user: any) => user.id === selectedUserId);

  const handlePointsSubmit = () => {
    if (!selectedUserId || pointAmount === 0 || !pointReason.trim()) {
      setOperationResult({ type: 'error', message: '请填写完整信息' });
      setTimeout(() => setOperationResult(null), 3000);
      return;
    }

    addPointsMutation.mutate({
      userId: selectedUserId,
      amount: pointAmount,
      reason: pointReason.trim()
    });
  };

  const handleDeductPoints = () => {
    if (!selectedUserId || pointAmount === 0 || !pointReason.trim()) {
      setOperationResult({ type: 'error', message: '请填写完整信息' });
      setTimeout(() => setOperationResult(null), 3000);
      return;
    }

    deductPointsMutation.mutate({
      userId: selectedUserId,
      amount: Math.abs(pointAmount), // Ensure positive number for deduction
      reason: pointReason.trim()
    });
  };

  const handleDeleteRecord = async (recordId: number) => {
    if (!confirm('确定要删除这条积分记录吗？\n\n⚠️ 注意：删除后不可恢复！\n此操作只会删除记录，不会影响用户的实际积分。')) {
      return;
    }

    try {
      const response = await fetch(`/api/point-records/${recordId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setOperationResult({ type: 'success', message: '积分记录删除成功！' });
        // Invalidate and refetch queries to update data
        queryClient.invalidateQueries({ queryKey: ['/api/point-records'] });
        queryClient.invalidateQueries({ queryKey: ['/api/points/stats'] });
        queryClient.invalidateQueries({ queryKey: ['/api/discord-users'] });
        
        // Clear result message after 3 seconds
        setTimeout(() => setOperationResult(null), 3000);
      } else {
        setOperationResult({ type: 'error', message: '删除积分记录失败！' });
        setTimeout(() => setOperationResult(null), 3000);
      }
    } catch (error) {
      console.error('Delete record error:', error);
      setOperationResult({ type: 'error', message: '删除积分记录失败！' });
    }

    setTimeout(() => setOperationResult(null), 3000);
  };

  const handleFormSubmit = (type: string) => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setOperationResult({
        type: 'success',
        message: type === 'batch' ? '批量操作完成！' : 
                type === 'template' ? '模板保存成功！' : 
                type === 'settings' ? '积分设置已更新！' : '操作完成！'
      });
      
      setTimeout(() => {
        setOperationResult(null);
        if (type === 'batch') setIsBatchModalOpen(false);
        if (type === 'template') setIsTemplateModalOpen(false);
        if (type === 'settings') setIsSettingsModalOpen(false);
      }, 2000);
    }, 1500);
  };

  const handleExport = () => {
    setIsExportModalOpen(true);
    setExportProgress(0);
    setIsLoading(true);
    
    const interval = setInterval(() => {
      setExportProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsLoading(false);
          return 100;
        }
        return prev + 20;
      });
    }, 300);
  };

  return (
    <div className="space-y-8">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">积分系统</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">管理用户积分奖励和记录</p>
        </div>
        <div className="flex space-x-3">
          <button 
            onClick={() => {
              queryClient.invalidateQueries({ queryKey: ['/api/discord-users'] });
              setOperationResult({ type: 'success', message: '用户数据同步成功！' });
              setTimeout(() => setOperationResult(null), 2000);
            }}
            className="bg-gray-800 hover:bg-gray-900 text-white px-4 py-2 rounded-xl transition-colors flex items-center space-x-2 shadow-lg shadow-gray-900/25"
          >
            <icons.RefreshCw className="w-4 h-4" />
            <span>同步用户</span>
          </button>
          <button 
            onClick={() => setIsModalOpen(true)}
            className="bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-4 py-2 rounded-xl transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-yellow-500/25 font-medium"
          >
            <icons.Plus className="w-4 h-4" />
            <span>发放积分</span>
          </button>
        </div>
      </div>

      {/* 操作结果提示 */}
      {operationResult && (
        <div className={`p-4 rounded-xl border ${
          operationResult.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-400'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-400'
        }`}>
          <div className="flex items-center space-x-2">
            {operationResult.type === 'success' ? (
              <icons.CheckCircle className="w-5 h-5" />
            ) : (
              <icons.AlertCircle className="w-5 h-5" />
            )}
            <span className="font-medium">{operationResult.message}</span>
          </div>
        </div>
      )}

      {/* 网格布局 */}
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧统计区域 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 今日发放统计 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center shadow-lg shadow-green-500/25">
                <icons.TrendingUp className="w-6 h-6 text-white font-bold" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日发放</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{pointStats.todayDistribution}</p>
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full" 
                style={{ width: pointStats.todayDistribution > 0 ? `${Math.min((pointStats.todayDistribution / 100) * 100, 100)}%` : '0%' }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">较昨日增长 {pointStats.todayDistribution}%</p>
          </div>

          {/* 今日扣除统计 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-red-500 rounded-xl flex items-center justify-center shadow-lg shadow-red-500/25">
                <icons.TrendingDown className="w-6 h-6 text-white font-bold" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">今日扣除</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{pointStats.todayDeduction}</p>
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-red-400 to-red-500 h-2 rounded-full" 
                style={{ width: pointStats.todayDeduction > 0 ? `${Math.min((pointStats.todayDeduction / 50) * 100, 100)}%` : '0%' }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">较昨日减少 {pointStats.todayDeduction}%</p>
          </div>

          {/* 总积分池 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Coins className="w-6 h-6 text-black font-bold" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">总积分池</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{pointStats.totalPool?.toLocaleString() || 0}</p>
              </div>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-2 rounded-full" 
                style={{ width: pointStats.totalPool > 0 ? `${Math.min((pointStats.totalPool / 1000) * 100, 100)}%` : '0%' }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">流通积分总量</p>
          </div>

          {/* 积分分布 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">积分分布</h3>
            <div className="space-y-3">
              {(pointStats.pointsDistribution || []).slice(0, 5).map((user: any, index: number) => (
                <div key={user.username} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm ${
                      index === 0 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25' : 
                      index === 1 ? 'bg-gradient-to-br from-gray-400 to-gray-500' : 
                      index === 2 ? 'bg-gradient-to-br from-orange-400 to-orange-500' : 'bg-gradient-to-br from-gray-600 to-gray-700'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{user.username}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{user.points} 积分</div>
                    </div>
                  </div>
                  {user.avatar && (
                    <img src={user.avatar} alt={user.username} className="w-8 h-8 rounded-lg object-cover" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 中间主要内容区域 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          {/* 搜索和筛选 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <icons.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="搜索用户或操作原因..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex space-x-2">
                <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>所有操作</option>
                  <option>发放积分</option>
                  <option>扣除积分</option>
                </select>
                <select className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>今天</option>
                  <option>本周</option>
                  <option>本月</option>
                  <option>全部</option>
                </select>
              </div>
            </div>
          </div>

          {/* 积分记录列表 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">积分记录</h3>
                <span className="text-sm text-gray-500 dark:text-gray-400">共 {filteredRecords.length} 条记录</span>
              </div>
              {recordsLoading ? (
                <div className="flex items-center justify-center p-8">
                  <icons.Loader2 className="h-8 w-8 animate-spin text-yellow-600 mr-3" />
                  <span className="text-gray-600 dark:text-gray-400">加载积分记录...</span>
                </div>
              ) : filteredRecords.length === 0 ? (
                <div className="text-center p-8">
                  <icons.Coins className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无积分记录</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">还没有任何积分操作记录</p>
                  <button
                    onClick={() => setIsModalOpen(true)}
                    className="flex items-center gap-2 mx-auto px-4 py-2 bg-yellow-600 text-white rounded-xl hover:bg-yellow-700 transition-colors"
                  >
                    <icons.Plus className="h-4 w-4" />
                    发放积分
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredRecords.map((record: any) => (
                    <div key={record.id} className="border border-gray-200 dark:border-gray-600 rounded-xl p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="relative">
                            {record.avatar ? (
                              <img src={record.avatar} alt={record.username} className="w-12 h-12 rounded-xl object-cover" />
                            ) : (
                              <div className="w-12 h-12 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center shadow-lg">
                                <span className="text-white font-bold text-sm">
                                  {record.username?.charAt(0) || 'U'}
                                </span>
                              </div>
                            )}
                            <div className={`absolute -bottom-1 -right-1 w-6 h-6 ${
                              record.amount > 0 ? 'bg-green-500' : 'bg-red-500'
                            } rounded-full flex items-center justify-center shadow-lg`}>
                              {record.amount > 0 ? (
                                <icons.Plus className="w-3 h-3 text-white" />
                              ) : (
                                <icons.Minus className="w-3 h-3 text-white" />
                              )}
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-1">
                              <div className="font-bold text-gray-900 dark:text-white">{record.username}</div>
                              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-bold ${
                                record.amount > 0 
                                  ? 'bg-gradient-to-r from-green-400 to-green-500 text-white shadow-lg shadow-green-500/25'
                                  : 'bg-gradient-to-r from-red-400 to-red-500 text-white shadow-lg shadow-red-500/25'
                              }`}>
                                {record.amount > 0 ? '+' : ''}{record.amount} 积分
                              </span>
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                              <span className="font-medium">原因：</span>{record.reason}
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                              操作前积分: {record.oldPoints || 0} | 操作后积分: {record.newPoints || 0}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-500">
                              {record.createdAt ? new Date(record.createdAt).toLocaleString('zh-CN') : 'N/A'}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <button 
                            onClick={() => handleDeleteRecord(record.id)}
                            className="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors p-2 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20" 
                            title="删除记录"
                          >
                            <icons.Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 右侧操作面板 */}
        <div className="col-span-12 lg:col-span-3 space-y-6">
          {/* 快捷操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">快捷操作</h3>
            <div className="space-y-3">
              <button 
                onClick={() => setIsTemplateModalOpen(true)}
                className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-3 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-blue-500/25"
              >
                <icons.FileText className="w-4 h-4" />
                <span>积分模板</span>
              </button>
              <button 
                onClick={() => setIsBatchModalOpen(true)}
                className="w-full bg-gradient-to-r from-purple-500 to-purple-600 text-white px-4 py-3 rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-purple-500/25"
              >
                <icons.Users className="w-4 h-4" />
                <span>批量操作</span>
              </button>
              <button 
                onClick={handleExport}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-3 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-green-500/25"
              >
                <icons.Download className="w-4 h-4" />
                <span>导出记录</span>
              </button>
              <button 
                onClick={() => setIsSettingsModalOpen(true)}
                className="w-full bg-gradient-to-r from-gray-500 to-gray-600 text-white px-4 py-3 rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-gray-500/25"
              >
                <icons.Settings className="w-4 h-4" />
                <span>积分设置</span>
              </button>
            </div>
          </div>

          {/* 积分排行榜 */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-xl flex items-center justify-center shadow-lg shadow-yellow-500/25">
                <icons.Trophy className="w-5 h-5 text-black font-bold" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">积分排行</h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">实时排名</p>
              </div>
            </div>
            
            <div className="space-y-3">
              {(pointStats.pointsDistribution || []).slice(0, 5).map((user: any, index: number) => (
                <div key={user.username} className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold text-sm ${
                      index === 0 ? 'bg-gradient-to-br from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25' : 
                      index === 1 ? 'bg-gradient-to-br from-gray-400 to-gray-500' : 
                      index === 2 ? 'bg-gradient-to-br from-orange-400 to-orange-500' : 'bg-gradient-to-br from-gray-600 to-gray-700'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900 dark:text-white text-sm">{user.username}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{user.points} 积分</div>
                    </div>
                  </div>
                  {user.avatar && (
                    <img src={user.avatar} alt={user.username} className="w-8 h-8 rounded-lg object-cover" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 发放积分模态框 */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-2xl mx-4 shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">积分管理</h3>
              <button 
                onClick={() => {
                  setIsModalOpen(false);
                  setSelectedUserId(null);
                  setPointAmount(0);
                  setPointReason('');
                  setIsDeductMode(false);
                  setUserPointRecords([]);
                  setUserSearchTerm('');
                  setShowUserDropdown(false);
                }}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <icons.X className="w-5 h-5" />
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 左侧：积分操作 */}
              <div className="space-y-4">
                <div className="flex space-x-2 mb-4">
                  <button
                    onClick={() => setIsDeductMode(false)}
                    className={`flex-1 px-4 py-2 rounded-xl font-medium transition-all ${
                      !isDeductMode 
                        ? 'bg-gradient-to-r from-yellow-400 to-yellow-500 text-black shadow-lg shadow-yellow-500/25' 
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    }`}
                  >
                    <icons.Plus className="w-4 h-4 inline mr-2" />
                    发放积分
                  </button>
                  <button
                    onClick={() => setIsDeductMode(true)}
                    className={`flex-1 px-4 py-2 rounded-xl font-medium transition-all ${
                      isDeductMode 
                        ? 'bg-gradient-to-r from-red-400 to-red-500 text-white shadow-lg shadow-red-500/25' 
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    }`}
                  >
                    <icons.Minus className="w-4 h-4 inline mr-2" />
                    扣除积分
                  </button>
                </div>

                <div className="relative">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">选择用户</label>
                  <div className="relative">
                    <input
                      type="text"
                      value={selectedUser ? `${selectedUser.username} (${selectedUser.points} 积分)` : userSearchTerm}
                      onChange={(e) => {
                        if (!selectedUser) {
                          setUserSearchTerm(e.target.value);
                        }
                      }}
                      onFocus={() => setShowUserDropdown(true)}
                      onBlur={() => {
                        // 延迟关闭，以便点击下拉选项时能正常工作
                        setTimeout(() => setShowUserDropdown(false), 200);
                      }}
                      placeholder="搜索用户名或ID..."
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white pr-10"
                    />
                    {selectedUser && (
                      <button
                        onClick={() => {
                          setSelectedUserId(null);
                          setUserSearchTerm('');
                          setUserPointRecords([]);
                        }}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <icons.X className="w-4 h-4" />
                      </button>
                    )}
                    {!selectedUser && (
                      <icons.Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    )}
                  </div>
                  
                  {showUserDropdown && !selectedUser && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-xl shadow-lg max-h-60 overflow-y-auto">
                      {filteredUsers.length > 0 ? (
                        filteredUsers.map((user: any) => (
                          <div
                            key={user.id}
                            onClick={() => {
                              handleUserSelect(user.id);
                              setUserSearchTerm('');
                              setShowUserDropdown(false);
                            }}
                            className="px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                {user.avatar && (
                                  <img src={user.avatar} alt={user.username} className="w-8 h-8 rounded-lg object-cover" />
                                )}
                                <div>
                                  <div className="font-medium text-gray-900 dark:text-white">{user.username}</div>
                                  <div className="text-sm text-gray-500 dark:text-gray-400">ID: {user.id}</div>
                                </div>
                              </div>
                              <div className="text-right">
                                <div className="font-medium text-gray-900 dark:text-white">{user.points} 积分</div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {user.roles && user.roles.length > 0 ? user.roles.join(', ') : '无角色'}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="px-4 py-3 text-gray-500 dark:text-gray-400 text-center">
                          未找到匹配的用户
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {!isDeductMode ? (
                  // 发放积分表单
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">积分数量</label>
                      <input
                        type="number"
                        value={pointAmount}
                        onChange={(e) => setPointAmount(Number(e.target.value))}
                        placeholder="输入积分数量（正数发放，负数扣除）"
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">发放原因</label>
                      <textarea
                        rows={3}
                        value={pointReason}
                        onChange={(e) => setPointReason(e.target.value)}
                        placeholder="请输入发放积分的原因..."
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">通知模板</label>
                      <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option>默认模板</option>
                        <option>签到奖励模板</option>
                        <option>活动奖励模板</option>
                        <option>自定义模板</option>
                      </select>
                    </div>
                    <div className="flex justify-end space-x-3 pt-4">
                      <button
                        type="button"
                        onClick={() => {
                          setIsModalOpen(false);
                          setSelectedUserId(null);
                          setPointAmount(0);
                          setPointReason('');
                          setUserPointRecords([]);
                        }}
                        className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        取消
                      </button>
                      <button
                        onClick={handlePointsSubmit}
                        disabled={addPointsMutation.isPending}
                        className="px-6 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg shadow-yellow-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                      >
                        {addPointsMutation.isPending && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                        <span>{addPointsMutation.isPending ? '发放中...' : '发放积分'}</span>
                      </button>
                    </div>
                  </>
                ) : (
                  // 扣除积分表单
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">扣除积分数量</label>
                      <input
                        type="number"
                        value={pointAmount}
                        onChange={(e) => setPointAmount(Number(e.target.value))}
                        placeholder="输入要扣除的积分数量（正数）"
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">扣除原因</label>
                      <textarea
                        rows={3}
                        value={pointReason}
                        onChange={(e) => setPointReason(e.target.value)}
                        placeholder="请输入扣除积分的原因..."
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">通知模板</label>
                      <select className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-red-500 focus:border-red-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option>违规扣分模板</option>
                        <option>违反规则模板</option>
                        <option>恶意行为模板</option>
                        <option>自定义模板</option>
                      </select>
                    </div>
                    <div className="flex justify-end space-x-3 pt-4">
                      <button
                        type="button"
                        onClick={() => {
                          setIsModalOpen(false);
                          setSelectedUserId(null);
                          setPointAmount(0);
                          setPointReason('');
                          setUserPointRecords([]);
                        }}
                        className="px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        取消
                      </button>
                      <button
                        onClick={handleDeductPoints}
                        disabled={deductPointsMutation.isPending}
                        className="px-6 py-3 bg-gradient-to-r from-red-400 to-red-500 text-white rounded-xl hover:from-red-500 hover:to-red-600 transition-all duration-200 shadow-lg shadow-red-500/25 font-medium disabled:opacity-50 flex items-center space-x-2"
                      >
                        {deductPointsMutation.isPending && <icons.Loader2 className="w-4 h-4 animate-spin" />}
                        <span>{deductPointsMutation.isPending ? '扣除中...' : '扣除积分'}</span>
                      </button>
                    </div>
                  </>
                )}
              </div>

              {/* 右侧：用户积分记录 */}
              <div className="border-l border-gray-200 dark:border-gray-600 pl-6">
                <h4 className="text-md font-bold text-gray-900 dark:text-white mb-4">用户积分记录</h4>
                {!selectedUserId ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <icons.User className="w-8 h-8 text-white" />
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">请先选择用户查看积分记录</p>
                  </div>
                ) : userPointRecords.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-full flex items-center justify-center mx-auto mb-4">
                      <icons.Coins className="w-8 h-8 text-white" />
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">该用户暂无积分记录</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-80 overflow-y-auto">
                    {userPointRecords.map((record: any) => (
                      <div key={record.id} className="border border-gray-200 dark:border-gray-600 rounded-xl p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-bold ${
                                record.amount > 0 
                                  ? 'bg-gradient-to-r from-green-400 to-green-500 text-white'
                                  : 'bg-gradient-to-r from-red-400 to-red-500 text-white'
                              }`}>
                                {record.amount > 0 ? '+' : ''}{record.amount}
                              </span>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {record.createdAt ? new Date(record.createdAt).toLocaleString('zh-CN') : 'N/A'}
                              </span>
                            </div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                              {record.reason}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 其他模态框保持原有样式... */}
      {/* 积分模板模态框 */}
      {isTemplateModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">积分模板</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">模板名称</label>
                <input
                  type="text"
                  placeholder="例如：每日签到奖励"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">积分数量</label>
                <input
                  type="number"
                  placeholder="50"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">模板描述</label>
                <textarea
                  rows={3}
                  placeholder="用于每日签到的积分奖励..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => handleFormSubmit('template')}
                disabled={isLoading}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoading ? '保存中...' : '保存模板'}
              </button>
              <button
                onClick={() => setIsTemplateModalOpen(false)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 批量操作模态框 */}
      {isBatchModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">批量操作</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">操作类型</label>
                <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>批量发放积分</option>
                  <option>批量扣除积分</option>
                  <option>重置用户积分</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">目标用户</label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2 text-purple-600 focus:ring-purple-500" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">所有用户</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2 text-purple-600 focus:ring-purple-500" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">活跃用户</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="mr-2 text-purple-600 focus:ring-purple-500" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">新用户</span>
                  </label>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">积分数量</label>
                <input
                  type="number"
                  placeholder="100"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => handleFormSubmit('batch')}
                disabled={isLoading}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoading ? '执行中...' : '执行操作'}
              </button>
              <button
                onClick={() => setIsBatchModalOpen(false)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 导出记录模态框 */}
      {isExportModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">导出积分记录</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">导出格式</label>
                <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>Excel 格式 (.xlsx)</option>
                  <option>CSV 格式 (.csv)</option>
                  <option>PDF 格式 (.pdf)</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">时间范围</label>
                <select className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option>今天</option>
                  <option>本周</option>
                  <option>本月</option>
                  <option>全部记录</option>
                </select>
              </div>
              {isLoading && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">导出进度</span>
                    <span className="font-medium text-gray-900 dark:text-white">{exportProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-green-400 to-green-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${exportProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setIsExportModalOpen(false)}
                disabled={isLoading}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoading ? '导出中...' : '开始导出'}
              </button>
              <button
                onClick={() => setIsExportModalOpen(false)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 积分设置模态框 */}
      {isSettingsModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-md mx-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">积分设置</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">单次发放上限</label>
                <input
                  type="number"
                  defaultValue="1000"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">每日发放上限</label>
                <input
                  type="number"
                  defaultValue="5000"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">积分兑换比例</label>
                <input
                  type="text"
                  defaultValue="1:1"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input type="checkbox" defaultChecked className="mr-2 text-gray-600 focus:ring-gray-500" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">启用积分过期机制</span>
                </label>
                <label className="flex items-center">
                  <input type="checkbox" defaultChecked className="mr-2 text-gray-600 focus:ring-gray-500" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">发送积分变动通知</span>
                </label>
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => handleFormSubmit('settings')}
                disabled={isLoading}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                {isLoading ? '保存中...' : '保存设置'}
              </button>
              <button
                onClick={() => setIsSettingsModalOpen(false)}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PointSystem;