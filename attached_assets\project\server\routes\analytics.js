import express from 'express';
import { db } from '../database/init.js';
import { logger } from '../utils/logger.js';

const router = express.Router();

// Get analytics data
router.get('/', async (req, res) => {
  try {
    const { timeRange = '7d' } = req.query;
    
    // Calculate date range
    let dateFilter = '';
    switch (timeRange) {
      case '24h':
        dateFilter = "datetime('now', '-1 day')";
        break;
      case '7d':
        dateFilter = "datetime('now', '-7 days')";
        break;
      case '30d':
        dateFilter = "datetime('now', '-30 days')";
        break;
      case '90d':
        dateFilter = "datetime('now', '-90 days')";
        break;
      default:
        dateFilter = "datetime('now', '-7 days')";
    }

    // Get user statistics
    const userStats = await db.getAsync(`
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN last_active > ${dateFilter} THEN 1 END) as active_users,
        AVG(points) as avg_points,
        SUM(message_count) as total_messages
      FROM users
    `);

    // Get checkin statistics
    const checkinStats = await db.getAsync(`
      SELECT 
        COUNT(*) as total_checkins,
        COUNT(DISTINCT user_id) as unique_checkins,
        AVG(points_earned) as avg_points_per_checkin
      FROM checkin_records 
      WHERE created_at > ${dateFilter}
    `);

    // Get daily activity
    const dailyActivity = await db.allAsync(`
      SELECT 
        date(created_at) as date,
        COUNT(*) as checkins
      FROM checkin_records 
      WHERE created_at > ${dateFilter}
      GROUP BY date(created_at)
      ORDER BY date
    `);

    res.json({
      success: true,
      data: {
        userStats,
        checkinStats,
        dailyActivity,
        timeRange
      }
    });
  } catch (error) {
    logger.error('Error getting analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get analytics data'
    });
  }
});

export default router;