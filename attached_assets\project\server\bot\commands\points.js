import { SlashCommandBuilder } from 'discord.js';
import { db } from '../../database/init.js';
import { logger } from '../../utils/logger.js';

export const pointsCommand = {
  data: new SlashCommandBuilder()
    .setName('积分')
    .setDescription('查看个人积分信息')
    .addUserOption(option =>
      option.setName('用户')
        .setDescription('要查看的用户（可选）')
        .setRequired(false)
    ),

  async execute(interaction) {
    try {
      const targetUser = interaction.options.getUser('用户') || interaction.user;
      
      const user = await db.getAsync(`
        SELECT points, title, checkin_streak, message_count, join_date 
        FROM users WHERE id = ?
      `, [targetUser.id]);

      if (!user) {
        await interaction.reply({
          content: '❌ 未找到用户信息。',
          ephemeral: true
        });
        return;
      }

      // Get user rank
      const rankResult = await db.getAsync(`
        SELECT COUNT(*) + 1 as rank 
        FROM users 
        WHERE points > ?
      `, [user.points]);

      const rank = rankResult?.rank || 1;

      await interaction.reply({
        embeds: [{
          title: `💰 ${targetUser.username} 的积分信息`,
          fields: [
            { name: '当前积分', value: user.points.toString(), inline: true },
            { name: '当前头衔', value: user.title, inline: true },
            { name: '排名', value: `第 ${rank} 名`, inline: true },
            { name: '连续签到', value: `${user.checkin_streak} 天`, inline: true },
            { name: '消息数量', value: user.message_count.toString(), inline: true },
            { name: '加入时间', value: new Date(user.join_date).toLocaleDateString('zh-CN'), inline: true }
          ],
          color: 0xD6F36F,
          thumbnail: {
            url: targetUser.displayAvatarURL()
          },
          timestamp: new Date().toISOString()
        }]
      });
    } catch (error) {
      logger.error('Error in points command:', error);
      await interaction.reply({
        content: '❌ 查询积分失败，请稍后重试。',
        ephemeral: true
      });
    }
  }
};